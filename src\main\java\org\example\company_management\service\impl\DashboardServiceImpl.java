package org.example.company_management.service.impl;

import org.example.company_management.mapper.DashboardMapper;
import org.example.company_management.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 仪表盘服务实现类
 */
@Service
public class DashboardServiceImpl implements DashboardService {
    
    @Autowired
    private DashboardMapper dashboardMapper;
    
    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 员工统计
        int totalEmployees = dashboardMapper.countTotalEmployees();
        int activeEmployees = dashboardMapper.countActiveEmployees();
        int inactiveEmployees = totalEmployees - activeEmployees;
        
        // 部门和职位统计
        int totalDepartments = dashboardMapper.countTotalDepartments();
        int totalPositions = dashboardMapper.countTotalPositions();
        
        // 业绩统计
        double totalPerformance = dashboardMapper.sumTotalPerformance();
        double averagePerformance = (activeEmployees > 0) ? 
                totalPerformance / activeEmployees : 0;
        
        // 构建返回数据
        Map<String, Object> employeeStats = new HashMap<>();
        employeeStats.put("total", totalEmployees);
        employeeStats.put("active", activeEmployees);
        employeeStats.put("inactive", inactiveEmployees);
        
        Map<String, Object> performanceStats = new HashMap<>();
        performanceStats.put("total", totalPerformance);
        performanceStats.put("average", averagePerformance);
        
        stats.put("employees", employeeStats);
        stats.put("departments", totalDepartments);
        stats.put("positions", totalPositions);
        stats.put("performance", performanceStats);
        
        return stats;
    }
} 
package org.example.company_management.controller;

import org.example.company_management.service.DashboardService;
import org.example.company_management.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 仪表盘控制器
 * <p>
 * 提供系统首页的数据统计信息
 * </p>
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    /**
     * 获取首页统计数据
     * <p>
     * 包括员工总数、在职/离职数量、部门数量、职位数量、总业绩等信息
     * </p>
     *
     * @return 统计数据集合
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        Map<String, Object> stats = dashboardService.getDashboardStats();
        return Result.success(stats);
    }
} 
import request from './request'

/**
 * 员工登录
 * @param {Object} data - 登录信息
 * @returns {Promise}
 */
export function employeeLogin(data) {
	return request({
		url: '/auth/employee/login',
		method: 'post',
		data,
	})
}

/**
 * 获取员工信息
 * @returns {Promise}
 */
export function getEmployeeInfo() {
	return request({
		url: '/auth/info',
		method: 'get',
	})
}

/**
 * 更新员工个人资料
 * @param {Object} data - 个人资料信息
 * @returns {Promise}
 */
export function updateProfile(data) {
	return request({
		url: '/auth/profile',
		method: 'put',
		data,
	})
}

/**
 * 登出
 * @returns {Promise}
 */
export function logout() {
	return request({
		url: '/auth/logout',
		method: 'post',
	})
}

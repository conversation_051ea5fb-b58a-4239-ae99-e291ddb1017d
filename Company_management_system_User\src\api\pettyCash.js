import request from './request'

/**
 * 分页查询当前登录用户的备用金申请（从ThreadLocal获取用户信息）
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.purpose] 用途(可选)
 * @param {string} [params.status] 状态(可选)
 * @param {string} [params.date] 年月(可选, YYYY-MM格式)
 * @returns {Promise<any>}
 */
export function fetchMyPettyCash(params) {
	return request({
		url: '/petty-cash/my/page',
		method: 'get',
		params: {
			page: params.pageNum,
			size: params.pageSize,
			purpose: params.purpose,
			status: params.status,
			date: params.date
		},
	})
}

/**
 * 申请新备用金
 * @param {Object} data 备用金信息
 * @returns {Promise<any>}
 */
export function applyPettyCash(data) {
	return request({
		url: '/petty-cash',
		method: 'post',
		data,
	});
}

/**
 * 根据ID获取备用金详情
 * @param {number} id 备用金ID
 * @returns {Promise<any>}
 */
export function getPettyCashById(id) {
	return request({
		url: `/petty-cash/${id}`,
		method: 'get'
	});
}

/**
 * 获取待审批的备用金数量（管理员和部门负责人可用）
 * @returns {Promise<any>}
 */
export function getPendingCount() {
	return request({
		url: '/petty-cash/pending-count',
		method: 'get'
	});
}

/**
 * 更新备用金信息（仅当是当前用户的待审核记录）
 * @param {number} id 备用金ID
 * @param {Object} data 备用金更新信息
 * @returns {Promise<any>}
 */
export function updatePettyCash(id, data) {
	return request({
		url: `/petty-cash/my/${id}`,
		method: 'put',
		data,
	});
}

/**
 * 提交审核（将状态从待审核改为审核中）
 * @param {number} id 备用金ID
 * @returns {Promise<any>}
 */
export function submitToApproval(id) {
	return request({
		url: `/petty-cash/submit-to-approval/${id}`,
		method: 'put',
	});
}

/**
 * 取消申请（将状态改为已取消）
 * @param {number} id 备用金ID
 * @returns {Promise<any>}
 */
export function cancelPettyCash(id) {
	return request({
		url: `/petty-cash/cancel/${id}`,
		method: 'put',
	});
}

/**
 * 获取指定部门的备用金列表（分页）
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.size 每页大小
 * @param {Array<number>} params.departmentIds 部门ID列表
 * @param {string} [params.purpose] 用途 (可选)
 * @param {string} [params.status] 审批状态 (可选)
 * @param {string} [params.date] 年月 (可选, YYYY-MM格式)
 * @returns {Promise<any>}
 */
export function getDepartmentPettyCash(params) {
	return request({
		url: '/petty-cash/department-records',
		method: 'post',
		data: {
			page: params.page,
			size: params.size,
			departmentIds: params.departmentIds,
			purpose: params.purpose,
			status: params.status,
			date: params.date
		}
	});
} 
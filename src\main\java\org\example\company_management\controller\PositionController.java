package org.example.company_management.controller;

import org.example.company_management.entity.Position;
import org.example.company_management.service.PositionService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 职位管理控制器
 * <p>
 * 提供职位的添加、修改、删除和查询功能。
 * 职位是公司的基本组织单位，每个员工都归属于某个职位。
 * 职位信息包含职位ID、职位名称和职位描述。
 * 一个职位可以关联多个部门。
 * </p>
 */
@RestController
@RequestMapping("/position")
public class PositionController {

    @Autowired
    private PositionService positionService;

    /**
     * 查询所有职位
     * <p>
     * 获取系统中所有职位的列表。
     * 用于职位选择下拉框和职位管理页面。
     * 返回的每个职位都包含完整的职位信息及其关联的部门。
     * </p>
     *
     * @return 职位列表
     */
    @GetMapping("/list")
    public Result<List<Position>> list() {
        try {
            List<Position> positions = positionService.list();
            return Result.success(positions);
        } catch (Exception e) {
            return Result.error("获取职位列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询职位
     * <p>
     * 分页获取系统中的职位列表。
     * 用于职位管理页面。
     * </p>
     *
     * @param pageNum      页码，默认为1
     * @param pageSize     每页大小，默认为10
     * @param positionName 职位名称(可选)
     * @param departmentId 部门ID(可选)
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult<Position>> page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String positionName,
            @RequestParam(required = false) Integer departmentId) {
        try {
            PageResult<Position> pageResult = positionService.pageList(pageNum, pageSize, positionName, departmentId);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("获取职位分页数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询职位
     * <p>
     * 获取指定ID的职位详细信息，包括其关联的部门。
     * 用于职位编辑前的数据获取。
     * </p>
     *
     * @param id 职位ID
     * @return 职位信息
     */
    @GetMapping("/{id}")
    public Result<Position> getById(@PathVariable("id") Integer id) {
        try {
            Position position = positionService.getById(id);
            if (position == null) {
                return Result.error("职位不存在");
            }
            
            // 获取职位关联的部门ID列表
            List<Integer> departmentIds = positionService.getPositionDepartmentIds(id);
            position.setDepartmentIds(departmentIds);
            
            // 获取职位关联的部门名称列表
            List<String> departmentNames = positionService.getPositionDepartmentNames(id);
            position.setDepartmentNames(departmentNames);
            
            return Result.success(position);
        } catch (Exception e) {
            return Result.error("获取职位信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据部门ID查询职位
     * <p>
     * 获取指定部门下的所有职位列表。
     * 用于员工管理时选择特定部门下的职位。
     * </p>
     *
     * @param departmentId 部门ID
     * @return 职位列表
     */
    @GetMapping("/department/{departmentId}")
    public Result<List<Position>> getByDepartmentId(@PathVariable("departmentId") Integer departmentId) {
        try {
            List<Position> positions = positionService.getByDepartmentId(departmentId);
            return Result.success(positions);
        } catch (Exception e) {
            return Result.error("获取部门职位列表失败: " + e.getMessage());
        }
    }

    /**
     * 新增职位
     * <p>
     * 添加一个新的职位到系统中。
     * 职位名称不能为空，且不能与现有职位重名。
     * 职位可以关联多个部门。
     * </p>
     *
     * @param position 职位信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody Position position) {
        try {
            positionService.add(position);
            return Result.success();
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("添加职位失败: " + e.getMessage());
        }
    }

    /**
     * 修改职位
     * <p>
     * 更新现有职位的信息。
     * 职位ID必须存在，且职位名称不能为空。
     * 如果修改职位名称，新名称不能与其他职位重名。
     * 可以更新职位关联的部门。
     * </p>
     *
     * @param position 职位信息
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Void> update(@RequestBody Position position) {
        try {
            positionService.update(position);
            return Result.success();
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新职位失败: " + e.getMessage());
        }
    }

    /**
     * 删除职位
     * <p>
     * 从系统中删除指定ID的职位。
     * 如果职位下有员工，会拒绝删除并返回错误信息。
     * 删除职位时，职位与部门的关联也会被删除。
     * </p>
     *
     * @param id 职位ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable("id") Integer id) {
        try {
            Position position = positionService.getById(id);
            if (position == null) {
                return Result.error("职位不存在");
            }

            // 检查职位下是否有员工
            int employeeCount = positionService.checkPositionHasEmployees(id);
            if (employeeCount > 0) {
                return Result.error("该职位下有" + employeeCount + "名员工，请先调整这些员工的职位后再删除");
            }

            positionService.delete(id);
            return Result.success("职位已删除");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("删除职位失败: " + e.getMessage());
        }
    }

    /**
     * 更新职位状态
     * <p>
     * 更新职位的状态（启用/禁用）。
     * </p>
     *
     * @param id     职位ID
     * @param status 状态
     * @return 操作结果
     */
    @PutMapping("/status")
    public Result<Void> updateStatus(@RequestParam Integer id, @RequestParam String status) {
        try {
            // 检查状态值是否有效
            if (!"Active".equals(status) && !"Inactive".equals(status)) {
                return Result.error("无效的状态值，只能是Active或Inactive");
            }

            Position position = positionService.getById(id);
            if (position == null) {
                return Result.error("职位不存在");
            }

            position.setStatus(status);
            positionService.update(position);
            return Result.success();
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新职位状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取职位关联的部门ID列表
     * <p>
     * 获取指定职位关联的所有部门ID。
     * </p>
     *
     * @param id 职位ID
     * @return 部门ID列表
     */
    @GetMapping("/{id}/departments")
    public Result<List<Integer>> getPositionDepartments(@PathVariable("id") Integer id) {
        try {
            List<Integer> departmentIds = positionService.getPositionDepartmentIds(id);
            return Result.success(departmentIds);
        } catch (Exception e) {
            return Result.error("获取职位关联部门失败: " + e.getMessage());
        }
    }
}

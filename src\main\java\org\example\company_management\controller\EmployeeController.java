package org.example.company_management.controller;

import org.example.company_management.dto.EmployeeDTO;
import org.example.company_management.entity.Employee;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.utils.DTOConverter;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工管理控制器
 * <p>
 * 提供员工的添加、修改、删除和查询功能。
 * 员工是公司的基本人员单位，每个员工都归属于某个部门。
 * 员工信息包含员工ID、姓名、邮箱、入职时间、部门等信息。
 * </p>
 */
@RestController
@RequestMapping("/employee")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    /**
     * 查询所有员工
     * <p>
     * 获取系统中所有员工的列表。
     * 用于员工选择下拉框和员工管理页面。
     * 返回的每个员工都包含完整的员工信息。
     * </p>
     *
     * @return 员工列表
     */
    @GetMapping("/list")
    public Result<List<Employee>> list() {
        List<Employee> employees = employeeService.list();
        return Result.success(employees);
    }

    /**
     * 分页查询员工
     * <p>
     * 分页获取系统中的员工列表。
     * 用于员工管理页面。
     * </p>
     *
     * @param pageNum      页码，默认为1
     * @param pageSize     每页大小，默认为10
     * @param name         员工姓名（可选）
     * @param departmentId 部门ID（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult<Employee>> page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer departmentId) {
        PageResult<Employee> pageResult = employeeService.pageList(pageNum, pageSize, name, departmentId);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询员工
     * <p>
     * 获取指定ID的员工详细信息。
     * 用于员工编辑前的数据获取。
     * </p>
     *
     * @param id 员工ID
     * @return 员工信息
     */
    @GetMapping("/{id}")
    public Result<Employee> getById(@PathVariable("id") Integer id) {
        Employee employee = employeeService.getById(id);
        return Result.success(employee);
    }

    /**
     * 根据部门ID查询员工
     * <p>
     * 获取指定部门ID的所有员工列表。
     * 用于部门详情页面展示部门员工。
     * </p>
     *
     * @param departmentId 部门ID
     * @return 员工列表
     */
    @GetMapping("/department/{departmentId}")
    public Result<List<Employee>> getByDepartmentId(@PathVariable("departmentId") Integer departmentId) {
        List<Employee> employees = employeeService.getByDepartmentId(departmentId);
        return Result.success(employees);
    }

    /**
     * 根据部门ID分页查询员工
     * <p>
     * 分页获取指定部门ID的员工列表。
     * 用于部门详情页面展示部门员工。
     * </p>
     *
     * @param departmentId 部门ID
     * @param pageNum      页码，默认为1
     * @param pageSize     每页大小，默认为10
     * @param name         员工姓名（可选）
     * @return 分页结果
     */
    @GetMapping("/department/{departmentId}/page")
    public Result<PageResult<Employee>> getByDepartmentIdWithPage(
            @PathVariable("departmentId") Integer departmentId,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String name) {
        PageResult<Employee> pageResult = employeeService.pageListByDepartmentId(pageNum, pageSize, departmentId, name);
        return Result.success(pageResult);
    }

    /**
     * 新增员工
     * <p>
     * 添加一个新的员工到系统中。
     * 员工姓名和邮箱不能为空，且邮箱不能与现有员工重复。
     * 密码必须至少8位，包含大小写字母和数字。
     * </p>
     *
     * @param employeeDTO 员工DTO
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> add(@Validated(ValidationGroups.Add.class) @RequestBody EmployeeDTO employeeDTO) {
        // 转换DTO为实体
        Employee employee = DTOConverter.convertToEmployee(employeeDTO);
        employeeService.add(employee);
        return Result.success();
    }

    /**
     * 修改员工
     * <p>
     * 更新现有员工的信息。
     * 员工ID必须存在，且员工姓名和邮箱不能为空。
     * 如果修改邮箱，新邮箱不能与其他员工重复。
     * 如果修改密码，新密码必须符合复杂度要求。
     * </p>
     *
     * @param employeeDTO 员工DTO
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Void> update(@Validated(ValidationGroups.Update.class) @RequestBody EmployeeDTO employeeDTO) {
        // 如果密码为空字符串，设置为null避免验证
        if (employeeDTO.getPassword() != null && employeeDTO.getPassword().trim().isEmpty()) {
            employeeDTO.setPassword(null);
        }

        // 转换DTO为实体
        Employee employee = DTOConverter.convertToEmployee(employeeDTO);
        employeeService.update(employee);
        return Result.success();
    }

    /**
     * 删除员工
     * <p>
     * 从系统中删除指定ID的员工。
     * </p>
     *
     * @param id 员工ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Integer id) {
        employeeService.delete(id);
        return Result.success();
    }

    /**
     * 更新员工状态
     * <p>
     * 更新员工的状态（在职/离职）。
     * </p>
     *
     * @param id     员工ID
     * @param status 状态
     * @return 操作结果
     */
    @PutMapping("/status")
    public Result<Void> updateStatus(@RequestParam Integer id, @RequestParam String status) {
        employeeService.updateStatus(id, status);
        return Result.success();
    }
} 
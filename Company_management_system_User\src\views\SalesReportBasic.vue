<template>
    <div class="department-content-container">
        <!-- 头部操作栏 -->
        <div class="toolbar">
                <div class="search-box">
                    <el-date-picker
                        v-model="searchForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        @change="handleSearch"
                    />
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="handleReset">重置</el-button>
                </div>
                
                <el-button
                    type="success"
                    @click="openReportDialog"
                    class="add-btn"
                >
                    <el-icon><Plus /></el-icon>
                    提交今日日报
                </el-button>
            </div>

            <el-table
                v-loading="loading"
                :data="reportList"
                border
                row-key="id"
                :max-height="'calc(100vh - 220px)'"
                class="custom-table"
                :header-cell-style="{ background: '#f7f7f7', color: '#606266' }"
            >
                <el-table-column type="index" width="60" align="center" label="序号" />
                <el-table-column prop="reportDate" label="日报日期" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag type="info">
                            {{ formatDate(row.reportDate) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="客户统计" width="180" align="center">
                    <template #default="{ row }">
                        <div class="client-stats">
                            <div class="stat-row">
                                <span class="label">年度:</span>
                                <span class="value">{{ row.yearlyNewClients || 0 }}</span>
                                <span class="label">月度:</span>
                                <span class="value">{{ row.monthlyNewClients || 0 }}</span>
                            </div>
                            <div class="stat-row">
                                <span class="label">距上次:</span>
                                <span class="value">{{ row.daysSinceLastNewClient || 0 }}天</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="responsibilityLevel" label="责任心评级" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getResponsibilityTagType(row.responsibilityLevel)">
                            {{ row.responsibilityLevel }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="dailyResults" label="今日效果" min-width="200" show-overflow-tooltip />
                <el-table-column prop="managerEvaluation" label="领导评价" min-width="150" show-overflow-tooltip>
                    <template #default="{ row }">
                        <span v-if="row.managerEvaluation" class="evaluation-text">
                            {{ row.managerEvaluation }}
                        </span>
                        <span v-else class="no-evaluation">暂无评价</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="提交时间" width="180" align="center">
                    <template #default="{ row }">
                        {{ formatDateTimeWithYear(row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="100" align="center">
                    <template #default="{ row }">
                        <el-button
                            type="primary"
                            size="small"
                            @click="viewReport(row)"
                        >
                            查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <div class="pagination-container">
                <el-pagination
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    v-model:currentPage="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    :page-sizes="[10, 20, 50, 100]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
        </div>

        <!-- 日报提交对话框 -->
        <el-dialog
            v-model="reportDialogVisible"
            title="提交今日日报"
            width="800px"
            destroy-on-close
            :close-on-click-modal="false"
        >
            <el-form
                ref="reportFormRef"
                :model="reportForm"
                :rules="reportFormRules"
                label-width="140px"
            >
                <!-- 客户统计信息（只读显示） -->
                <div class="statistics-section">
                    <h3>客户统计信息</h3>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="年度新客户总数">
                                <el-input v-model="clientStatistics.yearlyNewClients" readonly>
                                    <template #suffix>个</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="当月新客户总数">
                                <el-input v-model="clientStatistics.monthlyNewClients" readonly>
                                    <template #suffix>个</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="距上次新客户天数">
                                <el-input v-model="clientStatistics.daysSinceLastNewClient" readonly>
                                    <template #suffix>天</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 客户选择 -->
                <div class="client-selection-section">
                    <h3>客户选择</h3>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="询价客户" prop="inquiryClients">
                                <el-select
                                    v-model="reportForm.inquiryClients"
                                    multiple
                                    filterable
                                    remote
                                    reserve-keyword
                                    placeholder="请输入客户名称搜索"
                                    style="width: 100%"
                                    :loading="clientsLoading"
                                    :remote-method="searchClients"
                                    :max-collapse-tags="2"
                                    collapse-tags
                                    collapse-tags-tooltip
                                >
                                    <el-option
                                        v-for="client in myClients"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出货客户" prop="shippingClients">
                                <el-select
                                    v-model="reportForm.shippingClients"
                                    multiple
                                    filterable
                                    remote
                                    reserve-keyword
                                    placeholder="请输入客户名称搜索"
                                    style="width: 100%"
                                    :loading="clientsLoading"
                                    :remote-method="searchClients"
                                    :max-collapse-tags="2"
                                    collapse-tags
                                    collapse-tags-tooltip
                                >
                                    <el-option
                                        v-for="client in myClients"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="重点开发客户" prop="keyDevelopmentClients">
                                <el-select
                                    v-model="reportForm.keyDevelopmentClients"
                                    multiple
                                    filterable
                                    remote
                                    reserve-keyword
                                    placeholder="请输入客户名称搜索"
                                    style="width: 100%"
                                    :loading="clientsLoading"
                                    :remote-method="searchClients"
                                    :max-collapse-tags="2"
                                    collapse-tags
                                    collapse-tags-tooltip
                                >
                                    <el-option
                                        v-for="client in myClients"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 责任心评级 -->
                <el-form-item label="责任心评级" prop="responsibilityLevel" required>
                    <el-radio-group v-model="reportForm.responsibilityLevel">
                        <el-radio value="优秀">优秀</el-radio>
                        <el-radio value="中等">中等（等待主管约谈）</el-radio>
                        <el-radio value="差">差（等待处罚）</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 下班准备工作 -->
                <el-form-item label="下班准备工作" prop="endOfDayChecklist" required>
                    <el-checkbox-group v-model="reportForm.endOfDayChecklist">
                        <el-checkbox value="整理完桌面">整理完桌面</el-checkbox>
                        <el-checkbox value="整理完50通预计电话邮件上级">整理完50通预计电话邮件上级</el-checkbox>
                        <el-checkbox value="会议已开完">会议已开完</el-checkbox>
                        <el-checkbox value="准备好明天工作资料">准备好明天工作资料</el-checkbox>
                        <el-checkbox value="问候领导后打卡离开">问候领导后打卡离开</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item label="今日效果" prop="dailyResults">
                    <el-input
                        v-model="reportForm.dailyResults"
                        type="textarea"
                        :rows="4"
                        placeholder="请描述今日的工作效果和成果..."
                        maxlength="2000"
                        show-word-limit
                    />
                </el-form-item>

                <el-form-item label="会议报告" prop="meetingReport">
                    <el-input
                        v-model="reportForm.meetingReport"
                        type="textarea"
                        :rows="4"
                        placeholder="请记录今日参加的会议内容..."
                        maxlength="2000"
                        show-word-limit
                    />
                </el-form-item>

                <el-form-item label="工作日记" prop="workDiary">
                    <el-input
                        v-model="reportForm.workDiary"
                        type="textarea"
                        :rows="4"
                        placeholder="请总结今日工作和遇到的问题..."
                        maxlength="2000"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelReport">取消</el-button>
                    <el-button
                        type="primary"
                        @click="submitReport"
                        :loading="submitLoading"
                    >
                        提交日报
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 日报详情对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="日报详情"
            width="800px"
            destroy-on-close
        >
            <div v-if="currentReport" class="report-detail">
                <!-- 员工信息 -->
                <div class="detail-section">
                    <h3>员工信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>日报日期:</label>
                            <span>{{ formatDateWithYear(currentReport.reportDate) }}</span>
                        </div>
                        <div class="info-item">
                            <label>责任心评级:</label>
                            <el-tag :type="getResponsibilityTagType(currentReport.responsibilityLevel)">
                                {{ currentReport.responsibilityLevel }}
                            </el-tag>
                        </div>
                        <div class="info-item">
                            <label>提交时间:</label>
                            <span>{{ formatDateTimeWithYear(currentReport.createTime) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 客户统计 -->
                <div class="detail-section">
                    <h3>客户统计</h3>
                    <div class="stats-row">
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.yearlyNewClients || 0 }}</div>
                            <div class="stat-desc">年度新客户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.monthlyNewClients || 0 }}</div>
                            <div class="stat-desc">当月新客户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.daysSinceLastNewClient || 0 }}</div>
                            <div class="stat-desc">距上次新客户天数</div>
                        </div>
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="detail-section" v-if="hasClientData(currentReport)">
                    <h3>客户信息</h3>
                    <div class="client-info">
                        <div class="client-group" v-if="getParsedClientIds(currentReport, 'inquiryClients').length > 0">
                            <h4>询价客户</h4>
                            <div class="client-tags">
                                <el-tag v-for="clientId in getParsedClientIds(currentReport, 'inquiryClients')" :key="clientId" type="info">
                                    {{ getClientName(clientId) }}
                                </el-tag>
                            </div>
                        </div>
                        <div class="client-group" v-if="getParsedClientIds(currentReport, 'shippingClients').length > 0">
                            <h4>出货客户</h4>
                            <div class="client-tags">
                                <el-tag v-for="clientId in getParsedClientIds(currentReport, 'shippingClients')" :key="clientId" type="success">
                                    {{ getClientName(clientId) }}
                                </el-tag>
                            </div>
                        </div>
                        <div class="client-group" v-if="getParsedClientIds(currentReport, 'keyDevelopmentClients').length > 0">
                            <h4>重点开发客户</h4>
                            <div class="client-tags">
                                <el-tag v-for="clientId in getParsedClientIds(currentReport, 'keyDevelopmentClients')" :key="clientId" type="warning">
                                    {{ getClientName(clientId) }}
                                </el-tag>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 下班准备工作 -->
                <div class="detail-section" v-if="getParsedChecklistItems(currentReport).length > 0">
                    <h3>下班准备工作</h3>
                    <div class="checklist-display">
                        <div class="checklist-items">
                            <div v-for="item in getParsedChecklistItems(currentReport)" :key="item" class="checklist-item">
                                <el-icon class="check-icon"><Check /></el-icon>
                                <span>{{ item }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工作记录 -->
                <div class="detail-section">
                    <h3>工作记录</h3>
                    <div class="work-records">
                        <div class="record-item">
                            <h4>今日效果</h4>
                            <div class="record-content">
                                {{ currentReport.dailyResults || '暂无记录' }}
                            </div>
                        </div>
                        <div class="record-item">
                            <h4>会议报告</h4>
                            <div class="record-content">
                                {{ currentReport.meetingReport || '暂无记录' }}
                            </div>
                        </div>
                        <div class="record-item">
                            <h4>工作日记</h4>
                            <div class="record-content">
                                {{ currentReport.workDiary || '暂无记录' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 领导评价 -->
                <div class="detail-section">
                    <h3>领导评价</h3>
                    <div class="evaluation-content">
                        <div v-if="currentReport.managerEvaluation" class="evaluation-text">
                            {{ currentReport.managerEvaluation }}
                        </div>
                        <div v-if="currentReport.evaluationTime" class="evaluation-info">
                            <span class="evaluation-time">评价时间：{{ formatDateTimeWithYear(currentReport.evaluationTime) }}</span>
                            <span v-if="currentReport.evaluatorName" class="evaluator-name">评价人：{{ currentReport.evaluatorName }}</span>
                        </div>
                        <div v-if="!currentReport.managerEvaluation" class="no-evaluation">
                            暂无评价
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Check } from '@element-plus/icons-vue'
import {
    submitSalesReport,
    getSalesReportPage,
    getSalesReportByDate,
    getSalesReportById,
    getMyClients,
    getMyClientStatistics
} from '@/api/salesReport'

// 响应式数据
const loading = ref(false)
const reportList = ref([])
const reportDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentReport = ref(null)
const submitLoading = ref(false)
const clientsLoading = ref(false)

// 客户相关数据
const myClients = ref([])
const clientStatistics = reactive({
    yearlyNewClients: 0,
    monthlyNewClients: 0,
    daysSinceLastNewClient: 0
})

// 客户数据缓存
const clientsCache = new Map()
let searchTimeout = null

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
    dateRange: []
})

// 日报表单
const reportForm = reactive({
    reportDate: new Date().toISOString().split('T')[0],
    inquiryClients: [],           // 询价客户
    shippingClients: [],          // 出货客户
    keyDevelopmentClients: [],    // 重点开发客户
    responsibilityLevel: '',      // 责任心评级
    endOfDayChecklist: [],        // 下班准备工作清单
    dailyResults: '',             // 今日效果
    meetingReport: '',            // 会议报告
    workDiary: ''                 // 工作日记
})

// 表单验证规则
const reportFormRules = {
    responsibilityLevel: [
        { required: true, message: '请选择责任心评级', trigger: 'change' }
    ],
    endOfDayChecklist: [
        {
            required: true,
            message: '请选择下班准备工作',
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!value || !Array.isArray(value) || value.length === 0) {
                    callback(new Error('请至少选择一项下班准备工作'))
                } else {
                    callback()
                }
            }
        }
    ],
    dailyResults: [
        { required: true, message: '请填写今日效果', trigger: 'blur' },
        { min: 1, message: '今日效果至少需要1个字符', trigger: 'blur' }
    ],
    meetingReport: [
        { required: true, message: '请填写会议报告', trigger: 'blur' },
        { min: 1, message: '会议报告至少需要1个字符', trigger: 'blur' }
    ],
    workDiary: [
        { required: true, message: '请填写工作日记', trigger: 'blur' },
        { min: 1, message: '工作日记至少需要1个字符', trigger: 'blur' }
    ]
}

const reportFormRef = ref()

// 方法
// 加载员工负责的客户列表（带缓存）
const loadMyClients = async (keyword = '') => {
    const cacheKey = keyword || 'default'

    // 检查缓存
    if (clientsCache.has(cacheKey)) {
        myClients.value = clientsCache.get(cacheKey)
        return
    }

    try {
        clientsLoading.value = true
        const params = {}
        if (keyword) {
            params.keyword = keyword
            params.limit = 10  // 搜索时限制返回数量
        } else {
            params.limit = 10  // 默认返回10个
        }

        const response = await getMyClients(params)
        if (response.code === 200) {
            const clientData = response.data || []
            myClients.value = clientData
            // 缓存数据
            clientsCache.set(cacheKey, clientData)
        }
    } catch (error) {
        ElMessage.error('获取客户列表失败')
        console.error('Load my clients error:', error)
    } finally {
        clientsLoading.value = false
    }
}

// 远程搜索客户（带防抖）
const searchClients = (keyword) => {
    // 清除之前的定时器
    if (searchTimeout) {
        clearTimeout(searchTimeout)
    }

    // 设置防抖延迟
    searchTimeout = setTimeout(() => {
        if (keyword) {
            loadMyClients(keyword)
        } else {
            // 如果没有关键词，使用缓存的默认列表
            const defaultClients = clientsCache.get('default')
            if (defaultClients) {
                myClients.value = defaultClients
            } else {
                loadMyClients()
            }
        }
    }, 300) // 300ms防抖延迟
}

// 加载客户统计信息
const loadClientStatistics = async () => {
    try {
        const response = await getMyClientStatistics()
        if (response.code === 200) {
            Object.assign(clientStatistics, response.data)
        }
    } catch (error) {
        ElMessage.error('获取客户统计信息失败')
        console.error('Load client statistics error:', error)
    }
}
const loadReportList = async () => {
    try {
        loading.value = true

        const params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }

        if (searchForm.dateRange && searchForm.dateRange.length === 2) {
            params.startDate = searchForm.dateRange[0]
            params.endDate = searchForm.dateRange[1]
        }

        const response = await getSalesReportPage(params)

        if (response.code === 200) {
            reportList.value = response.data.list || []
            total.value = response.data.total || 0
        }
    } catch (error) {
        ElMessage.error('获取日报列表失败')
        console.error('Load report list error:', error)
    } finally {
        loading.value = false
    }
}

const getResponsibilityTagType = (level) => {
    switch (level) {
        case '优秀': return 'success'
        case '中等': return 'warning'
        case '差': return 'danger'
        default: return 'info'
    }
}

const getChecklistCompletion = (checklistData) => {
    try {
        let items = []

        // 如果是字符串，尝试解析JSON
        if (typeof checklistData === 'string') {
            const parsed = JSON.parse(checklistData)
            items = parsed.items || []
        } else if (Array.isArray(checklistData)) {
            // 如果已经是数组，直接使用
            items = checklistData
        } else {
            return 0
        }

        // 总共5个检查项
        const totalItems = 5
        const completedItems = items.length

        return Math.round((completedItems / totalItems) * 100)
    } catch (error) {
        console.error('解析检查清单完成度失败:', error)
        return 0
    }
}



const formatDate = (date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    })
}

const formatDateTime = (datetime) => {
    return new Date(datetime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const formatDateWithYear = (date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

const formatDateTimeWithYear = (datetime) => {
    return new Date(datetime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const handleSearch = () => {
    currentPage.value = 1
    loadReportList()
}

const handleReset = () => {
    searchForm.dateRange = []
    currentPage.value = 1
    loadReportList()
}

const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    loadReportList()
}

const handleCurrentChange = (page) => {
    currentPage.value = page
    loadReportList()
}

const openReportDialog = async () => {
    resetReportForm()

    // 加载客户数据和统计信息
    // 优化：只在缓存为空时才加载默认客户列表
    const promises = [loadClientStatistics()]

    if (!clientsCache.has('default')) {
        promises.push(loadMyClients('')) // 加载默认客户列表
    } else {
        // 使用缓存的默认客户列表
        myClients.value = clientsCache.get('default')
    }

    await Promise.all(promises)
    reportDialogVisible.value = true
}



const viewReport = async (report) => {
    try {
        loading.value = true

        // 获取日报详情
        const reportResponse = await getSalesReportById(report.id)

        if (reportResponse.code === 200 && reportResponse.data) {
            currentReport.value = reportResponse.data

            // 确保客户数据已加载，以便正确显示客户名称
            if (!clientsCache.has('default')) {
                await loadMyClients('') // 加载客户数据以便显示客户名称
            } else {
                // 使用缓存的客户数据
                myClients.value = clientsCache.get('default')
            }

            // 确保所有相关客户数据都已加载
            await ensureClientDataLoaded(reportResponse.data)

            detailDialogVisible.value = true
        } else {
            ElMessage.error(reportResponse.message || '获取日报详情失败')
        }
    } catch (error) {
        ElMessage.error('获取日报详情失败')
        console.error('Get report detail error:', error)
    } finally {
        loading.value = false
    }
}

const resetReportForm = () => {
    Object.assign(reportForm, {
        reportDate: new Date().toISOString().split('T')[0],
        inquiryClients: [],
        shippingClients: [],
        keyDevelopmentClients: [],
        responsibilityLevel: '',
        endOfDayChecklist: [],
        dailyResults: '',
        meetingReport: '',
        workDiary: ''
    })
}

const submitReport = async () => {
    try {
        // 表单验证
        const isValid = await reportFormRef.value.validate().catch(() => false)

        if (!isValid) {
            // 检查具体哪些字段没有填写
            const missingFields = []
            if (!reportForm.responsibilityLevel) {
                missingFields.push('责任心评级')
            }
            if (!reportForm.endOfDayChecklist || reportForm.endOfDayChecklist.length === 0) {
                missingFields.push('下班准备工作')
            }
            if (!reportForm.dailyResults || reportForm.dailyResults.length < 1) {
                missingFields.push('今日效果（至少1个字符）')
            }
            if (!reportForm.meetingReport || reportForm.meetingReport.length < 1) {
                missingFields.push('会议报告（至少1个字符）')
            }
            if (!reportForm.workDiary || reportForm.workDiary.length < 1) {
                missingFields.push('工作日记（至少1个字符）')
            }

            if (missingFields.length > 0) {
                ElMessage.error(`请完善以下必填项：${missingFields.join('、')}`)
            }
            return
        }

        submitLoading.value = true

        const submitData = {
            // 注：移除了reportDate，由后端根据当前时间自动设置
            inquiryClients: reportForm.inquiryClients,
            shippingClients: reportForm.shippingClients,
            keyDevelopmentClients: reportForm.keyDevelopmentClients,
            responsibilityLevel: reportForm.responsibilityLevel,
            endOfDayChecklist: reportForm.endOfDayChecklist,
            dailyResults: reportForm.dailyResults,
            meetingReport: reportForm.meetingReport,
            workDiary: reportForm.workDiary
        }

        const response = await submitSalesReport(submitData)

        if (response.code === 200) {
            ElMessage.success('日报提交成功')
            reportDialogVisible.value = false
            loadReportList()
        } else {
            // 显示后端返回的具体错误信息
            ElMessage.error(response.message || '提交失败，请重试')
        }
    } catch (error) {
        console.error('Submit report error:', error)

        // 处理网络请求错误
        if (error.response && error.response.data) {
            ElMessage.error(error.response.data.message || '提交失败，请检查网络连接')
        } else if (error.message) {
            ElMessage.error(error.message)
        } else {
            ElMessage.error('提交失败，请重试')
        }
    } finally {
        submitLoading.value = false
    }
}

const cancelReport = () => {
    reportDialogVisible.value = false
}

// 清理客户数据缓存
const clearClientsCache = () => {
    clientsCache.clear()
    if (searchTimeout) {
        clearTimeout(searchTimeout)
        searchTimeout = null
    }
}

// 确保所有相关客户数据已加载
const ensureClientDataLoaded = async (report) => {
    if (!report) return

    // 收集所有客户ID
    const allClientIds = new Set()

    // 解析各类客户ID
    const inquiryIds = getParsedClientIds(report, 'inquiryClients')
    const shippingIds = getParsedClientIds(report, 'shippingClients')
    const keyDevIds = getParsedClientIds(report, 'keyDevelopmentClients')

    inquiryIds.forEach(id => allClientIds.add(id))
    shippingIds.forEach(id => allClientIds.add(id))
    keyDevIds.forEach(id => allClientIds.add(id))

    // 检查是否有客户ID在当前客户列表中找不到
    const missingClients = Array.from(allClientIds).filter(id =>
        !myClients.value.find(client => client.id === id)
    )

    // 如果有缺失的客户数据，重新加载客户列表
    if (missingClients.length > 0) {
        // 清除缓存，强制重新加载
        clientsCache.delete('default')
        await loadMyClients('') // 重新加载完整的客户列表
    }
}

// 辅助方法
// 解析客户JSON字符串
const parseClientData = (clientJsonString) => {
    try {
        if (!clientJsonString) return []
        const parsed = JSON.parse(clientJsonString)
        return parsed.clientIds || []
    } catch (error) {
        console.error('解析客户数据失败:', error)
        return []
    }
}

// 解析下班准备工作JSON字符串
const parseChecklistData = (checklistJsonString) => {
    try {
        if (!checklistJsonString) return []
        const parsed = JSON.parse(checklistJsonString)
        return parsed.items || []
    } catch (error) {
        console.error('解析检查清单数据失败:', error)
        return []
    }
}

// 获取解析后的客户ID列表
const getParsedClientIds = (report, clientType) => {
    if (!report) return []
    const clientData = report[clientType]
    return parseClientData(clientData)
}

// 获取解析后的检查清单项目
const getParsedChecklistItems = (report) => {
    if (!report) return []
    return parseChecklistData(report.endOfDayChecklist)
}

const hasClientData = (report) => {
    if (!report) return false
    const inquiryClients = getParsedClientIds(report, 'inquiryClients')
    const shippingClients = getParsedClientIds(report, 'shippingClients')
    const keyDevelopmentClients = getParsedClientIds(report, 'keyDevelopmentClients')

    return inquiryClients.length > 0 || shippingClients.length > 0 || keyDevelopmentClients.length > 0
}

const getClientName = (clientId) => {
    const client = myClients.value.find(c => c.id === clientId)
    if (client) {
        return client.name
    }

    // 如果在当前客户列表中找不到，显示客户ID
    return `客户ID: ${clientId}`
}

// 生命周期
onMounted(() => {
    loadReportList()
})
</script>

<style scoped>
.department-content-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.add-btn {
    margin-left: auto;
}

.custom-table {
    margin-bottom: 60px; /* Space for pagination */
    border-radius: 6px;
    overflow: hidden; /* For rounded corners on table */
}

.operation-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; /* Ensure it's above the table if overlapping */
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1; /* Ensure it's above other elements */
}

.dialog-footer {
    text-align: right;
}

.dialog-footer .el-button {
    min-width: 80px;
}

.report-detail {
    max-height: 70vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-section h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.work-records {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 14px;
}

.record-content {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
}

/* 表单分组样式 */
.statistics-section,
.client-selection-section {
    margin-bottom: 25px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
}

.statistics-section h3,
.client-selection-section h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #409eff;
    padding-bottom: 8px;
}

/* 只读输入框样式 */
:deep(.el-input.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
    cursor: not-allowed;
}

/* 多选框组样式 */
:deep(.el-checkbox-group) {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

:deep(.el-checkbox) {
    margin-right: 0;
    margin-bottom: 0;
}

/* 客户选择下拉框样式 */
:deep(.el-select .el-tag) {
    margin: 2px;
    max-width: 150px;
}

:deep(.el-select .el-tag .el-tag__content) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 客户选择下拉框宽度优化 */
:deep(.el-select-dropdown) {
    min-width: 300px !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 客户选择表单项宽度 */
.client-selection-section .el-form-item {
    margin-bottom: 15px;
}

.client-selection-section .el-select {
    width: 100% !important;
}

/* 表格中的客户统计样式 */
.client-stats {
    font-size: 12px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.label {
    color: #909399;
}

.value {
    color: #303133;
    font-weight: 500;
}



/* 详情对话框样式 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-item label {
    font-weight: 600;
    color: #606266;
    min-width: 80px;
}

.stats-row {
    display: flex;
    gap: 20px;
    justify-content: space-around;
}

.stat-box {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
}

.stat-desc {
    font-size: 12px;
    color: #909399;
}

.client-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.client-group h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 14px;
}

.client-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.checklist-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checklist-progress {
    width: 100%;
}

.checklist-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border-radius: 6px;
    border-left: 3px solid #67c23a;
}

.check-icon {
    color: #67c23a;
    font-size: 16px;
}

/* 评价相关样式 */
.evaluation-text {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 10px;
}

.no-evaluation {
    color: #909399;
    font-style: italic;
}

.evaluation-content {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
}

.evaluation-info {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #909399;
    margin-top: 10px;
}

.evaluation-time,
.evaluator-name {
    display: flex;
    align-items: center;
}
</style>

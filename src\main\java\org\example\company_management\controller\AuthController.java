package org.example.company_management.controller;

import jakarta.validation.Valid;
import org.example.company_management.dto.LoginRequest;
import org.example.company_management.dto.UpdateProfileRequest;
import org.example.company_management.entity.Employee;
import org.example.company_management.service.AuthService;
import org.example.company_management.service.NotificationService;
import org.example.company_management.utils.JwtUtil;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * <p>
 * 提供用户登录、验证码获取、用户信息查询和登出等功能。
 * 用户登录需要先获取验证码，登录成功后返回JWT令牌。
 * 系统只允许ADMIN角色的员工登录后台系统。
 * </p>
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private NotificationService notificationService;

    /**
     * 管理员登录（后台系统专用）
     * <p>
     * 验证邮箱和密码，登录成功后只返回JWT令牌。
     * 只有ADMIN角色的员工才能登录系统。
     * </p>
     *
     * @param loginRequest 登录请求体，包含邮箱和密码
     * @return 登录结果，只包含JWT令牌
     * @throws Exception 可能抛出的异常
     */
    @PostMapping("/login")
    public Result<String> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            // 进行登录认证
            Employee employee = authService.login(loginRequest.getPhone(), loginRequest.getPassword());

            // 生成JWT token，只包含必要的身份信息
            Map<String, Object> claims = new HashMap<>();
            claims.put("employeeId", employee.getEmployeeId());
            claims.put("phone", employee.getPhone());
            claims.put("name", employee.getName());
            claims.put("role", employee.getRole());
            String token = JwtUtil.generateToken(claims);

            // 只返回token
            return Result.success(token);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 员工客户端登录
     * <p>
     * 验证邮箱和密码，登录成功后只返回JWT令牌。
     * 所有员工都可以登录客户端系统。
     * </p>
     *
     * @param loginRequest 登录请求体，包含邮箱和密码
     * @return 登录结果，只包含JWT令牌
     * @throws Exception 可能抛出的异常
     */
    @PostMapping("/employee/login")
    public Result<String> employeeLogin(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            System.out.println("收到员工登录请求: " + loginRequest.getPhone());

            // 进行员工登录认证
            Employee employee = authService.employeeLogin(loginRequest.getPhone(), loginRequest.getPassword());
            System.out.println("员工登录成功: " + employee.getName() + ", ID: " + employee.getEmployeeId() + ", 角色: " + employee.getRole());

            // 生成JWT token，只包含必要的身份信息
            Map<String, Object> claims = new HashMap<>();
            claims.put("employeeId", employee.getEmployeeId());
            claims.put("phone", employee.getPhone());
            claims.put("name", employee.getName());
            claims.put("role", employee.getRole());
            String token = JwtUtil.generateToken(claims);
            System.out.println("为员工生成token成功!");

            // 只返回token
            return Result.success(token);
        } catch (Exception e) {
            System.out.println("员工登录失败: " + e.getMessage());
            e.printStackTrace(); // 打印完整堆栈信息以便调试
            return Result.error(e.getMessage());
        }
    }

    /**
     * 管理员注册
     * <p>
     * 注册新的管理员账号，需要验证邮箱验证码。
     * 注册成功后返回管理员信息。
     * </p>
     *
     * @param registerRequest 注册请求，包含姓名、邮箱、密码和验证码
     * @return 注册结果，包含管理员信息
     */
   /* @PostMapping("/register")
    public Result<Employee> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            // 验证两次密码是否一致
            if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
                return Result.error("两次输入的密码不一致");
            }

            // 进行注册
            Employee employee = authService.registerAdmin(
                    registerRequest.getName(),
                    registerRequest.getPhone(),
                    registerRequest.getPassword()
            );

            return Result.success(employee);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }*/

    /**
     * 获取当前登录员工信息
     * <p>
     * 从ThreadLocal中获取当前登录员工的ID，然后查询详细信息。
     * </p>
     *
     * @return 员工信息
     */
    @GetMapping("/info")
    public Result<Employee> getEmployeeInfo() {
        try {
            // 从ThreadLocal中获取员工信息
            Map<String, Object> employeeInfo = (Map<String, Object>) ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            // 获取员工ID
            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 查询员工详细信息
            Employee employee = authService.getEmployeeById(employeeId);
            if (employee == null) {
                return Result.error("员工不存在");
            }
            return Result.success(employee);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新员工个人信息
     * <p>
     * 允许更新姓名、邮箱、密码和身份证号
     * </p>
     *
     * @param updateRequest 更新请求
     * @return 更新后的员工信息
     */
    @PutMapping("/profile")
    public Result<Employee> updateProfile(@Valid @RequestBody UpdateProfileRequest updateRequest) {
        try {
            // 从ThreadLocal中获取员工信息
            Map<String, Object> employeeInfo = (Map<String, Object>) ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            // 获取员工ID
            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 更新员工信息
            Employee updatedEmployee = authService.updateProfile(
                    employeeId,
                    updateRequest.getName(),
                    updateRequest.getPhone(),
                    updateRequest.getEmail(),
                    updateRequest.getPassword(),
                    updateRequest.getOldPassword()
            );

            return Result.success(updatedEmployee);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 退出登录
     * <p>
     * 前端清除本地存储的令牌，后端不需要处理。
     * </p>
     *
     * @return 操作结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        return Result.success();
    }
} 
package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 仪表盘数据访问接口
 */
@Mapper
public interface DashboardMapper {
    
    /**
     * 统计员工总数
     */
    @Select("SELECT COUNT(*) FROM employee")
    int countTotalEmployees();
    
    /**
     * 统计在职员工数量
     */
    @Select("SELECT COUNT(*) FROM employee WHERE status = 'Active'")
    int countActiveEmployees();
    
    /**
     * 统计部门总数
     */
    @Select("SELECT COUNT(*) FROM department")
    int countTotalDepartments();
    
    /**
     * 统计职位总数
     */
    @Select("SELECT COUNT(*) FROM position")
    int countTotalPositions();
    
    /**
     * 计算总业绩
     */
    @Select("SELECT COALESCE(SUM(actual_performance), 0) FROM performance")
    double sumTotalPerformance();
} 
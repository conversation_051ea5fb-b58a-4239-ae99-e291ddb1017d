package org.example.company_management.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 部门业绩统计数据传输对象
 */
@Data
public class DepartmentPerformanceStatsDTO {
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 年月 (新增)
     * 格式: YYYY-MM
     */
    private String yearMonth;
    
    /**
     * 预估业绩总和
     */
    private BigDecimal estimatedPerformance;
    
    /**
     * 实际业绩总和
     */
    private BigDecimal actualPerformance;
    
    /**
     * 部门员工工资总和
     */
    private BigDecimal totalSalary;
    
    /**
     * 记录数量
     */
    private Integer recordCount;

    /**
     * 部门备用金总额
     */
    private BigDecimal totalPettyCash;

    /**
     * 部门总开销 (新增)
     */
    private BigDecimal totalDepartmentExpense;

    /**
     * 部门员工总费用 (新增)
     */
    private BigDecimal totalEmployeeExpense;

    /**
     * 本月实际盈亏 (新增)
     * 计算公式: actualPerformance - totalSalary - totalPettyCash - totalDepartmentExpense - totalEmployeeExpense
     */
    private BigDecimal monthlyProfitLoss;

    /**
     * 本月预计盈亏 (新增)
     * 计算公式: estimatedPerformance - totalSalary - totalPettyCash - totalDepartmentExpense - totalEmployeeExpense
     */
    private BigDecimal estimatedMonthlyProfitLoss;
} 
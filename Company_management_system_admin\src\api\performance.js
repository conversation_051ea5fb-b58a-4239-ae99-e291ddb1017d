import request from '@/utils/request'

/**
 * 分页查询业绩记录
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页大小
 * @param {Number} params.employeeId - 员工ID（可选）
 * @param {String} params.employeeName - 员工姓名（可选）
 * @param {Number} params.departmentId - 部门ID（可选）
 * @param {String} params.yearMonth - 年月，格式为yyyy-MM（可选）
 * @returns {Promise} - 返回Promise对象
 */
export function getPerformancePage(params) {
	return request({
		url: '/performance/page',
		method: 'get',
		params,
	})
}

/**
 * 根据ID查询业绩记录
 * @param {Number} id - 业绩记录ID
 * @returns {Promise} - 返回Promise对象
 */
export function getPerformanceById(id) {
	return request({
		url: `/performance/${id}`,
		method: 'get',
	})
}

/**
 * 获取当前登录员工的业绩记录列表
 * @param {String} date - 可选的日期筛选参数（年月格式：YYYY-MM）
 * @returns {Promise} - 返回Promise对象
 */
export function getCurrentEmployeePerformances(date) {
	const params = date ? { date } : {}
	return request({
		url: '/performance/employee/list',
		method: 'get',
		params,
	})
}

/**
 * 获取当前登录员工特定月份的业绩详情
 * @param {String} date - 年月（格式：YYYY-MM）
 * @returns {Promise} - 返回Promise对象
 */
export function getCurrentEmployeePerformanceDetail(date) {
	return request({
		url: `/performance/employee/detail/${date}`,
		method: 'get',
	})
}

/**
 * 添加业绩记录
 * @param {Object} data - 业绩记录数据
 * @returns {Promise} - 返回Promise对象
 */
export function addPerformance(data) {
	return request({
		url: '/performance',
		method: 'post',
		data,
	})
}

/**
 * 更新业绩记录
 * @param {Object} data - 业绩记录数据
 * @returns {Promise} - 返回Promise对象
 */
export function updatePerformance(data) {
	return request({
		url: '/performance',
		method: 'put',
		data,
	})
}

/**
 * 删除业绩记录
 * @param {Number} id - 业绩记录ID
 * @returns {Promise} - 返回Promise对象
 */
export function deletePerformance(id) {
	return request({
		url: `/performance/${id}`,
		method: 'delete',
	})
}

/**
 * 批量删除业绩记录
 * @param {Array} ids - 业绩记录ID列表
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeletePerformance(ids) {
	return request({
		url: '/performance/batch',
		method: 'delete',
		data: ids,
	})
}

/**
 * 获取绩效统计数据
 * @param {Object} params - 统计参数（如按月份、部门等）
 * @returns {Promise} - 返回请求的Promise对象
 */
export function getPerformanceStats(params) {
	return request({
		url: '/performance/stats',
		method: 'get',
		params,
	})
}

/**
 * 获取部门绩效排名
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回请求的Promise对象
 */
export function getDepartmentRanking(params) {
	return request({
		url: '/performance/department/ranking',
		method: 'get',
		params,
	})
}

/**
 * 获取员工绩效排名
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回请求的Promise对象
 */
export function getEmployeeRanking(params) {
	return request({
		url: '/performance/employee/ranking',
		method: 'get',
		params,
	})
}

/**
 * 导入Excel业绩数据
 * @param {FormData} formData - 包含Excel文件的FormData对象
 * @returns {Promise} - 返回请求的Promise对象
 */
export function importPerformanceExcel(formData) {
	return request({
		url: '/performance/import',
		method: 'post',
		data: formData,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

package org.example.company_management.controller;

import org.example.company_management.entity.PettyCash;
import org.example.company_management.service.PettyCashService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/petty-cash")
public class PettyCashController {
    
    @Autowired
    private PettyCashService pettyCashService;
    
    /**
     * 分页查询备用金记录
     * @param page 页码
     * @param size 每页大小
     * @param employeeName 员工姓名（可选）
     * @param departmentId 部门ID（可选）
     * @param status 状态（可选）
     * @param date 日期（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult<PettyCash>> getPettyCashByPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String date) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("size", size);
        params.put("employeeName", employeeName);
        params.put("departmentId", departmentId);
        params.put("status", status);
        params.put("date", date);
        
        try {
            PageResult<PettyCash> pageResult = pettyCashService.getPettyCashByPage(params);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 根据ID查询备用金记录
     * @param id 备用金ID
     * @return 备用金记录
     */
    @GetMapping("/{id}")
    public Result<PettyCash> getPettyCashById(@PathVariable Integer id) {
        try {
            PettyCash pettyCash = pettyCashService.getPettyCashById(id);
            if (pettyCash != null) {
                return Result.success(pettyCash);
            } else {
                return Result.error("备用金记录不存在");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 添加备用金记录
     * @param pettyCash 备用金信息
     * @return 新增ID
     */
    @PostMapping
    public Result<Integer> addPettyCash(@RequestBody PettyCash pettyCash) {
        try {
            Integer id = pettyCashService.addPettyCash(pettyCash);
            return Result.success(id);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新备用金记录
     * @param id 备用金ID
     * @param pettyCash 备用金信息
     * @return 操作结果
     */
    @PutMapping("/{id}")
    public Result<Void> updatePettyCash(@PathVariable Integer id, @RequestBody PettyCash pettyCash) {
        try {
            pettyCash.setId(id);
            boolean success = pettyCashService.updatePettyCash(pettyCash);
            if (success) {
                return Result.success("更新成功", null);
            } else {
                return Result.error("更新失败，备用金记录不存在");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除备用金记录
     * @param id 备用金ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deletePettyCash(@PathVariable Integer id) {
        try {
            boolean success = pettyCashService.deletePettyCash(id);
            if (success) {
                return Result.success("删除成功", null);
            } else {
                return Result.error("删除失败，备用金记录不存在");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量删除备用金记录
     * @param requestBody 包含ID列表的请求体
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeletePettyCash(@RequestBody Map<String, List<Integer>> requestBody) {
        try {
            List<Integer> ids = requestBody.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.validateFailed("ID列表不能为空");
            }
            
            boolean success = pettyCashService.batchDeletePettyCash(ids);
            if (success) {
                return Result.success("批量删除成功", null);
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 审核备用金申请
     * @param id 备用金ID
     * @param requestBody 包含状态的请求体
     * @return 操作结果
     */
    @PutMapping("/approve/{id}")
    public Result<Void> approvePettyCash(@PathVariable Integer id, @RequestBody Map<String, String> requestBody) {
        try {
            String status = requestBody.get("status");
            if (status == null || status.isEmpty()) {
                return Result.validateFailed("状态不能为空");
            }
            
            boolean success = pettyCashService.approvePettyCash(id, status);
            if (success) {
                return Result.success("审核成功", null);
            } else {
                return Result.error("审核失败，备用金记录不存在");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取待审批的备用金列表
     * @return 待审批备用金列表
     */
    @GetMapping("/pending")
    public Result<List<PettyCash>> getPendingPettyCash() {
        try {
            List<PettyCash> pendingList = pettyCashService.getPendingPettyCash();
            return Result.success(pendingList);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取待审批的备用金数量
     * @return 待审批数量
     */
    @GetMapping("/pending-count")
    public Result<Integer> getPendingPettyCashCount() {
        try {
            int count = pettyCashService.getPendingPettyCashCount();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 分页查询当前登录用户的备用金记录
     * @param page 页码
     * @param size 每页大小
     * @param purpose 用途（可选）
     * @param status 状态（可选）
     * @param date 日期（可选）
     * @return 分页结果
     */
    @GetMapping("/my/page")
    public Result<PageResult<PettyCash>> getMyPettyCashByPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String purpose,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String date) {
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);
            params.put("purpose", purpose);
            params.put("status", status);
            params.put("date", date);
            
            PageResult<PettyCash> pageResult = pettyCashService.getMyPettyCashByPage(params);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新当前用户的备用金记录（仅允许更新待审核状态的记录）
     * @param id 备用金ID
     * @param pettyCash 备用金信息
     * @return 操作结果
     */
    @PutMapping("/my/{id}")
    public Result<Void> updateMyPettyCash(@PathVariable Integer id, @RequestBody PettyCash pettyCash) {
        try {
            boolean success = pettyCashService.updateMyPettyCash(id, pettyCash);
            if (success) {
                return Result.success("更新成功", null);
            } else {
                return Result.error("更新失败，备用金记录可能不存在、不属于当前用户或状态不是待审核");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 提交备用金申请到审批
     * @param id 备用金ID
     * @return 操作结果
     */
    @PutMapping("/submit-to-approval/{id}")
    public Result<Void> submitPettyCashToApproval(@PathVariable Integer id) {
        try {
            boolean success = pettyCashService.submitToApproval(id);
            if (success) {
                return Result.success("提交审批成功", null);
            } else {
                return Result.error("提交审批失败，备用金记录可能不存在、不属于当前用户或状态不允许提交");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 取消备用金申请
     * @param id 备用金ID
     * @return 操作结果
     */
    @PutMapping("/cancel/{id}")
    public Result<Void> cancelPettyCash(@PathVariable Integer id) {
        try {
            boolean success = pettyCashService.cancelPettyCash(id);
            if (success) {
                return Result.success("取消申请成功", null);
            } else {
                return Result.error("取消申请失败，备用金记录可能不存在、不属于当前用户或状态不允许取消");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 根据部门ID列表分页查询备用金记录
     * @param payload 包含page, size, departmentIds, purpose, status的请求体
     * @return 分页结果
     */
    @PostMapping("/department-records")
    public Result<PageResult<PettyCash>> getDepartmentPettyCashRecords(@RequestBody Map<String, Object> payload) {
        try {
            // 前端Vue组件通常使用 page 和 size，而后端Service层和Mapper层可能期望 page/size 或 pageNum/pageSize
            // 我们将从payload中提取 'page' 和 'size'，Service层将使用它们
            Integer page = (Integer) payload.get("page");
            Integer size = (Integer) payload.get("size");
            List<Integer> departmentIds = (List<Integer>) payload.get("departmentIds");
            String purpose = (String) payload.get("purpose");
            String status = (String) payload.get("status");
            String date = (String) payload.get("date");

            // 参数校验：page 和 size 是必需的，departmentIds可以由前端逻辑保证不为空，但service层会处理空列表
            if (page == null || size == null) {
                return Result.validateFailed("分页参数 page 和 size 不能为空");
            }
             if (departmentIds == null || departmentIds.isEmpty()) {
                // 根据产品需求，如果部门ID列表为空，前端可能已经阻止了请求
                // 或者，我们可以返回一个空列表，或者根据权限返回所有负责的部门数据（当前Service实现是返回空）
                // 此处，我们依赖Service层处理空departmentIds的情况（返回空PageResult）
            }

            Map<String, Object> serviceParams = new HashMap<>();
            serviceParams.put("page", page);
            serviceParams.put("size", size);
            serviceParams.put("departmentIds", departmentIds);
            serviceParams.put("purpose", purpose); // service层会处理null或空字符串
            serviceParams.put("status", status);   // service层会处理null或空字符串
            serviceParams.put("date", date);

            PageResult<PettyCash> pageResult = pettyCashService.getPettyCashByDepartmentIds(serviceParams);
            return Result.success(pageResult);
        } catch (Exception e) {
            // 日志记录异常 e.getMessage() e.printStackTrace()
            return Result.error("查询部门备用金记录失败: " + e.getMessage());
        }
    }
} 
package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
// import org.example.company_management.entity.EmployeeOtherExpense;

import java.math.BigDecimal;
// import java.util.List;

@Mapper
public interface EmployeeOtherExpenseMapper {
    /**
     * 根据员工ID和月份（YYYY-MM）计算员工其他费用的总和。
     * @param employeeId 员工ID
     * @param yearMonth YYYY-MM格式的年月字符串
     * @return 该月份员工其他费用的总和，如果无记录则为0或null（取决于SQL COALESCE）
     */
    BigDecimal sumAmountByEmployeeIdAndYearMonth(
            @Param("employeeId") Integer employeeId,
            @Param("yearMonth") String yearMonth
    );

    // Potentially other methods if they exist
} 
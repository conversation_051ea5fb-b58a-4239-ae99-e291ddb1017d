# 公司管理系统 - 后台管理端 (ManaSysVue - Admin)

## 项目概述

本项目是公司管理系统的后台管理端（ManaSysVue - Admin），基于 Vue 3 和 Vite 构建。与后端 API 服务配套使用，面向企业管理员、负责人等角色，提供组织架构、人员、客户、业绩、薪资、备用金等全方位管理能力。

## 技术栈与开发环境

- **核心框架**：Vue 3
- **构建工具**：Vite
- **路由管理**：Vue Router 4.x
- **状态管理**：Pinia 2.x
- **UI 组件库**：Element Plus（已全局配置为中文）
- **HTTP 客户端**：Axios
- **开发环境推荐**：Node.js v18+（或当前 LTS 版本）

# Vue 3 + Vite

## 项目结构

```
src/
├── api/            # API 请求模块（按业务模块组织）
├── assets/         # 静态资源
├── components/     # 可复用UI组件及全局组件注册
├── config/         # 配置文件
├── router/         # 路由配置
├── stores/         # Pinia 状态管理（含权限、用户信息等）
├── utils/          # 工具类（如axios封装）
├── views/          # 页面级组件
├── App.vue         # 根组件
├── main.js         # 应用入口
└── style.css       # 全局样式
```

## 主要功能模块

- **登录/登出**：管理员身份认证 (基于手机号和密码)，支持token自动续期与失效处理
- **仪表盘/欢迎页**：系统概览、统计数据展示
- **组织管理**：
    - 部门管理
    - 职位管理
- **人员管理**：
    - **员工管理**：支持员工手机号的查看、添加和编辑（手机号为必填项）；添加新员工时，邮箱字段为可选。
    - 客户管理
- **业绩管理**：
    - **业绩分析**: 业绩分析，支持查看部门及员工业绩，并可深入分析新添加的详细财务指标，如平均部门开销、员工费用、预计及实际月度盈亏等。
    - 工资管理
- **备用金管理（申请、审批、统计）**
- **个人资料管理**（管理员自身）：允许管理员编辑自己的手机号（必填）、邮箱（可选），身份证信息不可编辑但仍可查看。

## 核心实现机制

- **路由与权限守卫**：
    - 使用 Vue Router 配置页面路由和嵌套路由
    - 全局前置守卫实现登录校验、token失效自动跳转登录页
    - 登录后自动拉取用户信息和菜单权限，支持基于角色的菜单/页面访问控制

- **状态管理**：
    - 通过 Pinia 管理 token、用户信息、可访问菜单ID等
    - 状态持久化到 localStorage，支持刷新/重开浏览器后自动恢复
    - 支持登出清理所有认证信息

- **API 交互**：
    - 统一封装 axios 实例（`src/utils/request.js`），`baseURL` 为 `/api`
    - 请求拦截器自动加 Authorization 头
    - 响应拦截器统一处理后端 `{ code, message, data }`，401自动登出并跳转登录页。对其他非成功响应码 (如业务逻辑错误 `code !== 200`)，会通过 `ElMessage` 向用户提示具体的错误信息 (`message`)。
    - 网络/服务器错误详细提示

- **开发环境代理**：
    - `vite.config.js` 配置 `/api` 代理到后端服务（默认 `http://localhost:8080`），并重写路径
    - 生产构建时自动移除所有 `console.log` 和 `debugger`（terser配置）

- **UI与国际化**：
    - 全面采用 Element Plus 组件库
    - 全局配置为中文语言环境

## 项目运行与构建

1. **安装依赖**（需先安装 Node.js 和 npm/yarn/pnpm）：
    ```bash
    npm install
    # 或 yarn install
    # 或 pnpm install
    ```

2. **开发模式运行**（默认端口 http://localhost:5173）：
    ```bash
    npm run dev
    ```

3. **生产环境构建**（生成静态资源到 dist 目录）：
    ```bash
    npm run build
    ```

4. **预览生产构建产物**：
    ```bash
    npm run preview
    ```

## 环境变量

- `.env.production` 用于定义生产环境变量（如 API 地址等）
- 通过 `import.meta.env.VITE_XXX` 访问自定义环境变量
- 例如可配置 `VITE_API_BASE` 为生产API地址

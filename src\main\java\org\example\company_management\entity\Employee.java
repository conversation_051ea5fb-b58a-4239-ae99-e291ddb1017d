package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

/**
 * 员工实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Employee {
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 邮箱（登录账号）
     */
    private String email;
    
    /**
     * 密码（MD5加密）
     */
    private String password;
    
    /**
     * 入职时间
     */
    private Date entryDate;
    
    /**
     * 离职时间
     */
    private Date exitDate;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 部门名称（非数据库字段）
     */
    private String departmentName;
    
    /**
     * 职位ID
     */
    private Integer positionId;
    
    /**
     * 职位名称（非数据库字段）
     */
    private String positionName;
    
    /**
     * 所属物流航线
     */
    private String logisticsRoute;
    
    /**
     * 工作状态
     */
    private String status;
    
    /**
     * 角色（ADMIN/USER）
     */
    private String role;

    /**
     * 可访问菜单ID
     */
    private String accessibleMenuIdsJson;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 部门信息（非数据库字段）
     */
    private Department department;
    
    /**
     * 职位信息（非数据库字段）
     */
    private Position position;
} 
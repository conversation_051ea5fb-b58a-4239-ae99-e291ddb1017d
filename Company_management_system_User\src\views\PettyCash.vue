<template>
    <div class="petty-cash-container">
        <el-card class="petty-cash-list-card">
            <!-- 头部搜索和操作栏 -->
            <div class="toolbar">
                <div class="search-box">
                    <el-input
                        v-model="searchPurpose"
                        placeholder="搜索用途"
                        clearable
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                    >
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                    <!-- 添加状态下拉框 -->
                    <el-select
                        v-model="searchStatus"
                        placeholder="选择状态"
                        clearable
                        style="width: 140px"
                    >
                        <el-option
                            v-for="item in ['待审核', '审核中', '已审核', '已拒绝', '已取消']"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    <!-- 新增年月搜索 -->
                    <el-date-picker
                        v-model="searchDate"
                        type="month"
                        placeholder="选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        clearable
                        style="width: 140px; margin-left: 10px; margin-right: 10px;"
                        @change="handleSearch" 
                        @clear="handleSearch"
                    />
                    <el-button
                        type="primary"
                        @click="handleSearch"
                    >搜索</el-button>
                    <el-button @click="handleReset">
                        <el-icon><RefreshRight /></el-icon>重置
                    </el-button>
                </div>
                
                <!-- 申请备用金按钮 -->
                <el-button
                    type="success"
                    @click="openAddDialog"
                    class="add-btn"
                >
                    <el-icon><Plus /></el-icon>
                    申请备用金
                </el-button>
            </div>

            <el-table
                v-loading="loading"
                :data="pettyCashList"
                border
                row-key="id"
                :max-height="'calc(100vh - 220px)'"
                class="custom-table"
                :header-cell-style="{ background: '#f7f7f7', color: '#606266' }"
            >
                <el-table-column type="index" width="60" align="center" label="序号" class-name="index-column" />
                <el-table-column prop="purpose" label="用途" min-width="150" show-overflow-tooltip />
                <el-table-column prop="amount" label="金额" min-width="120" align="right">
                    <template #default="scope">
                        {{ formatCurrency(scope.row.amount) }}
                    </template>
                </el-table-column>
                <!-- 新增年月列 -->
                <el-table-column prop="date" label="申请年月" min-width="120" show-overflow-tooltip align="center" />
                
                <!-- 状态列 -->
                <el-table-column prop="status" label="状态" min-width="100" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tag
                            :type="getStatusTagType(scope.row.status)"
                            effect="light"
                        >
                            {{ scope.row.status || '未知状态' }}
                        </el-tag>
                    </template>
                </el-table-column>
                
                <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                        {{ formatDateTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                
                <el-table-column prop="updateTime" label="更新时间" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                        {{ formatDateTime(scope.row.updateTime) }}
                    </template>
                </el-table-column>
                
                <!-- 添加操作列 -->
                <el-table-column label="操作" fixed="right" min-width="260" align="center">
                    <template #default="scope">
                        <div class="operation-buttons">
                            <!-- 待审核或已取消状态可编辑 -->
                            <el-button
                                v-if="scope.row.status === '待审核' || scope.row.status === '已取消'"
                                type="primary"
                                size="small"
                                @click="handleEdit(scope.row)"
                                title="编辑申请"
                            >
                                <el-icon><Edit /></el-icon>编辑
                            </el-button>
                            
                            <!-- 待审核或已取消状态可提交审核 -->
                            <el-button
                                v-if="scope.row.status === '待审核' || scope.row.status === '已取消'"
                                type="success"
                                size="small"
                                @click="handleSubmitToApproval(scope.row)"
                                title="提交审核"
                            >
                                <el-icon><Check /></el-icon>提交审核
                            </el-button>
                            
                            <!-- 待审核或审核中状态可取消 -->
                            <el-button
                                v-if="scope.row.status === '待审核' || scope.row.status === '审核中'"
                                type="danger"
                                size="small"
                                @click="handleCancel(scope.row)"
                                title="取消申请"
                            >
                                <el-icon><Close /></el-icon>取消
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <div class="pagination-container">
                <el-pagination
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    v-model:currentPage="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    :page-sizes="[10, 20, 50, 100]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
        
        <!-- 申请备用金对话框 -->
        <el-dialog
            v-model="addDialogVisible"
            title="申请备用金"
            width="500px"
            destroy-on-close
        >
            <el-form
                ref="addFormRef"
                :model="addForm"
                :rules="addFormRules"
                label-width="100px"
                label-position="right"
                status-icon
            >
                <el-form-item label="用途" prop="purpose">
                    <el-input 
                        v-model="addForm.purpose" 
                        type="textarea"
                        :rows="4"
                        placeholder="请输入备用金用途"
                    />
                </el-form-item>
                
                <!-- 新增年月选择 -->
                <el-form-item label="申请年月" prop="date">
                    <el-date-picker
                        v-model="addForm.date"
                        type="month"
                        placeholder="请选择申请年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                    />
                </el-form-item>
                
                <el-form-item label="金额" prop="amount">
                    <el-input-number
                        v-model="addForm.amount"
                        :min="0"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                        placeholder="请输入申请金额"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelAdd">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="submitAdd"
                        :loading="addLoading"
                    >提交申请</el-button>
                </div>
            </template>
        </el-dialog>
        
        <!-- 编辑备用金对话框 -->
        <el-dialog
            v-model="editDialogVisible"
            title="编辑备用金申请"
            width="500px"
            destroy-on-close
        >
            <el-form
                ref="editFormRef"
                :model="editForm"
                :rules="addFormRules"
                label-width="100px"
                label-position="right"
                status-icon
            >
                <el-form-item label="用途" prop="purpose">
                    <el-input 
                        v-model="editForm.purpose" 
                        type="textarea"
                        :rows="4"
                        placeholder="请输入备用金用途"
                    />
                </el-form-item>
                
                <!-- 新增年月选择 -->
                <el-form-item label="申请年月" prop="date">
                    <el-date-picker
                        v-model="editForm.date"
                        type="month"
                        placeholder="请选择申请年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                    />
                </el-form-item>
                
                <el-form-item label="金额" prop="amount">
                    <el-input-number
                        v-model="editForm.amount"
                        :min="0"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                        placeholder="请输入申请金额"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelEdit">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="submitEdit"
                        :loading="editLoading"
                    >保存修改</el-button>
                </div>
            </template>
        </el-dialog>
        
        <!-- 提交审核确认对话框 -->
        <el-dialog
            v-model="submitApprovalDialogVisible"
            title="提交审核确认"
            width="400px"
        >
            <div class="approval-confirm-content">
                <p>确定要将用途为 <strong>{{ currentPettyCash?.purpose }}</strong> 的备用金申请提交审核吗？</p>
                <p>金额: <strong>{{ formatCurrency(currentPettyCash?.amount) }}</strong></p>
                <p>提交后状态将变更为 <el-tag type="info">审核中</el-tag>，等待管理员审核。</p>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="submitApprovalDialogVisible = false">取消</el-button>
                    <el-button 
                        type="success" 
                        @click="confirmSubmitToApproval"
                        :loading="submitApprovalLoading"
                    >确认提交</el-button>
                </div>
            </template>
        </el-dialog>
        
        <!-- 取消申请确认对话框 -->
        <el-dialog
            v-model="cancelDialogVisible"
            title="取消申请确认"
            width="400px"
        >
            <div class="cancel-confirm-content">
                <p>确定要取消用途为 <strong>{{ currentPettyCash?.purpose }}</strong> 的备用金申请吗？</p>
                <p>金额: <strong>{{ formatCurrency(currentPettyCash?.amount) }}</strong></p>
                <p>
                    取消后状态将变更为 <el-tag type="danger">已取消</el-tag>，
                    {{ currentPettyCash?.status === '审核中' ? '不再继续审核流程' : '不会进入审核流程' }}。
                </p>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelDialogVisible = false">取消</el-button>
                    <el-button 
                        type="danger" 
                        @click="confirmCancel"
                        :loading="cancelLoading"
                    >确认取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
    fetchMyPettyCash, 
    applyPettyCash, 
    updatePettyCash, 
    submitToApproval, 
    cancelPettyCash,
    getPettyCashById
} from '@/api/pettyCash';
import { Search, RefreshRight, Plus, Edit, Check, Close } from '@element-plus/icons-vue';

// 数据加载状态
const loading = ref(false);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索和筛选
const searchPurpose = ref('');
const searchStatus = ref('');
const searchDate = ref('');

// 备用金列表
const pettyCashList = ref([]);

// 添加备用金对话框
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const addLoading = ref(false);

// 添加备用金表单数据
const addForm = reactive({
    purpose: '',
    amount: 0,
    date: ''
});

// 编辑备用金对话框
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const editLoading = ref(false);
const editForm = reactive({
    id: null,
    purpose: '',
    amount: 0,
    date: ''
});

// 提交审核对话框
const submitApprovalDialogVisible = ref(false);
const submitApprovalLoading = ref(false);

// 取消申请对话框
const cancelDialogVisible = ref(false);
const cancelLoading = ref(false);

// 当前操作的备用金记录
const currentPettyCash = ref(null);

// 添加备用金表单验证规则
const addFormRules = {
    purpose: [
        { required: true, message: '请输入备用金用途', trigger: 'blur' },
        { min: 2, max: 100, message: '用途长度必须在2-100个字符之间', trigger: 'blur' }
    ],
    amount: [
        { required: true, message: '请输入申请金额', trigger: 'blur' },
        { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
    ],
    date: [{ required: true, message: '请选择年月', trigger: 'change' }]
};

// 生命周期钩子
onMounted(() => {
    fetchData();
});

// 获取备用金列表数据
const fetchData = async () => {
    loading.value = true;
    try {
        const res = await fetchMyPettyCash({
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            purpose: searchPurpose.value || undefined,
            status: searchStatus.value || undefined,
            date: searchDate.value || undefined
        });
        
        if (res.code === 200) {
            pettyCashList.value = res.data.list || [];
            total.value = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取备用金列表失败');
        }
    } catch (error) {
        console.error('获取备用金列表失败', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        loading.value = false;
    }
};

// 根据状态设置不同的Tag样式
const getStatusTagType = (status) => {
    switch (status) {
        case '待审核':
            return 'primary';
        case '审核中':
            return 'info';
        case '已审核':
            return 'success';
        case '已拒绝':
            return 'danger';
        case '已取消':
            return 'warning';
        default:
            return 'info';
    }
};

// 格式化货币
const formatCurrency = (value) => {
    if (value === undefined || value === null) return '¥0.00';
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2
    }).format(value);
};

// 格式化日期时间
const formatDateTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// 搜索处理
const handleSearch = () => {
    currentPage.value = 1;
    fetchData();
};

// 重置筛选条件
const handleReset = () => {
    searchPurpose.value = '';
    searchStatus.value = '';
    searchDate.value = '';
    currentPage.value = 1;
    fetchData();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
    pageSize.value = val;
    fetchData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchData();
};

// 打开添加备用金对话框
const openAddDialog = () => {
    // 重置表单数据
    if (addFormRef.value) {
        addFormRef.value.resetFields();
    } else {
        addForm.purpose = '';
        addForm.amount = 0;
        addForm.date = '';
    }
    
    // 显示对话框
    addDialogVisible.value = true;
};

// 取消添加
const cancelAdd = () => {
    addDialogVisible.value = false;
};

// 提交添加
const submitAdd = async () => {
    // 表单验证
    if (!addFormRef.value) return;
    
    await addFormRef.value.validate(async (valid) => {
        if (!valid) {
            ElMessage.warning('请正确填写表单信息');
            return;
        }
        
        // 金额必须大于0
        if (addForm.amount <= 0) {
            ElMessage.warning('申请金额必须大于0');
            return;
        }
        
        // 设置加载状态
        addLoading.value = true;
        
        try {
            // 发送申请备用金请求
            const res = await applyPettyCash({...addForm});
            
            if (res.code === 200) {
                ElMessage.success('备用金申请提交成功，状态为"待审核"');
                
                // 关闭对话框
                addDialogVisible.value = false;
                
                // 刷新备用金列表
                fetchData();
            } else {
                // 处理其他错误
                ElMessage.error(res.message || '备用金申请提交失败');
            }
        } catch (error) {
            console.error('申请备用金失败', error);
            ElMessage.error('网络错误，请稍后重试');
        } finally {
            // 取消加载状态
            addLoading.value = false;
        }
    });
};

// 处理编辑
const handleEdit = async (row) => {
    try {
        // 获取最新的备用金信息
        const res = await getPettyCashById(row.id);
        
        if (res.code === 200) {
            // 如果状态不是待审核或已取消，显示错误
            if (res.data.status !== '待审核' && res.data.status !== '已取消') {
                ElMessage.warning('只能编辑待审核或已取消状态的备用金申请');
                return;
            }
            
            // 设置编辑表单数据
            editForm.id = res.data.id;
            editForm.purpose = res.data.purpose;
            editForm.amount = res.data.amount;
            editForm.date = res.data.date;
            
            // 打开编辑对话框
            editDialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取备用金详情失败');
        }
    } catch (error) {
        console.error('获取备用金详情失败', error);
        ElMessage.error('网络错误，请稍后重试');
    }
};

// 取消编辑
const cancelEdit = () => {
    editDialogVisible.value = false;
};

// 提交编辑
const submitEdit = async () => {
    // 表单验证
    if (!editFormRef.value) return;
    
    await editFormRef.value.validate(async (valid) => {
        if (!valid) {
            ElMessage.warning('请正确填写表单信息');
            return;
        }
        
        // 金额必须大于0
        if (editForm.amount <= 0) {
            ElMessage.warning('申请金额必须大于0');
            return;
        }
        
        // 设置加载状态
        editLoading.value = true;
        
        try {
            // 发送更新备用金请求
            const res = await updatePettyCash(editForm.id, {
                purpose: editForm.purpose,
                amount: editForm.amount,
                date: editForm.date
            });
            
            if (res.code === 200) {
                ElMessage.success('备用金申请更新成功');
                
                // 关闭对话框
                editDialogVisible.value = false;
                
                // 刷新备用金列表
                fetchData();
            } else {
                ElMessage.error(res.message || '备用金申请更新失败');
            }
        } catch (error) {
            console.error('更新备用金申请失败', error);
            ElMessage.error('网络错误，请稍后重试');
        } finally {
            // 取消加载状态
            editLoading.value = false;
        }
    });
};

// 处理提交审核
const handleSubmitToApproval = (row) => {
    // 保存当前备用金信息
    currentPettyCash.value = row;
    // 打开确认对话框
    submitApprovalDialogVisible.value = true;
};

// 确认提交审核
const confirmSubmitToApproval = async () => {
    if (!currentPettyCash.value) return;
    
    submitApprovalLoading.value = true;
    
    try {
        // 发送提交审核请求
        const res = await submitToApproval(currentPettyCash.value.id);
        
        if (res.code === 200) {
            ElMessage.success('备用金申请已成功提交审核');
            
            // 关闭对话框
            submitApprovalDialogVisible.value = false;
            
            // 刷新备用金列表
            fetchData();
        } else {
            ElMessage.error(res.message || '提交审核失败');
        }
    } catch (error) {
        console.error('提交审核失败', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        submitApprovalLoading.value = false;
    }
};

// 处理取消申请
const handleCancel = (row) => {
    // 保存当前备用金信息
    currentPettyCash.value = row;
    // 打开确认对话框
    cancelDialogVisible.value = true;
};

// 确认取消申请
const confirmCancel = async () => {
    if (!currentPettyCash.value) return;
    
    cancelLoading.value = true;
    
    try {
        // 发送取消申请请求
        const res = await cancelPettyCash(currentPettyCash.value.id);
        
        if (res.code === 200) {
            ElMessage.success('备用金申请已成功取消');
            
            // 关闭对话框
            cancelDialogVisible.value = false;
            
            // 刷新备用金列表
            fetchData();
        } else {
            ElMessage.error(res.message || '取消申请失败');
        }
    } catch (error) {
        console.error('取消备用金申请失败', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        cancelLoading.value = false;
    }
};
</script>

<style scoped>
.petty-cash-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-box .el-input {
    width: 220px;
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

/* 添加备用金按钮样式 */
.add-btn {
    font-weight: bold;
    padding: 10px 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.add-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 操作列按钮容器样式 */
.operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

/* 操作列按钮样式 */
:deep(.el-table .operation-buttons .el-button) {
    border-radius: 4px;
    padding: 6px 10px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

:deep(.el-table .operation-buttons .el-button .el-icon) {
    margin-right: 4px;
    vertical-align: middle;
}

/* 对话框表单样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 12px;
}

:deep(.el-form-item__label) {
    font-weight: 500;
}

:deep(.el-dialog__header) {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 20px;
    margin-bottom: 10px;
}

:deep(.el-dialog__title) {
    font-weight: bold;
    font-size: 18px;
    color: #409EFF;
}

/* 删除确认对话框样式 */
.delete-confirm-content,
.approval-confirm-content,
.cancel-confirm-content {
    padding: 20px;
    text-align: center;
    font-size: 16px;
}

.delete-confirm-content p,
.approval-confirm-content p,
.cancel-confirm-content p {
    margin: 10px 0;
}

.delete-confirm-content strong,
.approval-confirm-content strong,
.cancel-confirm-content strong {
    color: #409EFF;
}

.warning-text {
    color: #F56C6C;
    font-weight: bold;
}

/* 分页容器样式 */
.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

/* 分页选择器宽度调整 */
:deep(.el-pagination .el-select .el-input) {
    width: 120px;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 操作列按钮样式增强 */
:deep(.el-table .operation-buttons .el-button) {
    margin: 2px;
    padding: 6px 10px;
    height: 32px;
    font-size: 12px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .search-box {
        width: 100%;
    }
    
    .search-box .el-input {
        width: 100%;
    }
    
    .add-btn {
        margin-top: 10px;
        width: 100%;
    }
    
    .pagination-container {
        position: static;
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    
    .operation-buttons {
        flex-direction: column;
        gap: 5px;
    }
}
</style> 
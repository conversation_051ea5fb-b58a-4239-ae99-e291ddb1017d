import { HomeFilled, OfficeBuilding, User, DataAnalysis, Money, Document } from '@element-plus/icons-vue';
import { markRaw } from 'vue';

// // 定义所有后台管理系统可用的顶层菜单项 (或根据需要包含子项的扁平列表)
// // 这里仅列出一级菜单作为示例，您可以根据权限控制的粒度调整
// export const allAdminTopLevelMenus = [
//     { id: 'dashboard', title: '首页', icon: markRaw(HomeFilled) },
//     { id: 'organization', title: '组织管理', icon: markRaw(OfficeBuilding) },
//     // 如果需要对子菜单（如部门、职位）进行单独权限控制，应将它们也作为独立项列出，
//     // 或者在权限数据和过滤逻辑中处理层级关系。
//     // 为简化多选框，这里先只列出一级菜单的ID和名称。
//     // 如果权限只控制一级菜单的显示，那么在Dashboard.vue中，子菜单会随父菜单的权限一同显隐。
//     { id: 'personnel', title: '人员管理', icon: markRaw(User) },
//     { id: 'performance', title: '业绩管理', icon: markRaw(DataAnalysis) },
//     { id: 'petty-cash', title: '备用金管理', icon: markRaw(Money) },
// ];

// 也可以提供一个更详细的、包含路径和isMenuItem的完整主菜单列表，如果Employee.vue需要这些信息
// 但对于权限选择，通常只需要id和可读的title

export const hierarchicalAdminMenuItems = [
    { id: 'dashboard', title: '首页', icon: markRaw(HomeFilled) }, // Icons for potential display in tree-select
    {
        id: 'organization', title: '组织管理', icon: markRaw(OfficeBuilding),
        children: [
            { id: 'department', title: '部门管理' },
            { id: 'position', title: '职位管理' },
        ],
    },
    {
        id: 'personnel', title: '人员管理', icon: markRaw(User),
        children: [
            { id: 'employee', title: '员工管理' },
            { id: 'client', title: '客户管理' },
        ],
    },
    {
        id: 'performance', title: '业绩管理', icon: markRaw(DataAnalysis),
        children: [
            { id: 'performance-analysis', title: '业绩分析' },
            { id: 'salary', title: '工资管理' },
            { id: 'department-expense', title: '部门开销' },
            { id: 'employee-expense', title: '员工费用' },
        ],
    },
    {
        id: 'sales-report', title: '销售日报', icon: markRaw(Document),
        children: [
            { id: 'sales-report-management', title: '日报管理' },
        ],
    },
    { id: 'petty-cash', title: '备用金管理', icon: markRaw(Money) },
]; 
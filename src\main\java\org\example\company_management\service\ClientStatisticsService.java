package org.example.company_management.service;

import org.example.company_management.dto.ClientStatisticsDto;

/**
 * {{CHENGQI: 客户统计服务接口}}
 * {{CHENGQI: 任务ID: P1-LD-004}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 客户统计相关业务逻辑接口，遵循接口隔离原则}}
 */
public interface ClientStatisticsService {
    
    /**
     * 计算员工的客户统计数据
     * @param employeeId 员工ID
     * @return 客户统计数据
     */
    ClientStatisticsDto calculateClientStatistics(Integer employeeId);
    
    /**
     * 计算年度新客户总数
     * @param employeeId 员工ID
     * @return 年度新客户总数
     */
    Integer calculateYearlyNewClients(Integer employeeId);
    
    /**
     * 计算当月新客户总数
     * @param employeeId 员工ID
     * @return 当月新客户总数
     */
    Integer calculateMonthlyNewClients(Integer employeeId);
    
    /**
     * 计算距离上次出新客户天数
     * @param employeeId 员工ID
     * @return 距离上次出新客户天数，如果没有新客户则返回-1
     */
    Integer calculateDaysSinceLastNewClient(Integer employeeId);
}

// {{CHENGQI: 接口定义完成，下一步创建实现类}}

import request from './request'

/**
 * 获取当前员工薪资列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getEmployeeSalaries(params) {
	return request({
		url: '/salary/employee/list',
		method: 'get',
		params,
	})
}

/**
 * 获取薪资详情
 * @param {String|Number} id - 薪资ID
 * @returns {Promise}
 */
export function getSalaryDetail(id) {
	return request({
		url: `/salary/employee/detail/${id}`,
		method: 'get',
	})
}

/**
 * 获取多个部门工资列表
 * @param {Object} params - 查询参数
 * @param {Array} params.departmentIds - 部门ID数组
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页大小
 * @param {String} params.startDate - 开始年月，格式为yyyy-MM（可选）
 * @param {String} params.endDate - 结束年月，格式为yyyy-MM（可选）
 * @returns {Promise}
 */
export function getDepartmentMultiSalaries(params) {
	return request({
		url: '/salary/departments/list',
		method: 'post',
		data: params,
	})
}


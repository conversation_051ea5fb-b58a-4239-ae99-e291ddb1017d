<template>
    <div v-if="notifications && notifications.length > 0">
        <el-dialog
            v-for="(notification, index) in notifications"
            :key="notification.notification_id || index" 
            :model-value="true" 
            :title="notification.title_cn || '系统提醒'"
            width="450px"
            :show-close="notifications.length === 1" 
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="() => handleDismiss(notification.notification_id)" 
            append-to-body 
            class="notification-dialog"
            @closed="() => onDialogClosed(notification.notification_id)"
        >
            <div v-html="sanitizeHtml(notification.message_cn)"></div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="() => handleDismiss(notification.notification_id)">
                        我知道了
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, watch, ref, nextTick } from 'vue';
import { useNotificationStore } from '@/stores/notification';
import DOMPurify from 'dompurify'; // 用于XSS防护

const props = defineProps({
    notifications: {
        type: Array,
        default: () => []
    }
});

const notificationStore = useNotificationStore();

// 当只有一个通知时，关闭按钮实际上执行的是dismiss操作
const handleDismiss = (notificationId) => {
    if (notificationId) {
        notificationStore.handleDismissNotification(notificationId);
    }
};

// 可选：如果需要在对话框关闭动画完成后执行某些操作
const onDialogClosed = (dismissedId) => {
    // console.log('Dialog closed for notification:', dismissedId);
    // 这里可以用来处理当多个弹窗时，关闭一个后是否要聚焦下一个等逻辑，但当前element-plus的dialog可能不会自动处理堆叠
};

// XSS防护
const sanitizeHtml = (htmlContent) => {
    return DOMPurify.sanitize(htmlContent);
};

// 监听通知变化，确保每次只显示一个（如果Element Plus Dialog不支持优雅堆叠）
// Element Plus的Dialog默认是覆盖方式，如果同时多个true的model-value，可能行为不理想。
// 此处假设后端返回的 popupNotifications 列表已经是需要依次展示的。
// 如果希望严格一次只弹一个，即便列表有多个，需要更复杂的队列管理逻辑在store或者这里。
// 目前的设计是，如果 notifications 数组有多个，会尝试渲染多个对话框。
</script>

<style scoped>
.notification-dialog :deep(.el-dialog__body) {
    word-break: break-word;
    line-height: 1.6;
}
/* 如果希望弹窗有特定层级，可以调整 z-index */
</style> 
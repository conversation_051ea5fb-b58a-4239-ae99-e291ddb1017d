# 公司管理系统 - 员工端 (ManaSysVue - User)

## 项目概述

本项目是公司管理系统的客户端界面 (ManaSysVue - User)，基于 Vue 3 和 Vite 构建。它与后端API服务 ([ManaSysSer](https://github.com/PandaCllcn/ManaSysSer) - 请替换为后端仓库链接) 配套使用，旨在为公司员工提供便捷的自助服务、信息查询、个人事务管理以及智能通知提醒功能。

## 技术栈与开发环境

- **核心框架**: Vue 3 (使用 `<script setup>` 单文件组件)
- **构建工具**: Vite
- **路由管理**: Vue Router 4.x
- **状态管理**: Pinia 2.x
- **UI 组件库**: Element Plus (已全局配置为中文语言环境)
- **HTTP 客户端**: Axios
- **图标库**: `@element-plus/icons-vue`
- **开发环境推荐**: Node.js v18+ (或当前 LTS 版本)

## 项目结构

```
src/
├── api/            # API 请求模块 (按业务模块组织, 如 notification.js)
├── assets/         # 静态资源 (图片、全局CSS变量等)
├── components/     # 可复用的UI组件 (如 NotificationBell.vue, NotificationPopup.vue)
├── router/         # 路由配置 (index.js)
├── stores/         # Pinia 状态管理模块 (如 auth.js, notification.js)
├── views/          # 页面级组件
├── App.vue         # 根组件
├── main.js         # 应用入口文件 (Vue实例化、插件注册)
└── style.css       # 全局基础样式
```

## 主要功能模块

- **员工登录**: 提供用户认证入口 (基于手机号和密码)。
- **仪表盘/首页**: 展示员工相关的概览信息或快捷入口（快捷操作已包含客户管理与备用金管理）。
- **个人信息管理**: 允许员工查看和修改自己的手机号（必填，唯一性校验）、邮箱（可选），身份证信息不可编辑但仍可查看。
- **客户管理**: 员工个人负责的客户信息查看与管理。
- **备用金管理**: 员工个人备用金的申请、记录查询等。
- **业绩查询**: 员工个人业绩数据的查看与分析，包括本月备用金、本月发布工资、本月平均部门开销、本月员工费用、本月预计盈亏和本月实际盈亏等详细财务指标。
- **工资查询**: 员工个人薪资明细的查询。
- **部门级信息查看** (通常供部门负责人或特定角色使用):
    - 部门员工列表
    - **部门整体业绩总览**: 部门整体业绩总览，包含汇总的部门平均开销、员工费用合计、预计盈亏和实际盈亏等关键财务数据。
    - 部门整体工资概况
    - 部门客户视图
    - 部门备用金使用概览

- **智能通知中心**:
    - **登录时提醒**: 特定职位（如"业务员"）的员工登录后，若有未读通知，系统会自动弹出"通知中心"对话框。
    - **页眉铃铛图标 (`NotificationBell.vue`)**:
        - 实时显示未读消息状态（通过红点提示）。
        - 点击铃铛图标可手动打开"通知中心"对话框。
    - **通知中心对话框**:
        - **选项卡**: 提供"未读消息"和"全部消息"两个视图。
        - **消息列表**: 展示通知的标题、内容摘要和接收时间（相对时间，如"10分钟前"）。
        - **消息详情**: 点击列表中的任一通知，会弹出该通知的详细信息子对话框，包含完整内容和接收时间。
        - **已读状态**: 在"全部消息"列表中，已被处理或在详情中查看过的消息会标记为"已读"。
        - **单个处理**: 在通知详情弹窗中，未读通知提供"我知道了"按钮，点击后将通知标记为已处理，并根据规则计算下次提醒时间。
        - **批量处理**: 在"未读消息"选项卡下，提供"全部标记为已读"按钮，一次性处理所有未读通知。
    - **通知类型**: 根据后端规则生成，例如上月业绩不达标、上月/本月新客户提醒、系统欢迎消息等。

*注：`NotificationPopup.vue` 组件最初设计为单独的通知弹窗，但当前主要的通知展示与交互逻辑已整合入 `NotificationBell.vue` 的通知中心。*

## 核心实现机制

- **路由与导航**:
    - 使用 Vue Router (`src/router/index.js`) 定义页面路由和层级关系。
    - 全局前置导航守卫 (`router.beforeEach`) 实现：
        - **登录校验**: 检查 `localStorage` 中是否存在 `token`，对需要认证的路由进行访问控制，未登录则重定向至登录页。
        - **动态页面标题**: 根据路由元信息 (`meta.title`) 自动设置浏览器窗口标题。

- **状态管理**:
    - 通过 Pinia (`src/stores/token.js` 中的 `useAuthStore`) 集中管理用户认证状态，包括 `token`、当前登录用户信息 (`user`)。
    - 认证状态信息会同步持久化到 `localStorage`，以便在页面刷新或关闭浏览器后保持登录状态。
    - 提供 `clearAuth` 等action用于安全登出。同时，通过 `src/stores/notification.js` (`useNotificationStore`) 管理通知相关的状态，包括：拉取铃铛和弹窗通知、处理用户对通知的"dismiss"和"mark as read"操作、管理未读消息计数以及控制通知中心在登录时是否自动弹出等逻辑。

- **API 交互**:
    - 基于 Axios 封装了统一的API请求模块 (`src/utils/request.js`)。
    - **请求拦截器**: 自动在请求头中附加 `Authorization: Bearer <token>` (如果token存在)。
    - **响应拦截器**: 
        - 解析后端返回的数据结构 (通常为 `{ code, message, data }`)。
        - 对非成功响应码 (`code !== 200`，如业务逻辑错误) 使用 Element Plus 的 `ElMessage` 组件进行全局错误提示，显示具体的错误信息 (`message`)。
        - 针对 `code === 401` (未授权) 的情况，会自动清除本地存储的认证信息并强制用户跳转到登录页面。
        - 处理网络错误及其他HTTP状态码（如404, 500）并给出通用提示。
    - `baseURL` 统一配置为 `/api`，便于通过代理与后端服务通信。`src/api/notification.js` 模块专门封装了与后端通知服务 (`/notifications/...`) 所有端点的交互函数。

- **开发环境代理**:
    - `vite.config.js` 文件中配置了开发服务器代理规则。
    - 所有以 `/api` 开头的请求会被代理到后端API服务器 (默认为 `http://localhost:8080`)，并自动移除路径中的 `/api` 前缀。

- **UI与国际化**:
    - 项目全面采用 Element Plus 作为基础UI组件库。
    - Element Plus 已在 `src/main.js` 中全局配置为中文 (`zhCn`) 语言环境。

## 项目运行与构建

1.  **安装依赖**: (确保已安装 Node.js 和 npm/pnpm/yarn)
    ```bash
    npm install
    # 或者
    # yarn install
    # 或者
    # pnpm install
    ```

2.  **开发模式运行** (启动本地开发服务器，通常在 `http://localhost:5174`):
    ```bash
    npm run dev
    ```

3.  **生产环境构建** (生成静态资源文件到 `dist` 目录):
    ```bash
    npm run build
    ```

4.  **预览生产构建产物** (本地启动一个静态服务器预览 `dist` 目录内容):
    ```bash
    npm run preview
    ```

## 环境变量

- 项目包含 `.env.production` 文件，用于定义生产环境特定的环境变量。
- 在代码中，可以通过 `import.meta.env.VITE_XXX` 的形式访问在 `.env` 文件中以 `VITE_` 为前缀定义的环境变量。
- 例如，可以在 `.env.production` 中配置生产环境的API服务地址 `VITE_API_BASE_URL` (如果需要覆盖Vite代理的默认行为或用于其他目的)。
- [ ] 完善通知系统的用户体验 (例如，更平滑的列表更新动画、通知的本地即时反馈优化)
- [ ] 考虑增加通知声音提示选项
- [ ] 允许用户自定义部分通知的接收偏好（如果后端支持）

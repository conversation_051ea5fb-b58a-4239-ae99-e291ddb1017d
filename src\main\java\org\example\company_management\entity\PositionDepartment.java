package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

/**
 * 职位-部门关联实体类
 * 用于表示职位和部门的多对多关系
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PositionDepartment {
    /**
     * 职位ID
     */
    private Integer positionId;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.PositionMapper">
    <!-- 结果映射 -->
    <resultMap id="positionMap" type="org.example.company_management.entity.Position">
        <id property="positionId" column="position_id"/>
        <result property="positionName" column="position_name"/>
        <result property="positionDescription" column="position_description"/>
        <result property="departmentName" column="department_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 查询所有职位 -->
    <select id="selectAll" resultMap="positionMap">
        SELECT p.position_id, p.position_name, p.position_description, 
               p.status, p.create_time, p.update_time,
               (SELECT d.department_name 
                FROM position_department pd 
                JOIN department d ON pd.department_id = d.department_id 
                WHERE pd.position_id = p.position_id 
                LIMIT 1) as department_name
        FROM position p
        ORDER BY p.position_id
    </select>
    
    <!-- 根据ID查询职位 -->
    <select id="selectById" resultMap="positionMap" parameterType="java.lang.Integer">
        SELECT p.position_id, p.position_name, p.position_description, 
               p.status, p.create_time, p.update_time,
               (SELECT d.department_name 
                FROM position_department pd 
                JOIN department d ON pd.department_id = d.department_id 
                WHERE pd.position_id = p.position_id 
                LIMIT 1) as department_name
        FROM position p
        WHERE p.position_id = #{positionId}
    </select>
    
    <!-- 分页查询职位 -->
    <select id="selectByPage" resultMap="positionMap" parameterType="java.util.Map">
        SELECT p.position_id, p.position_name, p.position_description, 
               p.status, p.create_time, p.update_time,
               (SELECT d.department_name 
                FROM position_department pd 
                JOIN department d ON pd.department_id = d.department_id 
                WHERE pd.position_id = p.position_id 
                LIMIT 1) as department_name
        FROM position p
        <where>
            <if test="positionName != null">
                p.position_name LIKE #{positionName}
            </if>
            <if test="departmentId != null">
                AND EXISTS (
                    SELECT 1 FROM position_department pd 
                    WHERE pd.position_id = p.position_id 
                    AND pd.department_id = #{departmentId}
                )
            </if>
        </where>
        ORDER BY p.position_id
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 查询符合条件的职位总数 -->
    <select id="countPositions" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT COUNT(*) FROM position p
        <where>
            <if test="positionName != null">
                p.position_name LIKE #{positionName}
            </if>
            <if test="departmentId != null">
                AND EXISTS (
                    SELECT 1 FROM position_department pd 
                    WHERE pd.position_id = p.position_id 
                    AND pd.department_id = #{departmentId}
                )
            </if>
        </where>
    </select>
    
    <!-- 根据部门ID查询职位 -->
    <select id="selectByDepartmentId" resultMap="positionMap" parameterType="java.lang.Integer">
        SELECT p.position_id, p.position_name, p.position_description, 
               p.status, p.create_time, p.update_time,
               d.department_name
        FROM position p
        JOIN position_department pd ON p.position_id = pd.position_id
        JOIN department d ON pd.department_id = d.department_id
        WHERE pd.department_id = #{departmentId}
        ORDER BY p.position_id
    </select>
    
    <!-- 添加职位 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Position" useGeneratedKeys="true" keyProperty="positionId">
        INSERT INTO position (
            position_name, 
            position_description,
            status, 
            create_time, 
            update_time
        ) VALUES (
            #{positionName}, 
            #{positionDescription},
            #{status}, 
            #{createTime}, 
            #{updateTime}
        )
    </insert>
    
    <!-- 更新职位 -->
    <update id="update" parameterType="org.example.company_management.entity.Position">
        UPDATE position
        <set>
            <if test="positionName != null">position_name = #{positionName},</if>
            <if test="positionDescription != null">position_description = #{positionDescription},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE position_id = #{positionId}
    </update>
    
    <!-- 删除职位 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM position WHERE position_id = #{positionId}
    </delete>
    
    <!-- 批量更新职位所属部门 -->
    <update id="updatePositionDepartment">
        UPDATE position_department
        SET department_id = #{param2}
        WHERE department_id = #{param1}
    </update>
    
    <!-- 删除职位-部门关联 -->
    <delete id="deletePositionDepartmentByPositionId" parameterType="java.lang.Integer">
        DELETE FROM position_department
        WHERE position_id = #{positionId}
    </delete>
    
    <!-- 根据职位ID查询关联的部门ID列表 -->
    <select id="selectDepartmentIdsByPositionId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT department_id
        FROM position_department
        WHERE position_id = #{positionId}
    </select>
    
    <!-- 根据职位ID查询关联的部门名称列表 -->
    <select id="selectDepartmentNamesByPositionId" parameterType="java.lang.Integer" resultType="java.lang.String">
        SELECT d.department_name
        FROM position_department pd
        JOIN department d ON pd.department_id = d.department_id
        WHERE pd.position_id = #{positionId}
    </select>
    
    <!-- 批量插入职位-部门关联记录 -->
    <insert id="batchInsertPositionDepartment" parameterType="java.util.List">
        INSERT INTO position_department (position_id, department_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.positionId}, #{item.departmentId}, #{item.createTime})
        </foreach>
    </insert>
</mapper>

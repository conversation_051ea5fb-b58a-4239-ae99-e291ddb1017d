<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.DepartmentMapper">
    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.Department">
        <id column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="leader_id" property="leaderId"/>
        <result column="department_leader" property="departmentLeader"/>
        <result column="department_description" property="departmentDescription"/>
        <result column="parent_department_id" property="parentDepartmentId"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="employee_count" property="employeeCount"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        d.department_id, d.department_name, d.leader_id, d.department_description, d.parent_department_id, d.status, d.create_time, d.update_time
    </sql>

    <!-- 查询所有部门 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        ORDER BY d.department_id
    </select>
    
    <!-- 分页查询部门 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        ORDER BY d.department_id
        LIMIT #{offset}, #{pageSize}
    </select>
    
    <!-- 分页查询部门(带条件) -->
    <select id="selectByPageAndCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        <where>
            <if test="departmentName != null and departmentName != ''">
                AND d.department_name LIKE CONCAT('%', #{departmentName}, '%')
            </if>
            <if test="leaderId != null">
                AND d.leader_id = #{leaderId}
            </if>
            <if test="leaderName != null and leaderName != ''">
                AND e.name LIKE CONCAT('%', #{leaderName}, '%')
            </if>
            <if test="parentDepartmentId != null">
                AND d.parent_department_id = #{parentDepartmentId}
            </if>
        </where>
        ORDER BY d.department_id
        LIMIT #{offset}, #{pageSize}
    </select>
    
    <!-- 获取部门总数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM department
    </select>
    
    <!-- 获取符合条件的部门总数 -->
    <select id="countTotalByCondition" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        <where>
            <if test="departmentName != null and departmentName != ''">
                AND d.department_name LIKE CONCAT('%', #{departmentName}, '%')
            </if>
            <if test="leaderId != null">
                AND d.leader_id = #{leaderId}
            </if>
            <if test="leaderName != null and leaderName != ''">
                AND e.name LIKE CONCAT('%', #{leaderName}, '%')
            </if>
            <if test="parentDepartmentId != null">
                AND d.parent_department_id = #{parentDepartmentId}
            </if>
        </where>
    </select>
    
    <!-- 根据关键词搜索部门 -->
    <select id="selectByKeyword" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        WHERE d.department_name LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY d.department_id
    </select>

    <!-- 根据ID查询部门 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        WHERE d.department_id = #{departmentId}
    </select>

    <!-- 新增部门 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Department" useGeneratedKeys="true" keyProperty="departmentId">
        INSERT INTO department (department_name, leader_id, department_description, parent_department_id, status, create_time, update_time)
        VALUES (#{departmentName}, #{leaderId}, #{departmentDescription}, #{parentDepartmentId}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <!-- 修改部门 -->
    <update id="update" parameterType="org.example.company_management.entity.Department">
        UPDATE department
        <set>
            <if test="departmentName != null">
                department_name = #{departmentName},
            </if>
            <if test="leaderId != null">
                leader_id = #{leaderId},
            </if>
            <if test="departmentDescription != null">
                department_description = #{departmentDescription},
            </if>
            <if test="parentDepartmentId != null">
                parent_department_id = #{parentDepartmentId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        WHERE department_id = #{departmentId}
    </update>

    <!-- 删除部门 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM department
        WHERE department_id = #{departmentId}
    </delete>
    
    <!-- 根据父部门ID查询子部门列表 -->
    <select id="selectByParentId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        WHERE d.parent_department_id = #{parentId}
        ORDER BY d.department_id
    </select>
    
    <!-- 根据部门负责人ID查询部门列表 -->
    <select id="selectByLeaderId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>,
        e.name as department_leader,
        (SELECT COUNT(1) FROM employee e WHERE e.department_id = d.department_id) as employee_count
        FROM department d
        LEFT JOIN employee e ON d.leader_id = e.employee_id
        WHERE d.leader_id = #{leaderId}
        ORDER BY d.department_id
    </select>
</mapper> 
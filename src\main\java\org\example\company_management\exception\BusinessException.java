package org.example.company_management.exception;

/**
 * 自定义业务异常
 */
public class BusinessException extends RuntimeException {
    
    private Integer code;
    
    /**
     * 构造函数
     * @param message 异常消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = 400;
    }
    
    /**
     * 构造函数
     * @param code 错误码
     * @param message 异常消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }
    
    /**
     * 获取错误码
     * @return 错误码
     */
    public Integer getCode() {
        return code;
    }
} 
package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门日报编辑DTO
 * 用于部门负责人编辑员工日报
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
public class DepartmentReportEditDTO {

    /**
     * 日报ID
     */
    @NotNull(message = "日报ID不能为空")
    private Long id;

    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空")
    private Integer employeeId;

    /**
     * 日报日期
     */
    @NotNull(message = "日报日期不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate reportDate;

    // ==================== 客户选择字段 ====================

    /**
     * 询价客户ID列表
     */
    private List<Integer> inquiryClientIds;

    /**
     * 出货客户ID列表
     */
    private List<Integer> shippingClientIds;

    /**
     * 重点开发客户ID列表
     */
    private List<Integer> keyDevelopmentClientIds;

    // ==================== 评估字段 ====================

    /**
     * 责任心评级
     */
    @NotBlank(message = "责任心评级不能为空")
    private String responsibilityLevel;

    // ==================== 工作检查清单 ====================

    /**
     * 下班准备工作检查清单（字符串数组）
     */
    private List<String> endOfDayChecklist;

    // ==================== 文本输入字段 ====================

    /**
     * 今日效果
     */
    @Size(min = 1, max = 2000, message = "今日效果内容长度必须在1-2000字符之间")
    private String dailyResults;

    /**
     * 会议报告
     */
    @Size(min = 1, max = 2000, message = "会议报告内容长度必须在1-2000字符之间")
    private String meetingReport;

    /**
     * 工作日记
     */
    @Size(min = 1, max = 2000, message = "工作日记内容长度必须在1-2000字符之间")
    private String workDiary;

    // ==================== 领导评价字段 ====================

    /**
     * 领导评价
     */
    @Size(max = 2000, message = "领导评价内容不能超过2000字符")
    private String managerEvaluation;

    // ==================== 内嵌类 ====================

    /**
     * 检查清单DTO
     */
    @Data
    public static class ChecklistDTO {
        /**
         * 检查项目
         */
        private ChecklistItemsDTO items;

        /**
         * 完成数量
         */
        private Integer completedCount;

        /**
         * 总数量
         */
        private Integer totalCount;

        /**
         * 最后更新时间
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
        private LocalDateTime lastUpdated;
    }

    /**
     * 检查清单项目DTO
     */
    @Data
    public static class ChecklistItemsDTO {
        /**
         * 桌面整理
         */
        private Boolean deskOrganized;

        /**
         * 邮件处理
         */
        private Boolean emailsHandled;

        /**
         * 会议完成
         */
        private Boolean meetingsCompleted;

        /**
         * 资料准备
         */
        private Boolean materialsReady;

        /**
         * 向领导问好
         */
        private Boolean greetedLeader;
    }
}

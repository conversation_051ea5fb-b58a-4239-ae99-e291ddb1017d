# 销售日报功能开发项目

## 项目概述

在现有公司管理系统基础上，新增销售日报功能模块。该功能允许员工每日提交销售工作报告，包含客户开发情况、工作效果、会议记录等关键信息，并支持基于角色的权限管理。

## 项目目标

- 为销售人员提供标准化的日报提交界面
- 实现基于角色的数据访问控制（员工、部门负责人、管理员）
- 提供数据统计和分析功能
- 与现有系统无缝集成，保持一致的UI/UX风格

## 技术架构

### 后端技术栈
- **核心框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0+
- **持久层**: MyBatis
- **安全认证**: JWT
- **开发环境**: JDK 17+, Maven 3.6+

### 前端技术栈
- **管理端**: Vue 3 + Vite + Element Plus
- **员工端**: Vue 3 + Vite + Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4.x

## 现有系统分析

### 数据库表结构
- **employee**: 员工信息表（包含角色字段：admin/manager/employee）
- **department**: 部门信息表（包含负责人关联）
- **client**: 客户信息表（包含员工关联和创建时间）
- **position**: 职位信息表

### 权限体系
- **admin**: 系统管理员，可查看所有数据
- **manager**: 部门负责人，可查看本部门数据
- **employee**: 普通员工，只能查看自己的数据

## 快速导航

- [需求分析](analysis.md) ✅
- [技术方案](proposals.md) 🔄
- [架构设计](architecture/)
- [实施计划](plan.md)
- [项目进度](logs/progress_log.md)
- [团队协作记录](logs/team_collaboration_log.md)

## 项目时间线

**项目启动时间**: 2025-06-04 10:43:32 +08:00
**预计完成时间**: 待制定详细计划后确定

---

*本文档由AI助手创建，遵循RIPER-5开发模式*

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  // 后端统计数据（新增属性）
  statisticsData: {
    type: Array,
    default: () => []
  },
  // 业绩数据列表
  performanceList: {
    type: Array,
    default: () => []
  },
  // 选择的部门信息
  selectedDepartments: {
    type: Array,
    default: () => []
  },
  // 部门选项列表
  departmentOptions: {
    type: Array,
    default: () => []
  },
  // 日期范围
  dateRange: {
    type: Array,
    default: () => []
  },
  // 弹窗是否可见
  visible: {
    type: Boolean,
    default: false
  },
  // 格式化金额的函数
  formatCurrency: {
    type: Function,
    required: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'close']);

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 辅助函数：从日期范围生成月份列表 (YYYY-MM)
const generateMonthsFromDateRange = (dateRangeArray) => {
  if (!dateRangeArray || dateRangeArray.length < 2 || !dateRangeArray[0] || !dateRangeArray[1]) {
    if (props.statisticsData && props.statisticsData.length > 0) {
        const months = new Set(props.statisticsData.map(d => d.yearMonth));
        return Array.from(months).sort();
    }
    return [];
  }
  const months = [];
  let currentDate = new Date(dateRangeArray[0].substring(0, 7) + '-01');
  const endDate = new Date(dateRangeArray[1].substring(0, 7) + '-01');

  while (currentDate <= endDate) {
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    months.push(`${year}-${month}`);
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  return months;
};

// 格式化日期范围
const formattedDateRange = computed(() => {
  if (!props.dateRange || props.dateRange.length < 2 || !props.dateRange[0] || !props.dateRange[1]) {
    return '全部时间';
  }
  
  const startDate = props.dateRange[0].substring(0, 7);
  const endDate = props.dateRange[1].substring(0, 7);
  
  const formatYearMonth = (dateStr) => {
    const [year, month] = dateStr.split('-');
    return `${year}年${month}月`;
  };
  
  return `${formatYearMonth(startDate)} 至 ${formatYearMonth(endDate)}`;
});

// 获取部门名称
const getDepartmentName = (departmentId) => {
  if (!departmentId) return '未知部门';
  
  // 将departmentId转换为字符串，统一比较格式
  const deptIdStr = String(departmentId);
  
  // 递归查找部门名称
  const findName = (options, id) => {
    const idStr = String(id);
    
    for (const option of options) {
      if (String(option.value) === idStr) {
        return option.label;
      }
      
      if (option.children && option.children.length > 0) {
        const name = findName(option.children, id);
        if (name) return name;
      }
    }
    return null;
  };
  
  const name = findName(props.departmentOptions, deptIdStr);
  return name || `部门(${deptIdStr})`;
};

// 格式化选中的部门
const formattedSelectedDepartments = computed(() => {
  if (!props.selectedDepartments || props.selectedDepartments.length === 0) {
    return '未选择部门';
  }
  
  if (props.selectedDepartments.length <= 3) {
    return props.selectedDepartments.map(id => getDepartmentName(id)).join('、');
  }
  
  return props.selectedDepartments.slice(0, 3).map(id => getDepartmentName(id)).join('、') + `等${props.selectedDepartments.length}个部门`;
});

// 已处理的月度统计数据（补齐0值）
const processedMonthlyStats = computed(() => {
  const finalDisplayData = [];
  if (!props.selectedDepartments || props.selectedDepartments.length === 0) {
    return [];
  }

  const generatedMonths = generateMonthsFromDateRange(props.dateRange);
  if (generatedMonths.length === 0 && (!props.statisticsData || props.statisticsData.length === 0)) {
      return [];
  }

  const backendStats = props.statisticsData || [];
  const monthsToIterate = generatedMonths.length > 0 ? generatedMonths :
                          (backendStats.length > 0 ? Array.from(new Set(backendStats.map(s => s.yearMonth))).sort() : []);

  const selectedDeptIds = props.selectedDepartments;

  for (const deptId of selectedDeptIds) {
    const deptName = getDepartmentName(deptId);
    for (const month of monthsToIterate) {
      const existingStat = backendStats.find(stat =>
        (typeof stat.departmentId === 'number' ? stat.departmentId.toString() : stat.departmentId) === (typeof deptId === 'number' ? deptId.toString() : deptId) && stat.yearMonth === month
      );
      if (existingStat) {
        finalDisplayData.push(existingStat);
      } else {
        finalDisplayData.push({
          departmentId: deptId,
          departmentName: deptName,
          yearMonth: month,
          estimatedPerformance: 0,
          actualPerformance: 0,
          totalSalary: 0,
          totalPettyCash: 0,
          totalDepartmentExpense: 0,
          totalEmployeeExpense: 0,
          estimatedMonthlyProfitLoss: 0,
          monthlyProfitLoss: 0,
          recordCount: 0
        });
      }
    }
  }

  // 应用排序：首先按年月降序，然后按部门名称升序
  finalDisplayData.sort((a, b) => {
    if (a.yearMonth > b.yearMonth) return -1;
    if (a.yearMonth < b.yearMonth) return 1;

    const deptNameA = String(a.departmentName || '');
    const deptNameB = String(b.departmentName || '');
    if (deptNameA < deptNameB) return -1;
    if (deptNameA > deptNameB) return 1;

    return 0;
  });

  return finalDisplayData;
});

// 计算总计统计
const totalStatistics = computed(() => {
  if (!processedMonthlyStats.value || processedMonthlyStats.value.length === 0) {
    return {
      departmentName: '总计',
      yearMonth: '-',
      estimatedPerformance: 0,
      actualPerformance: 0,
      totalSalary: 0,
      totalPettyCash: 0,
      totalDepartmentExpense: 0,
      totalEmployeeExpense: 0,
      estimatedMonthlyProfitLoss: 0,
      monthlyProfitLoss: 0,
    };
  }

  const totals = processedMonthlyStats.value.reduce((acc, currentRow) => {
    acc.estimatedPerformance += parseFloat(currentRow.estimatedPerformance || 0);
    acc.actualPerformance += parseFloat(currentRow.actualPerformance || 0);
    acc.totalSalary += parseFloat(currentRow.totalSalary || 0);
    acc.totalPettyCash += parseFloat(currentRow.totalPettyCash || 0);
    acc.totalDepartmentExpense += parseFloat(currentRow.totalDepartmentExpense || 0);
    acc.totalEmployeeExpense += parseFloat(currentRow.totalEmployeeExpense || 0);
    acc.estimatedMonthlyProfitLoss += parseFloat(currentRow.estimatedMonthlyProfitLoss || 0);
    acc.monthlyProfitLoss += parseFloat(currentRow.monthlyProfitLoss || 0);
    return acc;
  }, {
    estimatedPerformance: 0,
    actualPerformance: 0,
    totalSalary: 0,
    totalPettyCash: 0,
    totalDepartmentExpense: 0,
    totalEmployeeExpense: 0,
    estimatedMonthlyProfitLoss: 0,
    monthlyProfitLoss: 0,
  });

  return {
    departmentName: '总计',
    yearMonth: '-',
    ...totals
  };
});

// 组合部门统计和总计
const tableData = computed(() => {
  if (!processedMonthlyStats.value || processedMonthlyStats.value.length === 0) {
    return [totalStatistics.value]; 
  }
  return [...processedMonthlyStats.value, totalStatistics.value];
});

// 监听performanceList变化，更新统计数据
watch(() => props.performanceList, () => {
  // 当performanceList变化时，统计数据会自动更新（通过计算属性）
  // 现在依赖 statisticsData prop
}, { deep: true });

// 监听 statisticsData prop 的变化
watch(() => props.statisticsData, (newValue) => {
    // console.log('PerformanceStatistics: statisticsData changed, processed data will update', newValue);
}, { deep: true });
</script>

<template>
  <el-dialog
    :model-value="visible"
    @update:modelValue="value => emit('update:visible', value)"
    width="80vw"
    @close="handleClose"
    destroy-on-close
    class="performance-statistics-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <div class="dialog-title">部门利润统计</div>
        <div class="date-range-tag">{{ formattedDateRange }}</div>
      </div>
    </template>
    
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%;"
      :row-class-name="row => row.departmentName === '总计' ? 'total-row' : ''"
      v-loading="loading"
    >
      <el-table-column
        label="年月"
        prop="yearMonth"
        min-width="120"
        align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' && row.yearMonth === '-' }">
            {{ row.yearMonth }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="部门"
        prop="departmentName"
        min-width="180"
        align="center"
        show-overflow-tooltip
      />
      
      <el-table-column
        label="本月预估业绩"
        prop="estimatedPerformance"
        min-width="150"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' }">
            {{ formatCurrency(row.estimatedPerformance) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="本月实际业绩"
        prop="actualPerformance"
        min-width="150"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' }">
            {{ formatCurrency(row.actualPerformance) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="本月发布工资"
        prop="totalSalary"
        min-width="150"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' }">
            {{ formatCurrency(row.totalSalary) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="本月备用金"
        prop="totalPettyCash"
        min-width="150"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' }">
            {{ formatCurrency(row.totalPettyCash) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="本月部门总开销"
        prop="totalDepartmentExpense"
        min-width="160"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' }">
            {{ formatCurrency(row.totalDepartmentExpense) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="本月员工总费用"
        prop="totalEmployeeExpense"
        min-width="160"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计' }">
            {{ formatCurrency(row.totalEmployeeExpense) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="本月预计盈亏"
        prop="estimatedMonthlyProfitLoss"
        min-width="160"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计', 'bold-profit-loss': true }">
            {{ formatCurrency(row.estimatedMonthlyProfitLoss) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="本月实际盈亏"
        prop="monthlyProfitLoss"
        min-width="160"
        align="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :class="{ 'total-cell': row.departmentName === '总计', 'bold-profit-loss': true }">
            {{ formatCurrency(row.monthlyProfitLoss) }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 无数据提示 -->
    <el-empty v-if="!loading && (!statisticsData || statisticsData.length === 0)" description="暂无业绩统计数据" />
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  width: 100%;
}

.dialog-title {
  font-size: 18px;
  font-weight: 700;
  color: #303133;
}

.date-range-tag {
  background-color: #ecf5ff;
  color: #409eff;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 10px;
  font-weight: 500;
}

.total-row {
  background-color: #fdf6ec !important;
  font-weight: bold;
}

.total-cell {
  color: #f56c6c;
  font-weight: bold;
}

.bold-profit-loss {
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table__row:hover) {
  background-color: #ecf5ff !important;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__header) {
  padding: 20px;
  margin-right: 0;
}
</style> 
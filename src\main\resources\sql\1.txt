-- 在创建数据库之前，先删除数据库
DROP DATABASE IF EXISTS `management_sys`;

-- 创建公司管理系统数据库
CREATE DATABASE IF NOT EXISTS `management_sys` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用公司管理系统数据库
USE `management_sys`;

-- 创建部门表
CREATE TABLE IF NOT EXISTS `department`
(
    `department_id`          INT         NOT NULL AUTO_INCREMENT,                                        -- 部门ID
    `department_name`        VARCHAR(50) NOT NULL,                                                       -- 部门名称
    `leader_id`              INT,                                                                        -- 部门负责人ID
    `department_description` VARCHAR(255),                                                               -- 部门描述
    `parent_department_id`   INT,                                                                        -- 上级部门ID
    `status`                 VARCHAR(10) NOT NULL DEFAULT 'Active',                                      -- 状态（Active/Inactive）
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`department_id`),
    FOREIGN KEY (`parent_department_id`) REFERENCES `department` (`department_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
-- 部门表

-- 创建职位表
CREATE TABLE IF NOT EXISTS `position`
(
    `position_id`          INT         NOT NULL AUTO_INCREMENT,                                        -- 职位ID
    `position_name`        VARCHAR(50) NOT NULL,                                                       -- 职位名称
    `position_description` VARCHAR(255),                                                               -- 职位描述
    `department_id`        INT,                                                                        -- 所属部门ID
    `status`               VARCHAR(10) NOT NULL DEFAULT 'Active',                                      -- 状态（Active/Inactive）
    `create_time`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`position_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
-- 职位表

-- 创建员工表
CREATE TABLE IF NOT EXISTS `employee`
(
    `employee_id`     INT          NOT NULL AUTO_INCREMENT,                                                                     -- 员工ID
    `name`            VARCHAR(50)  NOT NULL,                                                                                    -- 姓名
    `email`           VARCHAR(100) NOT NULL,                                                                                    -- 邮箱（登录账号）
    `password`        VARCHAR(100) NOT NULL,                                                                                    -- 密码（MD5加密）
    `entry_date`      DATE         NOT NULL,                                                                                    -- 入职时间
    `exit_date`       DATE         NULL,                                                                                        -- 离职时间
    `id_card`         VARCHAR(18)  NOT NULL,                                                                                    -- 身份证号
    `department_id`   INT,                                                                                                      -- 部门ID
    `position_id`     INT,                                                                                                      -- 职位ID
    `logistics_route` VARCHAR(100),                                                                                             -- 所属物流航线
    `status`          VARCHAR(10) GENERATED ALWAYS AS (CASE WHEN `exit_date` IS NULL THEN 'Active' ELSE 'Inactive' END) STORED, -- 工作状态
    `role`            VARCHAR(20)  NOT NULL DEFAULT 'employee',                                                                 -- 角色（admin/manager/employee）
    `create_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,                                                          -- 创建时间
    `update_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,                              -- 更新时间
    PRIMARY KEY (`employee_id`),
    UNIQUE KEY `uk_email` (`email`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`),
    FOREIGN KEY (`position_id`) REFERENCES `position` (`position_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 为部门表添加外键约束，关联员工表
ALTER TABLE `department`
    ADD CONSTRAINT `fk_department_leader`
        FOREIGN KEY (`leader_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE SET NULL ON UPDATE CASCADE;

-- 创建业绩表
CREATE TABLE IF NOT EXISTS `performance`
(
    `id`                    INT            NOT NULL AUTO_INCREMENT, -- 业绩记录ID
    `employee_id`           INT            NOT NULL,                -- 员工ID
    `date`                  VARCHAR(7)     NOT NULL,                -- 日期（年月）
    `estimated_performance` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 预估业绩
    `actual_performance`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实际业绩
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 创建工资表
CREATE TABLE IF NOT EXISTS `salary`
(
    `id`                INT            NOT NULL AUTO_INCREMENT, -- 工资记录ID
    `employee_id`       INT            NOT NULL,                -- 员工ID
    `date`              VARCHAR(7)     NOT NULL,                -- 日期（年月）
    `basic_salary`      DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 基本工资
    `performance_bonus` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 绩效奖金
    `overtime_pay`      DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 加班费
    `deduction`         DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 扣款
    `total_salary`      DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 总工资
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 客户表
CREATE TABLE IF NOT EXISTS `client`
(
    `client_id`   INT          NOT NULL AUTO_INCREMENT,                                        -- 客户ID
    `name`        VARCHAR(50)  NOT NULL,                                                       -- 客户名称
    `employee_id` INT,                                                                         -- 员工ID
    `email`       VARCHAR(100) NOT NULL,                                                       -- 客户邮箱
    `phone`       VARCHAR(20)  NOT NULL,                                                       -- 客户电话
    `create_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`client_id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 更新部门负责人ID
UPDATE `department`
SET `leader_id` = 1
WHERE `department_id` = 1;
UPDATE `department`
SET `leader_id` = 2
WHERE `department_id` = 2;
UPDATE `department`
SET `leader_id` = 3
WHERE `department_id` = 3;


-- 将部门负责人设置为 manager 角色
UPDATE `employee`
SET `role` = 'manager'
WHERE `employee_id` IN (SELECT `leader_id`
                        FROM `department`
                        WHERE `leader_id` IS NOT NULL);


-- 客户表添加客户分类（海运、散货、空运、快递等）与合作状态（审核中、已拒绝、报价中、已合作）
ALTER TABLE `client`
    ADD COLUMN `category` VARCHAR(20) NOT NULL DEFAULT '海运' AFTER `employee_id`,
    ADD COLUMN `status`   VARCHAR(20) NOT NULL DEFAULT '审核中' AFTER `category`;


-- 创建职位与部门的关联表
-- 使用反引号（`）来转义可能与关键字冲突的表名和列名
use company_management_system;
CREATE TABLE IF NOT EXISTS `position_department`
(
    `position_id`   INT NOT NULL,
    `department_id` INT NOT NULL,
    `create_time`   DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`position_id`, `department_id`),
    -- 确保引用的表名 `position` 和 `department` 也被反引号括起来
    CONSTRAINT `fk_position_department_position` FOREIGN KEY (`position_id`) REFERENCES `position` (`position_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_position_department_department` FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE
);

-- 迁移现有职位的部门关系到新表
-- 同样，在 INSERT 语句中也使用反引号
INSERT INTO `position_department` (`position_id`, `department_id`, `create_time`)
SELECT `position_id`, `department_id`, NOW()
FROM `position`
WHERE `department_id` IS NOT NULL;
#
# ALTER TABLE `position`
#     DROP FOREIGN KEY `position_ibfk_1`;
# 然后删除列
ALTER TABLE `position`
    DROP COLUMN `department_id`;

-- 创建备用金表
CREATE TABLE IF NOT EXISTS `petty_cash`
(
    `id`          INT            NOT NULL AUTO_INCREMENT,                                        -- 备用金ID
    `employee_id` INT            NOT NULL,                                                       -- 员工ID
    `purpose`     VARCHAR(255)   NOT NULL,                                                       -- 用途
    `amount`      DECIMAL(12, 2) NOT NULL,                                                       -- 金额
    `status`      VARCHAR(20)    NOT NULL DEFAULT '审核中',                                      -- 状态（审核中、已审核、已拒绝）
    `create_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 客户表添加操作时间与国籍字段
ALTER TABLE `client`
    ADD COLUMN `operation_time` DATETIME AFTER `status`,
    ADD COLUMN `nationality`    VARCHAR(20) NOT NULL AFTER `operation_time`;

-- 修改客户表中电话的长度为100
ALTER TABLE `client`
    MODIFY COLUMN `phone` VARCHAR(100) NOT NULL;

-- 删除`overtime_pay`  -- 加班费
ALTER TABLE `salary`
    DROP COLUMN `overtime_pay`;
-- 添加工资表提成、其他、备注字段
ALTER TABLE `salary`
    ADD COLUMN `commission` DECIMAL(12, 2) NOT NULL DEFAULT 0 AFTER `performance_bonus`, -- 提成
    ADD COLUMN `other`      DECIMAL(12, 2) NOT NULL DEFAULT 0 AFTER `deduction`, -- 其他
    ADD COLUMN `remark`     VARCHAR(255)   NOT NULL DEFAULT '' AFTER `other`;
-- 备注

-- 添加员工表可访问菜单ID字段，在role字段后面
ALTER TABLE `employee`
    ADD COLUMN `accessible_menu_ids` JSON AFTER `role`;




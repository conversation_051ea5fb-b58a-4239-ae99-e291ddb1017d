package org.example.company_management.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.entity.Department;
import org.example.company_management.entity.Position;
import org.example.company_management.validation.ValidationGroups;

import java.util.Date;

/**
 * 员工数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeDTO {
    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空", groups = {ValidationGroups.Update.class})
    private Integer employeeId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(min = 2, max = 50, message = "姓名长度必须在2-50个字符之间", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String name;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String email;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String phone;

    /**
     * 密码
     * 密码必须至少8位，包含大小写字母和数字
     * 在编辑模式下可以为空（表示不修改密码）
     */
    @NotBlank(message = "密码不能为空", groups = {ValidationGroups.Add.class})
    @Pattern(
            regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$",
            message = "密码必须至少8位，包含大小写字母和数字",
            groups = {ValidationGroups.Add.class, ValidationGroups.Update.class}
    )
    private String password;

    /**
     * 入职时间
     */
    @NotNull(message = "入职时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private Date entryDate;

    /**
     * 离职时间
     */
    private Date exitDate;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String idCard;

    /**
     * 部门ID
     */
    @NotNull(message = "部门不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private Integer departmentId;

    /**
     * 部门名称（非数据库字段）
     */
    private String departmentName;

    /**
     * 职位ID
     */
    @NotNull(message = "职位不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private Integer positionId;

    /**
     * 职位名称（非数据库字段）
     */
    private String positionName;

    /**
     * 所属物流航线
     */
    private String logisticsRoute;

    /**
     * 工作状态
     */
    private String status;

    /**
     * 角色（ADMIN/USER）
     */
    @NotBlank(message = "角色不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String role;

    /**
     * 可访问菜单ID
     */
    private String accessibleMenuIdsJson;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 部门信息（非数据库字段）
     */
    private Department department;

    /**
     * 职位信息（非数据库字段）
     */
    private Position position;
} 
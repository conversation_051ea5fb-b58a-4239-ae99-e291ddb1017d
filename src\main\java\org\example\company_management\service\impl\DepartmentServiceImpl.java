package org.example.company_management.service.impl;

import org.example.company_management.entity.Department;
import org.example.company_management.entity.Position;
import org.example.company_management.mapper.DepartmentMapper;
import org.example.company_management.service.DepartmentService;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.service.PositionService;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 部门服务实现
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private PositionService positionService;

    @Override
    public List<Department> list() {
        return departmentMapper.selectAll();
    }

    @Override
    public PageResult<Department> pageList(int pageNum, int pageSize) {
        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 查询数据
        List<Department> departments = departmentMapper.selectByPage(offset, pageSize);

        // 查询总数
        int total = departmentMapper.countTotal();

        // 封装返回结果
        return new PageResult<>(pageNum, pageSize, total, departments);
    }

    @Override
    public PageResult<Department> pageList(int pageNum, int pageSize, String departmentName, Integer leaderId, String leaderName, Integer parentDepartmentId) {
        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 查询数据
        List<Department> departments = departmentMapper.selectByPageAndCondition(
                offset, pageSize, departmentName, leaderId, leaderName, parentDepartmentId);

        // 查询总数
        int total = departmentMapper.countTotalByCondition(departmentName, leaderId, leaderName, parentDepartmentId);

        // 封装返回结果
        return new PageResult<>(pageNum, pageSize, total, departments);
    }

//    /**
//     * 兼容旧版本的方法，调用新版本实现
//     */
//    @Override
//    public PageResult<Department> pageList(int pageNum, int pageSize, String departmentName, Integer leaderId, Integer parentDepartmentId) {
//        return pageList(pageNum, pageSize, departmentName, leaderId, null, parentDepartmentId);
//    }

    @Override
    public List<Department> search(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果关键词为空，返回所有部门
            return departmentMapper.selectAll();
        }
        return departmentMapper.selectByKeyword(keyword.trim());
    }

    @Override
    public Department getById(Integer id) {
        return departmentMapper.selectById(id);
    }

    @Override
    public void add(Department department) {
        // 校验部门名称
        if (department == null || !StringUtils.hasText(department.getDepartmentName())) {
            throw new IllegalArgumentException("部门名称不能为空");
        }

        // 检查部门名称是否已存在
        List<Department> departments = departmentMapper.selectAll();
        for (Department existingDept : departments) {
            if (existingDept.getDepartmentName().equals(department.getDepartmentName())) {
                throw new IllegalArgumentException("部门名称已存在，请使用其他名称");
            }
        }

        // 设置默认状态
        if (!StringUtils.hasText(department.getStatus())) {
            department.setStatus("Active");
        }

        // 设置创建和更新时间
        Date now = new Date();
        department.setCreateTime(now);
        department.setUpdateTime(now);

        // 保存部门
        departmentMapper.insert(department);

        // 注意：已删除创建部门经理职位和更新员工部门职位的代码
        // 根据需求，部门负责人只负责该部门，但不需要调整其部门归属和职位
    }

    @Override
    public void update(Department department) {
        // 校验部门名称
        if (department == null || !StringUtils.hasText(department.getDepartmentName())) {
            throw new IllegalArgumentException("部门名称不能为空");
        }

        // 检查部门ID是否存在
        Department existingDept = departmentMapper.selectById(department.getDepartmentId());
        if (existingDept == null) {
            throw new IllegalArgumentException("部门不存在");
        }

        // 检查部门名称是否与其他部门重复
        List<Department> departments = departmentMapper.selectAll();
        for (Department dept : departments) {
            if (dept.getDepartmentName().equals(department.getDepartmentName()) &&
                    !dept.getDepartmentId().equals(department.getDepartmentId())) {
                throw new IllegalArgumentException("部门名称已存在，请使用其他名称");
            }
        }

        // 设置更新时间
        department.setUpdateTime(new Date());

        // 更新部门
        departmentMapper.update(department);

        // 注意：已删除当部门负责人变更时创建/更新部门经理职位和更新员工部门职位的代码
        // 根据需求，部门负责人只负责该部门，但不需要调整其部门归属和职位
    }

    @Override
    public void delete(Integer id) {
        // 校验部门ID
        if (id == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }

        // 检查部门是否存在
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            throw new IllegalArgumentException("部门不存在，无法删除");
        }

        // 检查部门名称，防止删除"待分配"部门
        if ("待分配".equals(department.getDepartmentName())) {
            throw new IllegalArgumentException("无法删除'待分配'部门，因为它用于存放未分配员工");
        }

        // 检查是否有下级部门
        List<Department> subDepartments = departmentMapper.selectByParentId(id);
        if (subDepartments != null && !subDepartments.isEmpty()) {
            throw new IllegalArgumentException("该部门下有" + subDepartments.size() + "个下级部门，请先转移或删除这些下级部门后再删除");
        }

        // 检查部门下是否有员工
        if (department.getEmployeeCount() != null && department.getEmployeeCount() > 0) {
            throw new IllegalArgumentException("该部门下有" + department.getEmployeeCount() + "名员工，请先转移或删除这些员工后再删除部门");
        }

        // 检查部门下是否有职位
        List<Position> positions = positionService.getByDepartmentId(id);
        if (positions != null && !positions.isEmpty()) {
            throw new IllegalArgumentException("该部门下有" + positions.size() + "个职位，请先转移或删除这些职位后再删除部门");
        }

        // 执行删除操作
        int result = departmentMapper.deleteById(id);
        if (result <= 0) {
            throw new RuntimeException("删除部门失败，请稍后重试");
        }
    }

    /**
     * 获取或创建"待分配"部门
     *
     * @return 待分配部门ID
     */
    private Integer getOrCreateUnassignedDepartment() {
        // 查找是否已存在"待分配"部门
        List<Department> departments = departmentMapper.selectAll();
        for (Department dept : departments) {
            if ("待分配".equals(dept.getDepartmentName())) {
                return dept.getDepartmentId();
            }
        }

        // 如果不存在，创建一个
        Department unassignedDept = new Department();
        unassignedDept.setDepartmentName("待分配");
        unassignedDept.setLeaderId(null); // 使用null表示没有负责人
        unassignedDept.setDepartmentDescription("存放暂未分配部门的员工");
        unassignedDept.setStatus("Active");

        // 设置创建和更新时间
        Date now = new Date();
        unassignedDept.setCreateTime(now);
        unassignedDept.setUpdateTime(now);

        // 保存部门
        departmentMapper.insert(unassignedDept);

        return unassignedDept.getDepartmentId();
    }

    @Override
    public List<Department> getSubDepartments(Integer parentId) {
        return departmentMapper.selectByParentId(parentId);
    }

    @Override
    public List<Department> getDepartmentsByLeaderId(Integer leaderId) {
        if (leaderId == null) {
            return new ArrayList<>();
        }
        return departmentMapper.selectByLeaderId(leaderId);
    }
} 
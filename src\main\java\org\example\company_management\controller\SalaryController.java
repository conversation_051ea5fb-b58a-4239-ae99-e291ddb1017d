package org.example.company_management.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.SalaryImportDTO;
import org.example.company_management.entity.Salary;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.service.SalaryService;
import org.example.company_management.utils.ExcelUtil;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工资管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/salary")
public class SalaryController {

    @Autowired
    private SalaryService salaryService;

    /**
     * 分页查询工资记录
     *
     * @param pageNum      页码
     * @param pageSize     每页记录数
     * @param employeeName 员工姓名（可选）
     * @param departmentId 部门ID（可选）
     * @param yearMonth    年月（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<Map<String, Object>> getSalaryPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) String yearMonth) {
        PageInfo<Salary> pageInfo = salaryService.getSalaryPage(page, size, employeeName, departmentId, yearMonth);

        // 只返回必要的数据：列表和总条数
        Map<String, Object> result = new HashMap<>();
        result.put("list", pageInfo.getList());
        result.put("total", pageInfo.getTotal());

        return Result.success(result);
    }

    /**
     * 根据ID查询工资记录
     *
     * @param id 工资记录ID
     * @return 工资记录
     */
    @GetMapping("/{id}")
    public Result<Salary> getSalaryById(@PathVariable Integer id) {
        Salary salary = salaryService.getSalaryById(id);
        if (salary == null) {
            return Result.error("未找到对应的工资记录");
        }
        return Result.success(salary);
    }

    /**
     * 查询员工工资记录
     *
     * @param employeeId 员工ID
     * @return 工资记录列表
     */
    @GetMapping("/employee/{employeeId}")
    public Result<List<Salary>> getSalariesByEmployeeId(@PathVariable Integer employeeId) {
        List<Salary> salaries = salaryService.getSalariesByEmployeeId(employeeId);
        return Result.success(salaries);
    }

    /**
     * 查询当前登录员工的工资记录
     *
     * @param page 页码，默认为1
     * @param size 每页大小，默认为10
     * @param date 可选的日期筛选参数（年月格式：YYYY-MM）
     * @return 工资记录分页列表
     */
    @GetMapping("/employee/list")
    public Result<Map<String, Object>> getCurrentEmployeeSalaries(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String date) {
        try {
            // 打印请求信息，用于调试
            System.out.println("收到当前员工工资查询请求: page=" + page + ", size=" + size + ", date=" + date);

            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            System.out.println("ThreadLocal中的员工信息: " + (employeeInfo != null ? employeeInfo.toString() : "null"));

            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            System.out.println("当前登录员工ID: " + employeeId);

            // 获取该员工的工资记录（分页查询）
            PageInfo<Salary> pageInfo = salaryService.getSalaryPageByEmployeeId(page, size, employeeId, date);

            // 构建返回结果，与前端分页格式一致
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageInfo.getList()); // 当前页数据
            result.put("total", pageInfo.getTotal()); // 总记录数

            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取工资记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询当前登录员工特定月份的工资详情
     *
     * @param date 年月（格式：YYYY-MM）
     * @return 工资详情
     */
    @GetMapping("/employee/detail/{date}")
    public Result<Salary> getCurrentEmployeeSalaryDetail(@PathVariable String date) {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 获取该员工特定月份的工资记录
            Salary salary = salaryService.getSalaryByEmployeeIdAndDate(employeeId, date);
            if (salary == null) {
                return Result.error("未找到该月份的工资记录");
            }

            return Result.success(salary);
        } catch (Exception e) {
            return Result.error("获取工资详情失败: " + e.getMessage());
        }
    }

    /**
     * 新增工资记录
     *
     * @param salary 工资记录
     * @return 添加成功的工资记录
     */
    @PostMapping
    public Result<Salary> addSalary(@RequestBody Salary salary) {
        Salary existSalary = salaryService.getSalaryByEmployeeIdAndDate(salary.getEmployeeId(), salary.getDate());
        if (existSalary != null) {
            return Result.error("该员工当月工资记录已存在，请勿重复添加");
        }
        Salary addedSalary = salaryService.addSalary(salary);
        return Result.success(addedSalary);
    }

    /**
     * 更新工资记录
     *
     * @param salary 工资记录
     * @return 更新成功的工资记录
     */
    @PutMapping
    public Result<Salary> updateSalary(@RequestBody Salary salary) {
        Salary existSalary = salaryService.getSalaryById(salary.getId());
        if (existSalary == null) {
            return Result.error("未找到对应的工资记录");
        }

        // 检查是否修改了员工ID和日期，如果修改了需要检查是否与其他记录冲突
        if (!existSalary.getEmployeeId().equals(salary.getEmployeeId()) || !existSalary.getDate().equals(salary.getDate())) {
            Salary conflictSalary = salaryService.getSalaryByEmployeeIdAndDate(salary.getEmployeeId(), salary.getDate());
            if (conflictSalary != null && !conflictSalary.getId().equals(salary.getId())) {
                return Result.error("该员工当月工资记录已存在，请勿重复添加");
            }
        }

        Salary updatedSalary = salaryService.updateSalary(salary);
        return Result.success(updatedSalary);
    }

    /**
     * 删除工资记录
     *
     * @param id 工资记录ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteSalary(@PathVariable Integer id) {
        Salary existSalary = salaryService.getSalaryById(id);
        if (existSalary == null) {
            return Result.error("未找到对应的工资记录");
        }

        boolean success = salaryService.deleteSalary(id);
        if (success) {
            return Result.success(true);
        } else {
            return Result.error("工资记录删除失败");
        }
    }

    /**
     * 批量删除工资记录
     *
     * @param ids 工资记录ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteSalary(@RequestBody List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("未选择要删除的工资记录");
        }

        boolean success = salaryService.batchDeleteSalary(ids);
        if (success) {
            return Result.success(true);
        } else {
            return Result.error("工资记录批量删除失败");
        }
    }

    /**
     * 分页查询多个部门工资统计
     *
     * @param params 包含部门ID列表、分页信息和日期区间的参数对象
     * @return 多部门工资统计分页结果
     */
    @PostMapping("/departments/list")
    public Result<Map<String, Object>> getDepartmentMultiSalaries(@RequestBody Map<String, Object> params) {
        try {
            // 从请求参数中提取数据
            List<Integer> departmentIds = (List<Integer>) params.get("departmentIds");
            Integer page = params.get("page") != null ? Integer.valueOf(params.get("page").toString()) : 1;
            Integer size = params.get("size") != null ? Integer.valueOf(params.get("size").toString()) : 10;
            String startDate = (String) params.get("startDate");
            String endDate = (String) params.get("endDate");
            String employeeName = (String) params.get("employeeName");

            // 打印请求信息，用于调试
            System.out.println("收到多部门工资查询请求: departmentIds=" + departmentIds +
                    ", page=" + page + ", size=" + size +
                    ", startDate=" + startDate + ", endDate=" + endDate +
                    ", employeeName=" + employeeName);

            // 检查部门ID列表是否为空
            if (departmentIds == null || departmentIds.isEmpty()) {
                return Result.error("未提供有效的部门ID");
            }

            // 查询多部门工资统计信息
            PageInfo<Salary> pageInfo = salaryService.getDepartmentMultiSalaryPage(
                    departmentIds, page, size, startDate, endDate, employeeName
            );

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageInfo.getList());
            result.put("total", pageInfo.getTotal());

            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取多部门工资统计失败: " + e.getMessage());
        }
    }

    /**
     * 导入工资Excel文件
     *
     * @param file 上传的Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    public Result<ExcelUtil.ImportResult<SalaryImportDTO>> importSalaries(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("上传的文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null ||
                !(originalFilename.toLowerCase().endsWith(".xls") || originalFilename.toLowerCase().endsWith(".xlsx"))) {
            return Result.error("文件类型不支持，请上传 .xls 或 .xlsx 文件");
        }

        // 可选：文件大小校验 (例如在Service层或此处配置)
        long maxSize = 200 * 1024 * 1024; // 200MB 与前端配置一致
        if (file.getSize() > maxSize) {
            return Result.error("文件大小超过限制 (200MB)");
        }

        try {
            ExcelUtil.ImportResult<SalaryImportDTO> importResult = salaryService.importSalariesFromExcel(file);

            if (importResult.getFailureCount() > 0 || !importResult.getGeneralErrors().isEmpty()) {
                String message = String.format("工资文件导入处理完成。共解析 %d 行，成功 %d 行，失败/跳过 %d 行。",
                        importResult.getProcessedRows(),
                        importResult.getSuccessCount(),
                        importResult.getFailureCount());
                if (!importResult.getGeneralErrors().isEmpty()) {
                    message += " 通用错误: " + String.join("; ", importResult.getGeneralErrors());
                }
                // HTTP 200，但前端会根据data中的failureCount和failedRows等展示详情
                return Result.success(message, importResult);
            }

            String successMessage = String.format("工资文件导入成功。共解析 %d 行，成功导入 %d 行。",
                    importResult.getProcessedRows(),
                    importResult.getSuccessCount());
            return Result.success(successMessage, importResult);

        } catch (BusinessException be) {
            log.error("导入工资业务异常: {}", be.getMessage(), be);
            return Result.error(be.getCode() != null ? be.getCode() : 500, be.getMessage());
        } catch (Exception e) {
            log.error("导入工资文件发生意外错误", e);
            return Result.error("导入失败，发生意外错误：" + e.getMessage());
        }
    }
} 
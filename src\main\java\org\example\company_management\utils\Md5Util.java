package org.example.company_management.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * MD5工具类
 */
public class Md5Util {
    /**
     * 密码加密
     *
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String encrypt(String password) {
        try {
            // 创建MessageDigest实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将密码转换为字节数组
            byte[] passwordBytes = password.getBytes();
            // 计算MD5摘要
            byte[] digest = md.digest(passwordBytes);
            // 将字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 校验密码
     *
     * @param plainPassword     明文密码
     * @param encryptedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean match(String plainPassword, String encryptedPassword) {
        String encryptedPlainPassword = encrypt(plainPassword);
        return encryptedPlainPassword.equals(encryptedPassword);
    }

    /**
     * 增强MD5加密，加入盐值和多次迭代
     *
     * @param password   明文密码
     * @param salt       盐值
     * @param iterations 迭代次数
     * @return 加密后的密码
     */
    public static String encryptWithSalt(String password, String salt, int iterations) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将盐和密码结合
            byte[] saltedPassword = (salt + password).getBytes();
            byte[] digest = md.digest(saltedPassword);

            // 进行多次迭代加密
            for (int i = 1; i < iterations; i++) {
                md.reset();
                digest = md.digest(digest);
            }

            return Base64.getEncoder().encodeToString(digest);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new RuntimeException("密码加密失败", e);
        }
    }
} 
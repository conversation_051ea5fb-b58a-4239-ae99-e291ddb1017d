import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
	// 状态
	const token = ref(localStorage.getItem('token') || '')
	const user = ref(JSON.parse(localStorage.getItem('user') || '{}'))
	const loading = ref(false)
	const error = ref(null)

	// Getters
	const isAuthenticated = computed(() => !!token.value)

	// Actions
	function setToken(newToken) {
		token.value = newToken
		localStorage.setItem('token', newToken)
	}

	function setUser(userData) {
		user.value = userData
		localStorage.setItem('user', JSON.stringify(userData))
	}

	function setLoading(status) {
		loading.value = status
	}

	function setError(errorMessage) {
		error.value = errorMessage
	}

	function clearAuth() {
		token.value = ''
		user.value = {}
		localStorage.removeItem('token')
		localStorage.removeItem('user')
	}

	return {
		// 状态
		token,
		user,
		loading,
		error,
		// Getters
		isAuthenticated,
		// Actions
		setToken,
		setUser,
		setLoading,
		setError,
		clearAuth,
	}
})

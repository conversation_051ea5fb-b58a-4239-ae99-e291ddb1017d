<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="800px"
    @close="handleClose"
    destroy-on-close
    class="import-errors-dialog"
  >
    <div v-if="!importResult || (!importResult.generalErrors?.length && !importResult.failedRows?.length)">
      <el-empty description="暂无导入错误或警告信息" />
    </div>
    <div v-else class="errors-content">
      <div v-if="importResult.generalErrors && importResult.generalErrors.length > 0" class="general-errors-section">
        <h4>通用错误/信息：</h4>
        <el-alert
          v-for="(error, index) in importResult.generalErrors"
          :key="'gen_err_' + index"
          :title="error"
          type="error"
          :closable="false"
          show-icon
          class="error-item"
        />
      </div>

      <div v-if="importResult.failedRows && importResult.failedRows.length > 0" class="failed-rows-section">
        <h4>失败/跳过的行记录：(共 {{ importResult.failureCount }} 条)</h4>
        <el-table :data="importResult.failedRows" style="width: 100%" border max-height="300px">
          <el-table-column label="详细信息" min-width="700">
            <template #default="{ row }">
              <el-tooltip
                effect="dark"
                :content="row.errorMessage"
                placement="top"
                :disabled="!row.errorMessage || row.errorMessage.length < 30"
              >
                <div class="error-message-cell" v-html="formatErrorMessage(row.errorMessage)"></div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="importResult.successCount > 0 && (!importResult.failedRows || importResult.failedRows.length === 0) && (!importResult.generalErrors || importResult.generalErrors.length === 0)" class="success-info-section">
         <el-alert
          :title="`成功导入 ${importResult.successCount} 条记录。`"
          type="success"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { ElDialog, ElButton, ElAlert, ElTable, ElTableColumn, ElEmpty, ElTooltip } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  importResult: {
    type: Object,
    default: () => null, // { generalErrors: [], failedRows: [], failureCount: 0, successCount: 0 }
  },
  title: {
    type: String,
    default: '导入结果详情',
  },
});

const emits = defineEmits(['update:modelValue']);

const handleClose = () => {
  emits('update:modelValue', false);
};

// 格式化错误信息，主要用于v-html内的换行符
const formatErrorMessage = (message) => {
  if (!message) return '';
  // 如果message本身可能包含HTML特殊字符，直接用于v-html是OK的
  // 如果message是纯文本，并且希望在v-html中正确显示换行，则替换
  return String(message).replace(/\n/g, '<br />');
};

</script>

<style scoped>
.import-errors-dialog .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 20px;
}

.errors-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px; /* For scrollbar */
}

.general-errors-section h4,
.failed-rows-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.error-item {
  margin-bottom: 8px;
}

.failed-rows-section {
  margin-top: 20px;
}

.success-info-section {
  margin-top: 20px;
}

/* 5. CSS for the error message cell */
.error-message-cell {
  white-space: nowrap;      /* Keep text on a single line */
  overflow: hidden;         /* Hide overflowing text */
  text-overflow: ellipsis;  /* Display ellipsis for overflow */
  /* width: 100%;             Ensure it takes full cell width if needed, or rely on table layout */
  display: block; /* or inline-block, to make ellipsis work reliably with width constraints */
}

.dialog-footer {
  text-align: right;
}
</style> 
-- 在创建数据库之前，先删除数据库
DROP DATABASE IF EXISTS `company_management_system`;

-- 创建公司管理系统数据库
CREATE DATABASE IF NOT EXISTS `company_management_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用公司管理系统数据库
USE `company_management_system`;

-- 创建部门表
CREATE TABLE IF NOT EXISTS `department`
(
    `department_id`          INT         NOT NULL AUTO_INCREMENT,                                        -- 部门ID
    `department_name`        VARCHAR(50) NOT NULL,                                                       -- 部门名称
    `leader_id`              INT,                                                                        -- 部门负责人ID
    `department_description` VARCHAR(255),                                                               -- 部门描述
    `parent_department_id`   INT,                                                                        -- 上级部门ID
    `status`                 VARCHAR(10) NOT NULL DEFAULT 'Active',                                      -- 状态（Active/Inactive）
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`department_id`),
    FOREIGN KEY (`parent_department_id`) REFERENCES `department` (`department_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
-- 部门表

-- 创建职位表
CREATE TABLE IF NOT EXISTS `position`
(
    `position_id`          INT         NOT NULL AUTO_INCREMENT,                                        -- 职位ID
    `position_name`        VARCHAR(50) NOT NULL,                                                       -- 职位名称
    `position_description` VARCHAR(255),                                                               -- 职位描述
    `department_id`        INT,                                                                        -- 所属部门ID
    `status`               VARCHAR(10) NOT NULL DEFAULT 'Active',                                      -- 状态（Active/Inactive）
    `create_time`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`position_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
-- 职位表

-- 创建员工表
CREATE TABLE IF NOT EXISTS `employee`
(
    `employee_id`     INT          NOT NULL AUTO_INCREMENT,                                        -- 员工ID
    `name`            VARCHAR(50)  NOT NULL,                                                       -- 姓名
    `email`           VARCHAR(100),                                                                -- 邮箱（登录账号）
    `password`        VARCHAR(100) NOT NULL,                                                       -- 密码（MD5加密）
    `entry_date`      DATE         NOT NULL,                                                       -- 入职时间
    `exit_date`       DATE         NULL,                                                           -- 离职时间
    `id_card`         VARCHAR(18)  NOT NULL,                                                       -- 身份证号
    `department_id`   INT,                                                                         -- 部门ID
    `position_id`     INT,                                                                         -- 职位ID
    `logistics_route` VARCHAR(100),                                                                -- 所属物流航线
    `status`          VARCHAR(10)  NOT NULL DEFAULT 'Active',                                      -- 工作状态
    `role`            VARCHAR(20)  NOT NULL DEFAULT 'employee',                                    -- 角色（admin/manager/employee）
    `create_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`employee_id`),
    UNIQUE KEY `uk_email` (`email`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`),
    FOREIGN KEY (`position_id`) REFERENCES `position` (`position_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 为部门表添加外键约束，关联员工表
ALTER TABLE `department`
    ADD CONSTRAINT `fk_department_leader`
        FOREIGN KEY (`leader_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE SET NULL ON UPDATE CASCADE;

-- 创建业绩表
CREATE TABLE IF NOT EXISTS `performance`
(
    `id`                    INT            NOT NULL AUTO_INCREMENT, -- 业绩记录ID
    `employee_id`           INT            NOT NULL,                -- 员工ID
    `date`                  VARCHAR(7)     NOT NULL,                -- 日期（年月）
    `estimated_performance` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 预估业绩
    `actual_performance`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实际业绩
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 创建工资表
CREATE TABLE IF NOT EXISTS `salary`
(
    `id`                       INT            NOT NULL AUTO_INCREMENT, -- 工资记录ID
    `employee_id`              INT            NOT NULL,                -- 员工ID
    `date`                     VARCHAR(7)     NOT NULL,                -- 日期（年月）
    `basic_salary`             DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 基本工资
    `performance_bonus`        DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 绩效奖金
    `full_attendance_bonus`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 全勤奖金
    -- 业务操作奖金
    `business_operation_bonus` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 业务操作奖金
    -- 实得金额
    `sum_salary`               DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实得金额
    -- 请假金额
    `leave_deduction`          DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 请假扣款
    -- 扣借款
    `deduction`                DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 扣款
    -- 迟到缺卡
    `late_deduction`           DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 迟到缺卡扣款
    -- 社保个人部分
    `social_security_personal` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 社保个人部分
    -- 公积金
    `provident_fund`           DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 公积金
    -- 代扣代缴个税
    `tax`                      DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 个税
    -- 水电费
    `water_electricity_fee`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 水电费
    -- 实发工资
    `actual_salary`            DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实发工资
    -- 报销
    `reimbursement`            DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 报销
    -- 私账
    `private_account`          DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 私账
    -- 合计
    `total_salary`             DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 合计
    -- 备注
    `remark`                   VARCHAR(255)   NOT NULL DEFAULT '',     -- 备注
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 客户表
CREATE TABLE IF NOT EXISTS `client`
(
    `client_id`   INT          NOT NULL AUTO_INCREMENT,                                        -- 客户ID
    `name`        VARCHAR(50)  NOT NULL,                                                       -- 客户名称
    `employee_id` INT,                                                                         -- 员工ID
    `email`       VARCHAR(100) NOT NULL,                                                       -- 客户邮箱
    `phone`       VARCHAR(20)  NOT NULL,                                                       -- 客户电话
    `create_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`client_id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 插入部门数据（注意：需要先插入员工数据，再更新部门负责人ID）
INSERT INTO `department` (`department_name`, `department_description`, `parent_department_id`, `status`)
VALUES ('总公司', '公司总部', NULL, 'Active'),
       ('财务部', '负责公司财务规划、会计核算和财务分析', 1, 'Active'),
       ('技术研发部', '负责产品研发和技术创新', 1, 'Active');

-- 插入职位数据
INSERT INTO `position` (`position_name`, `position_description`, `department_id`, `status`)
VALUES ('总经理', '负责公司整体运营和战略决策', 1, 'Active'),
       ('财务会计', '负责财务核算和报表编制', 2, 'Active'),
       ('高级工程师', '负责核心技术研发和技术难题攻关', 3, 'Active');

-- 插入员工数据
INSERT INTO `employee` (`name`, `email`, `password`, `entry_date`, `exit_date`, `id_card`, `department_id`,
                        `position_id`, `logistics_route`, `role`)
VALUES ('张伟', '<EMAIL>', MD5('password123'), '2018-05-12', NULL, '110101198001011234', 1, 1, NULL,
        'admin'),
       ('王芳', '<EMAIL>', MD5('password123'), '2019-07-15', NULL, '******************', 2, 2, NULL,
        'manager'),
       ('郑华', '<EMAIL>', MD5('password123'), '2021-11-05', NULL, '370101199409096789', 3, 3, NULL,
        'employee');

-- 更新部门负责人ID
UPDATE `department`
SET `leader_id` = 1
WHERE `department_id` = 1;
UPDATE `department`
SET `leader_id` = 2
WHERE `department_id` = 2;
UPDATE `department`
SET `leader_id` = 3
WHERE `department_id` = 3;

-- 插入业绩数据
INSERT INTO `performance` (`employee_id`, `date`, `estimated_performance`, `actual_performance`)
VALUES (1, '2023-01', 100000.00, 120000.00),
       (2, '2023-01', 90000.00, 95000.00),
       (3, '2023-01', 70000.00, 75000.00);

-- 插入客户数据
INSERT INTO `client` (`name`, `email`, `phone`, `employee_id`)
VALUES ('张三', '<EMAIL>', '12345678901', 1);

-- 将部门负责人设置为 manager 角色
UPDATE `employee`
SET `role` = 'manager'
WHERE `employee_id` IN (SELECT `leader_id`
                        FROM `department`
                        WHERE `leader_id` IS NOT NULL);


-- 客户表添加客户分类（海运、散货、空运、快递等）与合作状态（未审核，审核中、审核通过，已拒绝）
ALTER TABLE `client`
    ADD COLUMN `category` VARCHAR(20) NOT NULL DEFAULT '海运' AFTER `employee_id`,
#     审核状态
    ADD COLUMN `status`   VARCHAR(20) NOT NULL DEFAULT '未审核' AFTER `category`;

alter table `client`
#     客户合作状态（报价中，已合作）
    ADD COLUMN `client_status` VARCHAR(20) NOT NULL DEFAULT '报价中' AFTER `status`;


-- 创建职位与部门的关联表
-- 使用反引号（`）来转义可能与关键字冲突的表名和列名
use company_management_system;
CREATE TABLE IF NOT EXISTS `position_department`
(
    `position_id`   INT NOT NULL,
    `department_id` INT NOT NULL,
    `create_time`   DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`position_id`, `department_id`),
    -- 确保引用的表名 `position` 和 `department` 也被反引号括起来
    CONSTRAINT `fk_position_department_position` FOREIGN KEY (`position_id`) REFERENCES `position` (`position_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_position_department_department` FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE
);

-- 迁移现有职位的部门关系到新表
-- 同样，在 INSERT 语句中也使用反引号
INSERT INTO `position_department` (`position_id`, `department_id`, `create_time`)
SELECT `position_id`, `department_id`, NOW()
FROM `position`
WHERE `department_id` IS NOT NULL;
#
# ALTER TABLE `position`
#     DROP FOREIGN KEY `position_ibfk_1`;
# 然后删除列
ALTER TABLE `position`
    DROP COLUMN `department_id`;

-- 创建备用金表
CREATE TABLE IF NOT EXISTS `petty_cash`
(
    `id`          INT            NOT NULL AUTO_INCREMENT,                                        -- 备用金ID
    `employee_id` INT            NOT NULL,                                                       -- 员工ID
    `purpose`     VARCHAR(255)   NOT NULL,                                                       -- 用途
    `amount`      DECIMAL(12, 2) NOT NULL,                                                       -- 金额
    `status`      VARCHAR(20)    NOT NULL DEFAULT '审核中',                                      -- 状态（审核中、已审核、已拒绝）
    `create_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 插入备用金示例数据
INSERT INTO `petty_cash` (`employee_id`, `purpose`, `amount`, `status`, `create_time`, `update_time`)
VALUES (2, '出差费用', 2000.00, '审核中', now(), now()),
       (3, '办公用品', 500.00, '已审核', now(), now()),
       (1, '客户招待费', 1500.00, '已拒绝', now(), now());

-- 客户表添加操作时间与国籍字段
ALTER TABLE `client`
    ADD COLUMN `operation_time` DATETIME AFTER `status`,
    ADD COLUMN `nationality`    VARCHAR(20) NOT NULL AFTER `operation_time`;

-- 修改客户表中电话的长度为100
ALTER TABLE `client`
    MODIFY COLUMN `phone` VARCHAR(100) NOT NULL;

-- 添加员工表可访问菜单ID字段，在role字段后面
ALTER TABLE `employee`
    ADD COLUMN `accessible_menu_ids` JSON AFTER `role`;

-- 客户表添加联系人与备注字段
ALTER TABLE `client`
    ADD COLUMN `contact_person` VARCHAR(50)  DEFAULT '' AFTER `name`, -- 联系人
    ADD COLUMN `remark`         VARCHAR(255) DEFAULT '' AFTER `phone`;
-- 备注

-- 客户表新增拒绝备注字段
ALTER TABLE `client`
    ADD COLUMN `reject_remark` VARCHAR(255) DEFAULT '' AFTER `remark`;

-- 员工表添加手机号字段在name字段后面
ALTER TABLE `employee`
    ADD COLUMN `phone` VARCHAR(20) AFTER `name`;


-- 删除员工表的status字段
ALTER TABLE `employee`
    DROP COLUMN `status`;
-- 重新添加status字段
ALTER TABLE `employee`
    ADD COLUMN `status` VARCHAR(10) NOT NULL DEFAULT 'Active' AFTER `logistics_route`;

-- 备用金表添加date年月字段
ALTER TABLE `petty_cash`
    ADD COLUMN `date` VARCHAR(7) NOT NULL AFTER `employee_id`;

-- 创建部门日常开销表
CREATE TABLE IF NOT EXISTS `department_expense`
(
    `id`            INT            NOT NULL AUTO_INCREMENT,                                        -- 开销ID
    `department_id` INT            NOT NULL,                                                       -- 部门ID
    `expense_date`  DATE           NOT NULL,                                                       -- 开销日期 (格式 YYYY-MM-DD)
    `item_name`     VARCHAR(255)   NOT NULL,                                                       -- 项目名称
    `amount`        DECIMAL(12, 2) NOT NULL DEFAULT 0.00,                                          -- 金额
    `remark`        VARCHAR(255)   NOT NULL DEFAULT '',                                            -- 备注
    `create_time`   DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`   DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='部门日常开销表';

-- 创建员工其他费用表
CREATE TABLE IF NOT EXISTS `employee_other_expense`
(
    `id`           INT            NOT NULL AUTO_INCREMENT,                                        -- 费用ID
    `employee_id`  INT            NOT NULL,                                                       -- 员工ID
    `expense_date` DATE           NOT NULL,                                                       -- 费用日期 (格式 YYYY-MM-DD)
    `item_name`    VARCHAR(255)   NOT NULL,                                                       -- 项目名称
    `amount`       DECIMAL(12, 2) NOT NULL DEFAULT 0.00,                                          -- 金额
    `remark`       VARCHAR(255)   NOT NULL DEFAULT '',                                            -- 备注
    `create_time`  DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`  DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='员工其他费用表';

-- 创建通知规则定义表
CREATE TABLE IF NOT EXISTS `notification_rules`
(
    `rule_id`                       INT AUTO_INCREMENT PRIMARY KEY,
    `rule_name`                     VARCHAR(100) NOT NULL UNIQUE COMMENT '规则名称，例如: LOW_PERFORMANCE_LAST_MONTH',
    `description`                   VARCHAR(255) COMMENT '规则描述',
    `target_position_name`          VARCHAR(50) COMMENT '目标职位名称，例如 "业务员", 为空则可能适用于所有职位或通过其他逻辑判断',
    `condition_type`                VARCHAR(50)  NOT NULL COMMENT '条件类型，用于后端逻辑识别，例如: PERFORMANCE_VS_SALARY, NEW_CLIENTS_LAST_MONTH, NEW_CLIENTS_THIS_MONTH_AFTER_15TH',
    `message_template_cn`           VARCHAR(500) NOT NULL COMMENT '中文通知消息模板，可使用占位符如 {userName}, {actualPerformance}, {calculatedSalary}',
    `default_show_again_after_days` INT          NOT NULL COMMENT '默认再次提示间隔天数 (例如 7天, 3天)',
    `priority`                      INT      DEFAULT 10 COMMENT '优先级，数字越小越高',
    `is_active`                     BOOLEAN  DEFAULT TRUE COMMENT '规则是否激活',
    `create_time`                   DATETIME DEFAULT CURRENT_TIMESTAMP,
    `update_time`                   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='通知规则定义表';

-- 插入通知规则示例数据
INSERT INTO `notification_rules` (`rule_name`, `description`, `target_position_name`, `condition_type`,
                                  `message_template_cn`, `default_show_again_after_days`, `is_active`)
VALUES ('LOW_PERFORMANCE_LAST_MONTH', '上月业绩低于上月应得工资', '业务员', 'PERFORMANCE_VS_SALARY',
        '您好{userName}，上个月自己工资都没有挣回来，请注意，继续努力哦！',
        30, TRUE),
       ('NO_NEW_CLIENTS_LAST_MONTH', '上月无新审批通过客户', '业务员', 'NEW_CLIENTS_LAST_MONTH',
        '您好{userName}，您上个月没有新客户，请努力寻找新客户哦！', 30, TRUE),
       ('NO_NEW_CLIENTS_THIS_MONTH_MID', '本月已过15号仍无新客户', '业务员', 'NEW_CLIENTS_THIS_MONTH_AFTER_15TH',
        '您好{userName}，这个月时间已过半，已经过去15天啦，请抓紧开发新客户哦！', 3, TRUE);

-- 创建员工通知记录表
CREATE TABLE IF NOT EXISTS `employee_notifications`
(
    `notification_id`       BIGINT AUTO_INCREMENT PRIMARY KEY,
    `employee_id`           INT           NOT NULL COMMENT '员工ID',
    `rule_id`               INT           NULL COMMENT '关联的通知规则ID (如果使用 notification_rules 表)',
    `notification_type`     VARCHAR(100)  NOT NULL COMMENT '通知类型 (如果未使用rules表，则直接存储类型key)',
    `title_cn`              VARCHAR(100)  NOT NULL DEFAULT '系统提醒' COMMENT '中文通知标题',
    `message_cn`            VARCHAR(1000) NOT NULL COMMENT '中文通知内容',
    `generated_at`          DATETIME               DEFAULT CURRENT_TIMESTAMP COMMENT '通知生成时间',
    `last_shown_at`         DATETIME      NULL COMMENT '上次弹窗显示时间',
    `dismissed_at`          DATETIME      NULL COMMENT '用户点击"我知道了"的时间',
    `show_again_after_days` INT COMMENT '本次我知道了之后，下次提示的间隔天数（可以基于规则默认值，或特定情况调整）',
    `next_prompt_date`      DATE          NULL COMMENT '根据dismissed_at和show_again_after_days计算得出的下次应提示日期',
    `is_read_in_bell`       BOOLEAN                DEFAULT FALSE COMMENT '在铃铛中是否已读',
    `status`                VARCHAR(20)            DEFAULT 'ACTIVE' COMMENT '状态: ACTIVE, DISMISSED, ARCHIVED',
    `create_time`           DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,
    FOREIGN KEY (`rule_id`) REFERENCES `notification_rules` (`rule_id`) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='员工通知记录表';


INSERT INTO `notification_rules`
(`rule_name`, `description`, `target_position_name`, `condition_type`, `message_template_cn`,
 `default_show_again_after_days`, `priority`, `is_active`)
VALUES ('SYSTEM_WELCOME_MESSAGE', '新用户欢迎通知', NULL, 'ALWAYS_TRIGGER',
        '尊敬的 {userName}，欢迎您加入我们的大家庭！希望您在这里工作愉快。', 3650, 100, TRUE);

-- 创建销售日报表
CREATE TABLE IF NOT EXISTS `sales_daily_report`
(
    `id`                         BIGINT                      NOT NULL AUTO_INCREMENT COMMENT '日报ID',
    `employee_id`                INT                         NOT NULL COMMENT '员工ID',
    `report_date`                DATE                        NOT NULL COMMENT '日报日期',

    -- 统计字段（系统自动计算）
    `yearly_new_clients`         INT                         NOT NULL DEFAULT 0 COMMENT '年度新客户总数',
    `monthly_new_clients`        INT                         NOT NULL DEFAULT 0 COMMENT '当月新客户总数',
    `days_since_last_new_client` INT                         NOT NULL DEFAULT 0 COMMENT '距离上次出新客户天数',

    -- 客户选择字段（JSON格式存储客户ID数组）
    `inquiry_clients`            JSON COMMENT '询价客户ID列表',
    `shipping_clients`           JSON COMMENT '出货客户ID列表',
    `key_development_clients`    JSON COMMENT '重点开发客户ID列表',

    -- 评估字段
    `responsibility_level`       ENUM ('优秀', '中等', '差') NOT NULL COMMENT '责任心评级',

    -- 工作检查清单（JSON格式存储）
    `end_of_day_checklist`       JSON COMMENT '下班准备工作检查清单',

    -- 文本输入字段
    `daily_results`              TEXT COMMENT '今日效果',
    `meeting_report`             TEXT COMMENT '会议报告',
    `work_diary`                 TEXT COMMENT '工作日记',

    -- 领导评价字段
    `manager_evaluation`         TEXT COMMENT '领导评价',
    `evaluation_time`            DATETIME COMMENT '评价时间',
    `evaluator_id`               INT COMMENT '评价人ID',

    -- 系统字段
    `create_time`                DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `report_date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_report_date` (`report_date`),
    INDEX `idx_employee_date` (`employee_id`, `report_date`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售日报表';

-- 添加销售日报表的领导评价相关字段（如果不存在）
-- 这些字段用于支持领导对员工日报的评价功能
ALTER TABLE `sales_daily_report`
ADD COLUMN IF NOT EXISTS `manager_evaluation` TEXT COMMENT '领导评价',
ADD COLUMN IF NOT EXISTS `evaluation_time` DATETIME COMMENT '评价时间',
ADD COLUMN IF NOT EXISTS `evaluator_id` INT COMMENT '评价人ID';

-- 为评价人ID添加外键约束（如果不存在）
-- 注意：MySQL 8.0+ 支持 IF NOT EXISTS 语法
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sales_daily_report'
    AND CONSTRAINT_NAME = 'fk_sales_daily_report_evaluator'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE `sales_daily_report` ADD CONSTRAINT `fk_sales_daily_report_evaluator` FOREIGN KEY (`evaluator_id`) REFERENCES `employee`(`employee_id`) ON DELETE SET NULL',
    'SELECT "Foreign key constraint already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;


<template>
    <div :class="$attrs.class">
        <span @click="openNotificationDialog" class="el-dropdown-link notification-bell-trigger">
            <el-badge :is-dot="notificationStore.hasUnreadBellMessage" class="item">
                <el-icon :size="22"><Bell /></el-icon>
            </el-badge>
        </span>

        <el-dialog
            v-model="notificationsDialogVisible"
            title="通知中心"
            width="600px" 
            top="10vh"
            append-to-body
            custom-class="notification-center-dialog"
            :close-on-click-modal="false"
        >
            <el-tabs v-model="activeTabInsideDialog" class="notification-tabs-dialog">
                <el-tab-pane label="未读消息" name="unread">
                    <div v-if="unreadNotifications.length === 0" class="empty-notifications-dialog">
                        暂无未读消息
                    </div>
                    <el-scrollbar v-else max-height="calc(80vh - 200px)">
                        <div
                            v-for="item in unreadNotifications"
                            :key="item.notificationId + '-unread'"
                            @click="() => openDetailDialog(item)"
                            class="notification-item-dialog"
                        >
                            <div class="notification-title-dialog">{{ item.titleCn || '系统提醒' }}</div>
                            <div class="notification-message-dialog" v-html="sanitize(item.messageCn)"></div>
                            <div class="notification-time-dialog">{{ formatTime(item.generatedAt) }}</div>
                        </div>
                    </el-scrollbar>
                </el-tab-pane>
                <el-tab-pane label="全部消息" name="all">
                    <div v-if="allSortedNotifications.length === 0" class="empty-notifications-dialog">
                        暂无任何消息
                    </div>
                    <el-scrollbar v-else max-height="calc(80vh - 200px)">
                        <div
                            v-for="item in allSortedNotifications"
                            :key="item.notificationId + '-all'"
                            @click="() => openDetailDialog(item)"
                            class="notification-item-dialog"
                            :class="{ 'is-read-in-dialog': item.isReadInBell }"
                        >
                            <div class="notification-title-dialog">{{ item.titleCn || '系统提醒' }}</div>
                            <div class="notification-message-dialog" v-html="sanitize(item.messageCn)"></div>
                            <div class="notification-time-dialog">{{ formatTime(item.generatedAt) }}</div>
                            <el-tag v-if="item.isReadInBell" size="small" type="info" effect="plain" class="read-status-tag-dialog">已读</el-tag>
                        </div>
                    </el-scrollbar>
                </el-tab-pane>
            </el-tabs>
            <template #footer>
                <el-button v-if="unreadNotifications.length > 0 && activeTabInsideDialog === 'unread'" @click="handleProcessAllUnreadInDialog">
                    <el-icon><Finished /></el-icon> 全部标记为已读
                </el-button>
                <el-button @click="notificationsDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>

        <!-- 用于显示单个通知详情的对话框 (复用之前的) -->
        <el-dialog 
            v-model="detailDialogVisible"
            :title="selectedNotification?.titleCn || '通知详情'"
            width="500px"
            append-to-body
            custom-class="notification-detail-sub-dialog"
        >
            <div v-if="selectedNotification" class="notification-detail-content">
                <p v-html="sanitize(selectedNotification.messageCn)"></p>
                <p class="time-info">接收时间: {{ formatTime(selectedNotification.generatedAt) }}</p>
                <p class="status-info" v-if="selectedNotification.isReadInBell">状态: 已读</p>
                <p class="status-info" v-else>状态: 未读</p>
            </div>
            <template #footer>
                <el-button @click="detailDialogVisible = false">关闭</el-button>
                <el-button 
                    v-if="selectedNotification && !selectedNotification.isReadInBell"
                    type="primary" 
                    @click="handleAcknowledgeNotification"
                >
                    我知道了
                </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, useAttrs, watch } from 'vue';
import { useNotificationStore } from '@/stores/notification';
import { Bell, Finished, View } from '@element-plus/icons-vue';
import DOMPurify from 'dompurify';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const attrs = useAttrs();
const notificationStore = useNotificationStore();

const notificationsDialogVisible = ref(false);
const activeTabInsideDialog = ref('unread'); 

const detailDialogVisible = ref(false);
const selectedNotification = ref(null);

watch(() => notificationStore.shouldOpenCenterOnLoginIfUnread, (newValue) => {
    if (newValue) {
        notificationsDialogVisible.value = true;
        activeTabInsideDialog.value = 'unread';
        notificationStore.setOpenedCenterOnLogin();
    }
});

const openNotificationDialog = () => {
    notificationsDialogVisible.value = true;
};

const unreadNotifications = computed(() => 
    notificationStore.bellNotifications
        .filter(n => !n.isReadInBell && n.status !== 'ARCHIVED')
        .sort((a, b) => new Date(b.generatedAt) - new Date(a.generatedAt))
);

const allSortedNotifications = computed(() => 
    [...notificationStore.bellNotifications]
        .sort((a, b) => new Date(b.generatedAt) - new Date(a.generatedAt))
);

const sanitize = (html) => DOMPurify.sanitize(html, { USE_PROFILES: { html: true } });

const formatTime = (timeStr) => {
    if (!timeStr) return '';
    try {
        return formatDistanceToNow(parseISO(timeStr), { addSuffix: true, locale: zhCN });
    } catch (e) {
        return timeStr;
    }
};

const openDetailDialog = (item) => {
    // console.log('Opening detail for:', JSON.parse(JSON.stringify(item)));
    selectedNotification.value = item;
    detailDialogVisible.value = true;
};

const handleAcknowledgeNotification = () => {
    if (selectedNotification.value) {
        notificationStore.handleDismissNotification(selectedNotification.value.notificationId);
    }
    detailDialogVisible.value = false;
};

const handleProcessAllUnreadInDialog = () => {
    const unreadIds = unreadNotifications.value.map(n => n.notificationId);
    if (unreadIds.length > 0) {
        notificationStore.handleDismissMultipleNotifications(unreadIds);
    }
    // 注意：如果store中的批量处理最终是逐条调用handleDismissNotification，
    // 并且每条都会触发fetchBellNotifications，可能会有效率问题。
    // 最理想的是后端支持真正的批量dismiss接口。
};

</script>

<style scoped>
.notification-bell-trigger {
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 100%;
}
.notification-bell-trigger .el-icon {
    color: #303133; 
}

.notification-center-dialog :deep(.el-dialog__body) {
    padding-top: 10px;
    padding-bottom: 10px;
}



.notification-tabs-dialog :deep(.el-tabs__header) {
    margin-bottom: 10px; 
}

.notification-tabs-dialog :deep(.el-tabs__nav-wrap::after) {
    display: none; 
}

.notification-item-dialog {
    padding: 12px 10px;
    line-height: 1.5;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    display: flex; 
    flex-direction: column; 
    align-items: flex-start; 
    position: relative;
}
.notification-item-dialog:last-child {
    border-bottom: none;
}

.notification-item-dialog:hover {
    background-color: #f5f7fa;
}

.notification-item-dialog.is-read-in-dialog .notification-title-dialog,
.notification-item-dialog.is-read-in-dialog .notification-message-dialog {
    color: #909399;
}

.notification-title-dialog {
    font-weight: 500;
    color: #303133;
    margin-bottom: 5px;
    font-size: 15px;
}

.notification-message-dialog {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    word-break: break-all;
}

.notification-time-dialog {
    font-size: 12px;
    color: #909399;
    align-self: flex-end;
}

.read-status-tag-dialog {
    position: absolute;
    top: 12px;
    right: 10px;
}

.empty-notifications-dialog {
    text-align: center;
    color: #909399;
    padding: 30px 0;
    font-size: 14px;
}

.notification-detail-sub-dialog :deep(.el-dialog__body) {
    padding: 20px 25px;
}

.notification-detail-content {
    line-height: 1.7;
    font-size: 14px;
}
.notification-detail-content .time-info {
    margin-top: 15px;
    font-size: 12px;
    color: #909399;
    text-align: right;
}

.status-info {
    margin-top: 10px;
    font-size: 12px;
    color: #606266;
}
</style> 
package org.example.company_management.service;

import com.github.pagehelper.PageInfo;
import org.example.company_management.dto.SalaryImportDTO;
import org.example.company_management.entity.Salary;
import org.example.company_management.utils.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 工资服务接口
 */
public interface SalaryService {
    /**
     * 查询所有工资记录
     *
     * @return 工资记录列表
     */
    List<Salary> getAllSalaries();

    /**
     * 查询员工工资记录
     *
     * @param employeeId 员工ID
     * @return 工资记录列表
     */
    List<Salary> getSalariesByEmployeeId(Integer employeeId);

    /**
     * 查询员工特定月份的工资记录
     *
     * @param employeeId 员工ID
     * @param date       日期（年月）
     * @return 工资记录
     */
    Salary getSalaryByEmployeeIdAndDate(Integer employeeId, String date);

    /**
     * 分页查询工资记录
     *
     * @param pageNum      页码
     * @param pageSize     每页记录数
     * @param employeeName 员工姓名（可选）
     * @param departmentId 部门ID（可选）
     * @param yearMonth    年月（可选）
     * @return 分页结果
     */
    PageInfo<Salary> getSalaryPage(int pageNum, int pageSize, String employeeName, Integer departmentId, String yearMonth);

    /**
     * 分页查询特定员工的工资记录
     *
     * @param pageNum    页码
     * @param pageSize   每页记录数
     * @param employeeId 员工ID
     * @param date       日期（年月，可选）
     * @return 分页结果
     */
    PageInfo<Salary> getSalaryPageByEmployeeId(Integer pageNum, Integer pageSize, Integer employeeId, String date);

    /**
     * 根据ID查询工资记录
     *
     * @param id 工资记录ID
     * @return 工资记录
     */
    Salary getSalaryById(Integer id);

    /**
     * 添加工资记录
     *
     * @param salary 工资记录
     * @return 添加成功的工资记录
     */
    Salary addSalary(Salary salary);

    /**
     * 更新工资记录
     *
     * @param salary 工资记录
     * @return 更新成功的工资记录
     */
    Salary updateSalary(Salary salary);

    /**
     * 删除工资记录
     *
     * @param id 工资记录ID
     * @return 是否删除成功
     */
    boolean deleteSalary(Integer id);

    /**
     * 批量删除工资记录
     *
     * @param ids 工资记录ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteSalary(List<Integer> ids);

    /**
     * 分页查询多个部门工资统计
     *
     * @param departmentIds 部门ID列表
     * @param pageNum       页码
     * @param pageSize      每页记录数
     * @param startDate     开始日期，格式YYYY-MM，可选
     * @param endDate       结束日期，格式YYYY-MM，可选
     * @param employeeName  员工姓名（可选）
     * @return 分页结果
     */
    PageInfo<Salary> getDepartmentMultiSalaryPage(
            List<Integer> departmentIds,
            Integer pageNum,
            Integer pageSize,
            String startDate,
            String endDate,
            String employeeName
    );

    /**
     * 从Excel文件导入工资数据。
     *
     * @param file 上传的Excel文件
     * @return 导入结果，包含成功、失败的记录和相关信息
     */
    ExcelUtil.ImportResult<SalaryImportDTO> importSalariesFromExcel(MultipartFile file);
}
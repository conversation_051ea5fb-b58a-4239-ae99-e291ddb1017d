package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Client;

import java.util.List;
import java.util.Map;

/**
 * 客户Mapper接口
 */
@Mapper
public interface ClientMapper {
    /**
     * 根据ID查询客户
     *
     * @param clientId 客户ID
     * @return 客户信息
     */
    Client selectById(Integer clientId);

    /**
     * 查询所有客户
     *
     * @return 客户列表
     */
    List<Client> selectAll();

    /**
     * 分页查询客户
     *
     * @param params 查询参数，包含offset, pageSize, name, email, phone, employeeId
     * @return 客户列表
     */
    List<Client> selectByPage(Map<String, Object> params);

    /**
     * 获取客户总数
     *
     * @param params 查询参数，包含name, email, phone, employeeId
     * @return 客户总数
     */
    int countTotal(Map<String, Object> params);

    /**
     * 新增客户
     *
     * @param client 客户信息
     * @return 影响行数
     */
    int insert(Client client);

    /**
     * 修改客户
     *
     * @param client 客户信息
     * @return 影响行数
     */
    int update(Client client);

    /**
     * 删除客户
     *
     * @param clientId 客户ID
     * @return 影响行数
     */
    int deleteById(Integer clientId);

    /**
     * 根据负责员工查询客户
     *
     * @param employeeId 员工ID
     * @return 客户列表
     */
    List<Client> selectByEmployee(Integer employeeId);

    /**
     * 根据名称查询客户
     *
     * @param name 客户名称
     * @return 客户列表
     */
    List<Client> selectByName(String name);

    /**
     * 根据邮箱查询客户
     *
     * @param email 客户邮箱
     * @return 客户信息
     */
    Client selectByEmail(String email);

    /**
     * 根据电话查询客户
     *
     * @param phone 客户电话
     * @return 客户信息
     */
    Client selectByPhone(String phone);

    /**
     * 分页查询当前登录用户负责的客户
     *
     * @param params 查询参数，包含offset, pageSize, name, status, employeeId
     * @return 客户列表
     */
    List<Client> selectMyClientsByPage(Map<String, Object> params);

    List<Client> selectOtherClientsByName(String name);

    /**
     * 获取当前登录用户负责的客户总数
     *
     * @param params 查询参数，包含name, status, employeeId
     * @return 客户总数
     */
    int countMyClientsTotal(Map<String, Object> params);

    /**
     * 获取待审批的客户列表（状态为"审核中"的客户）
     *
     * @return 待审批客户列表
     */
    List<Client> selectPendingClients();

    /**
     * 更新客户状态
     *
     * @param params 参数，包含clientId和status
     * @return 影响行数
     */
    int updateClientStatus(Map<String, Object> params);

    /**
     * 批量更新客户状态
     *
     * @param params 参数，包含clientIds列表和status
     * @return 影响行数
     */
    int batchUpdateClientStatus(Map<String, Object> params);

    /**
     * 获取待审批的客户数量（状态为"审核中"的客户数量）
     *
     * @return 待审批客户数量
     */
    int countPendingClients();

    /**
     * 根据部门ID列表查询客户（分页）
     * @param params 查询参数，包含departmentIds, start, size, clientName, category, status
     * @return 客户列表
     */
    List<Client> selectClientsByDepartments(Map<String, Object> params);

    /**
     * 根据部门ID列表获取客户总数
     * @param params 查询参数，包含departmentIds, clientName, category, status
     * @return 客户总数
     */
    int countClientsByDepartments(Map<String, Object> params);

   

    /**
     * 根据员工ID、状态和创建时间范围统计客户数量。
     * @param employeeId 员工ID
     * @param status 客户状态 (e.g., "已合作")
     * @param startTimeInclusive 创建时间的开始（包含）
     * @param endTimeExclusive 创建时间的结束（不包含）
     * @return 符合条件的客户数量
     */
    long countByEmployeeIdAndStatusAndCreateTimeBetween(
            @Param("employeeId") Integer employeeId,
            @Param("status") String status,
            @Param("startTimeInclusive") String startTimeInclusive, // LocalDateTime formatted as String
            @Param("endTimeExclusive") String endTimeExclusive     // LocalDateTime formatted as String
    );

    /**
     * 根据员工ID、审核状态列表和创建时间范围统计客户数量（用于通知系统）
     * @param employeeId 员工ID
     * @param statusList 审核状态列表 (e.g., ["审核中", "审核通过"])
     * @param startTimeInclusive 创建时间的开始（包含）
     * @param endTimeExclusive 创建时间的结束（不包含）
     * @return 符合条件的客户数量
     */
    long countByEmployeeIdAndApprovalStatusAndCreateTimeBetween(
            @Param("employeeId") Integer employeeId,
            @Param("statusList") List<String> statusList,
            @Param("startTimeInclusive") String startTimeInclusive,
            @Param("endTimeExclusive") String endTimeExclusive
    );

    // {{CHENGQI: 为销售日报功能添加的客户统计方法}}
    // {{CHENGQI: 任务ID: P1-LD-004}}
    // {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}

    /**
     * 统计员工年度新客户数量
     * @param params 参数，包含employeeId, year, status
     * @return 新客户数量
     */
    Integer countNewClientsByEmployeeAndYear(Map<String, Object> params);

    /**
     * 统计员工当月新客户数量
     * @param params 参数，包含employeeId, year, month, status
     * @return 新客户数量
     */
    Integer countNewClientsByEmployeeAndMonth(Map<String, Object> params);

    /**
     * 计算距离上次新客户的天数
     * @param params 参数，包含employeeId, status
     * @return 天数，如果没有新客户则返回null
     */
    Integer calculateDaysSinceLastNewClient(Map<String, Object> params);
}
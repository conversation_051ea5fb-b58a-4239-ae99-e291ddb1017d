<script setup>
import { ref, reactive, onMounted, computed, markRaw, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/token';
import { useNotificationStore } from '@/stores/notification';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Menu as IconMenu,
    User,
    Briefcase,
    ArrowDown,
    DataAnalysis,
    Money,
    HomeFilled,
    Edit,
    PieChart,
    Coin,
    Wallet,
    Tickets,
    CollectionTag,
    List,
    DocumentCopy,
    Document,
    Notebook,
    FolderOpened,
} from '@element-plus/icons-vue';
import { getEmployeeInfo, logout, updateProfile } from '@/api/auth';
import NotificationBell from '@/components/NotificationBell.vue';
// import NotificationPopup from '@/components/NotificationPopup.vue'; // 不再直接使用

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const user = ref(null);
const isCollapse = ref(false);
const activeMenu = ref('');
const profileFormRef = ref(null);

// 面包屑导航相关
const breadcrumbs = ref([]);

// 个人信息弹窗相关
const profileDialogVisible = ref(false);
const isEditing = ref(false);
const profileForm = reactive({
    name: '',
    email: '',
    phone: '',
    idCard: '',
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    departmentName: '',
    positionName: '',
    entryDate: '',
    status: '',
    logisticsRoute: '',
});

// 表单验证规则
const profileRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] },
    ],
    oldPassword: [
        {
            validator: (rule, value, callback) => {
                // 如果新密码有值，原密码必须填写
                if (profileForm.newPassword && !value) {
                    callback(new Error('修改密码时需要输入原密码'));
                }
                // 如果原密码有值，新密码必须填写
                else if (value && !profileForm.newPassword) {
                    callback(new Error('请输入新密码'));
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
    newPassword: [
        {
            validator: (rule, value, callback) => {
                // 如果原密码有值，新密码必须填写
                if (profileForm.oldPassword && !value) {
                    callback(new Error('请输入新密码'));
                }
                // 如果新密码有值，验证密码规则
                else if (value) {
                    // 密码长度至少8位
                    if (value.length < 8) {
                        callback(new Error('密码长度至少为8位'));
                        return;
                    }
                    // 必须包含大小写字母和数字
                    const hasUpperCase = /[A-Z]/.test(value);
                    const hasLowerCase = /[a-z]/.test(value);
                    const hasNumber = /\d/.test(value);

                    if (!hasUpperCase || !hasLowerCase || !hasNumber) {
                        callback(new Error('密码必须包含大小写字母和数字'));
                        return;
                    }

                    // 如果原密码没有值，不允许修改密码
                    if (!profileForm.oldPassword) {
                        callback(new Error('请先输入原密码'));
                        return;
                    }
                }
                // 当新密码变化时，验证确认密码
                if (profileForm.confirmPassword) {
                    profileFormRef.value?.validateField('confirmPassword');
                }
                callback();
            },
            trigger: 'blur',
        },
    ],
    confirmPassword: [
        {
            validator: (rule, value, callback) => {
                if (profileForm.newPassword && !value) {
                    callback(new Error('请确认新密码'));
                } else if (value !== profileForm.newPassword) {
                    callback(new Error('两次输入的密码不一致'));
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
};

// 基础菜单项 - 不再直接暴露，移入 computed
const baseMenuItems = [
    {
        id: 'home',
        title: '首页',
        icon: markRaw(HomeFilled),
        path: '/dashboard/home',
        isMenuItem: true,
    },
    {
        id: 'client',
        title: '客户管理',
        icon: markRaw(User),
        path: '/dashboard/client',
        isMenuItem: true,
    },
    {
        id: 'salesReport',
        title: '我的日报',
        icon: markRaw(Document),
        path: '/dashboard/sales-report',
        isMenuItem: true,
    },
    {
        id: 'pettyCash',
        title: '备用金管理',
        icon: markRaw(Wallet),
        path: '/dashboard/petty-cash',
        isMenuItem: true,
    },
    {
        id: 'performance',
        title: '业绩查询',
        icon: markRaw(DataAnalysis),
        path: '/dashboard/performance',
        isMenuItem: true,
    },
    {
        id: 'salary',
        title: '工资查询',
        icon: markRaw(Money),
        path: '/dashboard/salary',
        isMenuItem: true,
    },
];

// 角色菜单权限映射
const roleMenuPermissions = {
    'admin': ['home', 'client', 'salesReport', 'departmentReports', 'pettyCash', 'performance', 'salary', 'departmentEmployees', 'departmentPerformance', 'departmentSalary', 'departmentClients', 'departmentPettyCash', 'departmentExpense', 'employeeExpense'],
    'manager': ['home', 'client', 'salesReport', 'departmentReports', 'pettyCash', 'performance', 'salary', 'departmentEmployees', 'departmentPerformance', 'departmentSalary', 'departmentClients', 'departmentPettyCash', 'departmentExpense', 'employeeExpense'],
    'employee': ['home', 'client', 'salesReport', 'pettyCash', 'performance', 'salary']
};

// 部门管理菜单项
const departmentEmployeesMenuItem = {
    id: 'departmentEmployees',
    title: '部门员工',
    icon: markRaw(Briefcase),
    path: '/dashboard/department-employees',
    isMenuItem: true,
};

// 部门业绩菜单项
const departmentPerformanceMenuItem = {
    id: 'departmentPerformance',
    title: '部门业绩',
    icon: markRaw(PieChart),
    path: '/dashboard/department-performance',
    isMenuItem: true,
};

// 部门工资菜单项
const departmentSalaryMenuItem = {
    id: 'departmentSalary',
    title: '部门工资',
    icon: markRaw(Coin),
    path: '/dashboard/department-salary',
    isMenuItem: true,
};

// 部门客户菜单项
const departmentClientsMenuItem = {
    id: 'departmentClients',
    title: '部门客户',
    icon: markRaw(Tickets),
    path: '/dashboard/department-clients',
    isMenuItem: true,
};

// 部门日报菜单项
const departmentReportsMenuItem = {
    id: 'departmentReports',
    title: '部门日报',
    icon: markRaw(FolderOpened),
    path: '/dashboard/department-reports',
    isMenuItem: true,
};

// 部门备用金菜单项
const departmentPettyCashMenuItem = {
    id: 'departmentPettyCash',
    title: '部门备用金',
    icon: markRaw(CollectionTag),
    path: '/dashboard/department-petty-cash',
    isMenuItem: true,
};

// 部门开销菜单项
const departmentExpenseMenuItem = {
    id: 'departmentExpense',
    title: '部门开销',
    icon: markRaw(List),
    path: '/dashboard/department-expense',
    isMenuItem: true,
};

// 员工费用菜单项
const employeeExpenseMenuItem = {
    id: 'employeeExpense',
    title: '员工费用',
    icon: markRaw(DocumentCopy),
    path: '/dashboard/employee-expense',
    isMenuItem: true,
};

// 动态菜单项 - 使用 computed
const menuItems = computed(() => {
    const items = [...baseMenuItems]; // 复制基础菜单项
    
    // 获取当前用户角色
    const userRole = user.value?.role || 'employee';
    
    // 使用角色菜单权限映射检查是否应该显示特定菜单项
    if (roleMenuPermissions[userRole]?.includes('departmentEmployees')) {
        items.push(departmentEmployeesMenuItem);
    }
    
    // 检查是否应该显示部门业绩菜单
    if (roleMenuPermissions[userRole]?.includes('departmentPerformance')) {
        items.push(departmentPerformanceMenuItem);
    }
    
    // 检查是否应该显示部门工资菜单
    if (roleMenuPermissions[userRole]?.includes('departmentSalary')) {
        items.push(departmentSalaryMenuItem);
    }
    
    // 新增：检查是否应该显示部门客户菜单
    if (roleMenuPermissions[userRole]?.includes('departmentClients')) {
        items.push(departmentClientsMenuItem);
    }

    // 新增：检查是否应该显示部门日报菜单（放在部门客户后面）
    if (roleMenuPermissions[userRole]?.includes('departmentReports')) {
        items.push(departmentReportsMenuItem);
    }

    // 新增：检查是否应该显示部门备用金菜单
    if (roleMenuPermissions[userRole]?.includes('departmentPettyCash')) {
        items.push(departmentPettyCashMenuItem);
    }
    
    // 新增：检查是否应该显示部门开销菜单
    if (roleMenuPermissions[userRole]?.includes('departmentExpense')) {
        items.push(departmentExpenseMenuItem);
    }
    
    // 新增：检查是否应该显示员工费用菜单
    if (roleMenuPermissions[userRole]?.includes('employeeExpense')) {
        items.push(employeeExpenseMenuItem);
    }
    
    return items;
});

// 扁平化菜单，方便根据路径查找菜单项
const flattenedMenuItems = computed(() => {
    const flattened = [];

    // 添加所有菜单项
    menuItems.value.forEach((item) => {
        flattened.push(item);
    });

    return flattened;
});

// 根据当前路由更新面包屑和激活的菜单
const updateBreadcrumbsAndActiveMenu = () => {
    const currentPath = route.path;

    // 在扁平化菜单中查找当前路径对应的菜单项
    const currentMenuItem = flattenedMenuItems.value.find(
        (item) => item.path === currentPath
    );

    if (currentMenuItem) {
        // 设置激活的菜单
        activeMenu.value = currentMenuItem.id;

        // 设置面包屑
        breadcrumbs.value = [];

        // 添加当前菜单到面包屑
        breadcrumbs.value.push({
            title: currentMenuItem.title,
            path: currentMenuItem.path,
        });
    } else {
        // 如果是首页或未找到对应菜单项
        activeMenu.value = 'home';
        breadcrumbs.value = [
            {
                title: '首页',
                path: '/dashboard/home',
            },
        ];
    }
};

// 监听路由变化
watch(
    () => route.path,
    () => {
        updateBreadcrumbsAndActiveMenu();
    },
    { immediate: true }
);

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
};

// 获取用户详细信息
const fetchUserInfo = async (isInitialDashboardLoad = false) => {
    try {
        const response = await getEmployeeInfo();
        if (response.code === 200) {
            user.value = response.data;
            authStore.setUser(response.data);
            // 初始化表单数据
            Object.assign(profileForm, {
                name: user.value.name,
                email: user.value.email,
                phone: user.value.phone,
                idCard: user.value.idCard,
                oldPassword: '',
                newPassword: '',
                confirmPassword: '',
                departmentName: user.value.departmentName,
                positionName: user.value.positionName,
                entryDate: formatDate(user.value.entryDate),
                status: user.value.status === 'Active' ? '在职' : '离职',
                logisticsRoute: user.value.logisticsRoute,
            });
            // 用户信息获取成功后，检查是否是业务员，然后获取通知
            if (user.value) {
                // notificationStore.fetchPopupNotifications(); // 不再直接调用这个来弹窗
                notificationStore.fetchBellNotifications(isInitialDashboardLoad); // 传入 true 表示是登录时首次加载
            }
        }
    } catch (error) {
        console.error('获取用户信息失败', error);
    }
};

// 显示个人信息弹窗
const showProfileDialog = async () => {
    await fetchUserInfo(); // 获取最新的用户信息
    profileDialogVisible.value = true;
    isEditing.value = false;
};

// 切换编辑模式
const toggleEdit = () => {
    isEditing.value = !isEditing.value;
    if (!isEditing.value) {
        // 取消编辑时重置表单
        Object.assign(profileForm, {
            name: user.value.name,
            email: user.value.email,
            phone: user.value.phone,
            idCard: user.value.idCard,
            oldPassword: '',
            newPassword: '',
            confirmPassword: '',
            departmentName: user.value.departmentName,
            positionName: user.value.positionName,
            entryDate: formatDate(user.value.entryDate),
            status: user.value.status === 'Active' ? '在职' : '离职',
            logisticsRoute: user.value.logisticsRoute,
        });
    }
};

// 提交个人信息更新
const submitProfileUpdate = async (formEl) => {
    if (!formEl) return;

    try {
        // 先进行表单验证
        const valid = await formEl.validate();

        if (valid) {
            // 验证密码修改相关的逻辑
            if (profileForm.newPassword) {
                if (!profileForm.oldPassword) {
                    ElMessage.error('修改密码时需要输入原密码');
                    return;
                }
                if (profileForm.newPassword !== profileForm.confirmPassword) {
                    ElMessage.error('两次输入的新密码不一致');
                    return;
                }
            }

            // 构造请求数据，只包含后端需要的字段
            const updateData = {
                name: profileForm.name,
                phone: profileForm.phone,
                email: profileForm.email,
                password: profileForm.newPassword || undefined,
                oldPassword: profileForm.oldPassword || undefined,
            };

            const response = await updateProfile(updateData);

            if (response.code === 200) {
                ElMessage.success('个人信息更新成功');
                isEditing.value = false;
                await fetchUserInfo(); // 重新获取用户信息
                profileDialogVisible.value = false; // 关闭弹窗
            } else {
                ElMessage.error(response.message || '更新失败');
            }
        }
    } catch (error) {
        if (error.response) {
            ElMessage.error(error.response.data.message || '更新失败');
        } else {
            ElMessage.error(error.message || '更新失败');
        }
    }
};

// 处理登出
const handleLogout = async () => {
    try {
        await ElMessageBox.confirm('确定要退出登录吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        // 登出前清除通知信息
        notificationStore.clearNotifications(); 

        await logout();
        authStore.clearAuth();
        ElMessage.success('已成功退出登录');
        router.push('/login');
    } catch (error) {
        if (error !== 'cancel') {
            console.error('登出失败', error);
        }
    }
};

// 处理菜单点击
const handleMenuClick = (path) => {
    router.push(path);
};

// 控制侧边栏折叠
const toggleSidebar = () => {
    isCollapse.value = !isCollapse.value;
};

onMounted(async () => {
    // 确保用户已登录
    if (!authStore.isAuthenticated) {
        router.push('/login');
        return;
    }

    // 获取用户信息 (内部会触发通知的获取)
    await fetchUserInfo(true);

    // 更新面包屑和激活菜单
    updateBreadcrumbsAndActiveMenu();

    // 5-7秒后再次获取通知，以便加载后台异步分析可能产生的新通知
    setTimeout(() => {
        if (authStore.isAuthenticated) { // 确保用户仍然登录
            // console.log('Dashboard: Delayed fetch of bell notifications triggered.');
            notificationStore.fetchBellNotifications(false); // false表示非首次加载
        }
    }, 7000); // 7秒延迟

    // 显示欢迎消息（如果是从登录页跳转过来的）
    if (route.query.fromLogin && user.value) { // 确保user.value存在
        ElMessage({
            message: `欢迎回来，${user.value?.name || '员工'}！`,
            type: 'success',
            duration: 3000,
        });
    }
});
</script>

<template>
    <div class="app-container">
        <el-container class="layout-container">
            <!-- 侧边栏 -->
            <el-aside
                :width="isCollapse ? '64px' : '200px'"
                class="sidebar"
            >
                <!-- Logo 区域 -->
                <div class="logo-container">
                    <div
                        class="logo-content"
                        :class="{ 'collapsed': isCollapse }"
                    >
                        <img
                            src="../assets/img/logo.jpg"
                            class="logo-img"
                            alt="Logo"
                            v-if="!isCollapse"
                        >
                        <span
                            class="logo-text-collapsed"
                            v-if="isCollapse"
                        >中航</span>
                    </div>
                </div>

                <!-- 侧边菜单 -->
                <el-scrollbar class="sidebar-menu-container">
                    <el-menu
                        :default-active="activeMenu"
                        class="sidebar-menu"
                        :collapse="isCollapse"
                        :collapse-transition="false"
                        background-color="#001529"
                        text-color="#fff"
                        active-text-color="#409EFF"
                    >
                        <!-- 菜单项 -->
                        <el-menu-item
                            v-for="item in menuItems"
                            :key="item.id"
                            :index="item.id"
                            @click="handleMenuClick(item.path)"
                            class="menu-item-with-transition"
                        >
                            <el-icon>
                                <component :is="item.icon" />
                            </el-icon>
                            <template #title>
                                <span>{{ item.title }}</span>
                            </template>
                        </el-menu-item>
                    </el-menu>
                </el-scrollbar>

                <!-- 伸缩按钮放在左侧下方 -->
                <div class="sidebar-footer">
                    <el-icon
                        class="toggle-button"
                        @click="toggleSidebar"
                    >
                        <IconMenu />
                    </el-icon>
                </div>
            </el-aside>

            <!-- 主内容区域 -->
            <el-container class="main-container">
                <!-- 顶部导航栏 -->
                <el-header class="main-header">
                    <div class="header-left">
                        <!-- 面包屑导航 -->
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item
                                v-for="(item, index) in breadcrumbs"
                                :key="index"
                                :to="item.path ? { path: item.path } : null"
                            >
                                {{ item.title }}
                            </el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    <div class="header-right">
                        <!-- 通知铃铛 -->
                        <NotificationBell class="header-action-item" /> 

                        <el-dropdown
                            trigger="hover"
                            @command="(command) => command === 'logout' ? handleLogout() : showProfileDialog()"
                        >
                            <div class="user-info">
                                <el-avatar
                                    :size="32"
                                    :src="user?.avatar || 'https://ui-avatars.com/api/?name=' + user?.name + '&background=random'"
                                    class="user-avatar"
                                ></el-avatar>
                                <span class="user-name">{{ user?.name || '员工' }}</span>
                                <el-icon class="dropdown-icon">
                                    <ArrowDown />
                                </el-icon>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="profile">
                                        <el-icon>
                                            <User />
                                        </el-icon> 个人信息
                                    </el-dropdown-item>
                                    <el-dropdown-item command="logout">
                                        <el-icon>
                                            <Money />
                                        </el-icon> 退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 内容区域 -->
                <el-main class="main-content">
                    <router-view></router-view>
                </el-main>
            </el-container>
        </el-container>

        <!-- 个人信息弹窗 -->
        <el-dialog
            v-model="profileDialogVisible"
            :title="isEditing ? '编辑个人信息' : '个人信息'"
            width="500px"
            :close-on-click-modal="false"
            @closed="isEditing = false"
        >
            <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
                :disabled="!isEditing"
                status-icon
            >
                <!-- 基本信息部分 -->
                <el-form-item
                    label="姓名"
                    prop="name"
                >
                    <el-input v-model="profileForm.name" />
                </el-form-item>
                <el-form-item
                    label="手机号"
                    :prop="isEditing ? 'phone' : ''"
                >
                    <el-input v-model="profileForm.phone" :disabled="!isEditing" />
                </el-form-item>
                <el-form-item
                    label="邮箱"
                    prop="email"
                >
                    <el-input v-model="profileForm.email" />
                </el-form-item>
                <el-form-item 
                    v-if="!isEditing" 
                    label="身份证号"
                >
                    <el-input v-model="profileForm.idCard" disabled />
                </el-form-item>

                <!-- 只在查看模式下显示的信息 -->
                <template v-if="!isEditing">
                    <el-form-item label="所属部门">
                        <el-input
                            v-model="profileForm.departmentName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="职位">
                        <el-input
                            v-model="profileForm.positionName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="入职日期">
                        <el-input
                            v-model="profileForm.entryDate"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="在职状态">
                        <el-input
                            v-model="profileForm.status"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="物流航线">
                        <el-input
                            v-model="profileForm.logisticsRoute"
                            disabled
                        />
                    </el-form-item>
                </template>

                <!-- 只在编辑模式下显示的密码修改部分 -->
                <template v-if="isEditing">
                    <el-divider content-position="center">修改密码（选填）</el-divider>
                    <el-form-item
                        label="原密码"
                        prop="oldPassword"
                    >
                        <el-input
                            v-model="profileForm.oldPassword"
                            type="password"
                            show-password
                            placeholder="修改密码时必填"
                        />
                    </el-form-item>
                    <el-form-item
                        label="新密码"
                        prop="newPassword"
                    >
                        <el-input
                            v-model="profileForm.newPassword"
                            type="password"
                            show-password
                            placeholder="至少8位，必须包含大小写字母和数字"
                        />
                    </el-form-item>
                    <el-form-item
                        label="确认密码"
                        prop="confirmPassword"
                    >
                        <el-input
                            v-model="profileForm.confirmPassword"
                            type="password"
                            show-password
                            placeholder="请再次输入新密码"
                        />
                    </el-form-item>
                </template>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="profileDialogVisible = false">关闭</el-button>
                    <template v-if="!isEditing">
                        <el-button
                            type="primary"
                            @click="toggleEdit"
                        >
                            编辑信息
                        </el-button>
                    </template>
                    <template v-else>
                        <el-button @click="toggleEdit">取消</el-button>
                        <el-button
                            type="primary"
                            @click="submitProfileUpdate(profileFormRef)"
                        >
                            保存
                        </el-button>
                    </template>
                </span>
            </template>
        </el-dialog>

        <!-- 通知弹窗 -->
        <!-- <NotificationPopup :notifications="notificationStore.popupNotifications" /> --> <!-- 不再直接使用 -->
    </div>
</template>

<style scoped>
.app-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

.layout-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #001529;
    color: white;
    height: 100vh;
    transition: width 0.3s;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.logo-container {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #ffffff;
}

.logo-content {
    display: flex;
    align-items: center;
    overflow: hidden;
}

.logo-content.collapsed {
    justify-content: center;
}

.logo-img {
    height: 40px;
    object-fit: contain;
}

.logo-text-collapsed {
    color: #001529;
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
}

/* 侧边栏底部放置伸缩按钮 */
.sidebar-footer {
    padding: 16px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.toggle-button {
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 18px;
    transition: color 0.3s;
}

.toggle-button:hover {
    color: white;
}

/* 菜单样式 */
.sidebar-menu-container {
    flex: 1;
    overflow-y: auto;
}

.sidebar-menu {
    border-right: none;
}

.sidebar-menu :deep(.el-submenu__title):hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

.sidebar-menu :deep(.el-menu-item):hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
    background-color: #2c3e50 !important;
    transition: background-color 0.3s;
}

/* 增加点击菜单项的过渡效果 */
.menu-item-with-transition {
    position: relative;
    transition: all 0.3s;
}

.menu-item-with-transition:active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(44, 62, 80, 0.7);
    animation: fadeOut 0.5s forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 主内容区域样式 */
.main-container {
    height: 100vh;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
}

.main-header {
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left :deep(.el-breadcrumb) {
    font-size: 16px;
    font-weight: 500;
}

.header-left :deep(.el-breadcrumb__item) {
    color: #606266;
}

.header-left :deep(.el-breadcrumb__inner.is-link) {
    color: #409eff;
    font-weight: 500;
}

.header-left :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
    color: #303133;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-action-item {
    margin-right: 15px; /* 可根据需要调整与用户头像的间距 */
    display: flex; /* 确保垂直居中 */
    align-items: center;
    height: 100%;
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.user-info:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.user-avatar {
    margin-right: 8px;
    background-color: #1890ff;
}

.user-name {
    font-size: 14px;
    margin-right: 8px;
    color: #333;
}

.dropdown-icon {
    font-size: 12px;
    color: #666;
    margin-left: 4px;
}

.main-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    height: calc(100vh - 60px);
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
    .sidebar {
        position: fixed;
        z-index: 1000;
        transform: translateX(0);
        transition: transform 0.3s;
    }

    .sidebar.collapsed {
        transform: translateX(-100%);
    }

    .main-container {
        margin-left: 0;
    }

    .user-name {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style> 
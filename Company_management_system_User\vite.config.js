import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
	plugins: [vue()],
	resolve: {
		alias: {
			'@': path.resolve(__dirname, 'src'),
		},
	},
	server: {
		// host: '0.0.0.0', // 新增此行，使服务监听所有网络接口
		port: 5174,
		proxy: {
			'/api': {
				// target: 'http://**********:8080',
				target: 'http://localhost:8080',
				changeOrigin: true,
				rewrite: (path) => path.replace(/^\/api/, ''),
			},
		},
	},
	build: {
		// 生产环境独有配置
		minify: 'terser', // 使用terser进行代码压缩
		terserOptions: {
		  compress: {
			drop_console: true, // 移除所有console.log
			drop_debugger: true // 移除debugger
		  }
		}
	}
})

package org.example.company_management.service;

import org.example.company_management.entity.Department;
import org.example.company_management.utils.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 部门服务接口
 */
public interface DepartmentService {
    /**
     * 查询所有部门
     * @return 部门列表
     */
    List<Department> list();
    
    /**
     * 分页查询部门
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageResult<Department> pageList(int pageNum, int pageSize);
    
    /**
     * 分页查询部门(带条件)
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param departmentName 部门名称(可选)
     * @param leaderId 部门负责人ID(可选)
     * @param leaderName 部门负责人姓名(可选)
     * @param parentDepartmentId 父部门ID(可选)
     * @return 分页结果
     */
    PageResult<Department> pageList(int pageNum, int pageSize, String departmentName, Integer leaderId, String leaderName, Integer parentDepartmentId);
    
    /**
     * 根据关键词搜索部门
     * @param keyword 搜索关键词(用于模糊匹配部门名称)
     * @return 部门列表
     */
    List<Department> search(String keyword);
    
    /**
     * 根据ID查询部门
     * @param id 部门ID
     * @return 部门信息
     */
    Department getById(Integer id);
    
    /**
     * 新增部门
     * @param department 部门信息
     */
    void add(Department department);
    
    /**
     * 修改部门
     * @param department 部门信息
     */
    void update(Department department);
    
    /**
     * 删除部门
     * @param id 部门ID
     */
    void delete(Integer id);
    
    /**
     * 根据部门负责人ID查询负责的部门列表
     * @param leaderId 部门负责人ID
     * @return 该负责人负责的所有部门列表
     */
    List<Department> getDepartmentsByLeaderId(Integer leaderId);
    
    /**
     * 获取部门的子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> getSubDepartments(Integer parentId);
} 
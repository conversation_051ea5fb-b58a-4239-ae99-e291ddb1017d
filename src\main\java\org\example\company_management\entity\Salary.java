package org.example.company_management.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 工资实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Salary {
    /**
     * 工资记录ID
     */
    private Integer id;

    /**
     * 员工ID
     */
    private Integer employeeId;

    /**
     * 发放年月（格式：yyyy-MM）
     */
    private String date;

    /**
     * 基本工资
     */
    private BigDecimal basicSalary;

    /**
     * 绩效奖金
     */
    private BigDecimal performanceBonus;

    /**
     * 全勤奖金
     */
    private BigDecimal fullAttendanceBonus;

    /**
     * 业务操作奖金
     */
    private BigDecimal businessOperationBonus;

    /**
     * 实得金额 (实得金额)
     */
    private BigDecimal sumSalary;

    /**
     * 请假扣款
     */
    private BigDecimal leaveDeduction;

    /**
     * 扣除金额
     */
    private BigDecimal deduction;

    /**
     * 迟到缺卡扣款
     */
    private BigDecimal lateDeduction;

    /**
     * 社保个人部分
     */
    private BigDecimal socialSecurityPersonal;

    /**
     * 公积金
     */
    private BigDecimal providentFund;

    /**
     * 个税
     */
    private BigDecimal tax;

    /**
     * 水电费
     */
    private BigDecimal waterElectricityFee;

    /**
     * 实发工资
     */
    private BigDecimal actualSalary;

    /**
     * 报销
     */
    private BigDecimal reimbursement;

    /**
     * 私账
     */
    private BigDecimal privateAccount;

    /**
     * 总工资
     */
    private BigDecimal totalSalary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 员工姓名（非数据库字段）
     */
    private String employeeName;

    /**
     * 部门名称（非数据库字段）
     */
    private String department;

    /**
     * 职位名称（非数据库字段）
     */
    private String position;
    
} 
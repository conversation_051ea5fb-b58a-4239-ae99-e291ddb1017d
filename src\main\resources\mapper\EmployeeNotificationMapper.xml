<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.EmployeeNotificationMapper">

    <resultMap id="EmployeeNotificationResultMap" type="org.example.company_management.entity.EmployeeNotification">
        <id property="notificationId" column="notification_id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="notificationType" column="notification_type"/>
        <result property="titleCn" column="title_cn"/>
        <result property="messageCn" column="message_cn"/>
        <result property="generatedAt" column="generated_at"/>
        <result property="lastShownAt" column="last_shown_at"/>
        <result property="dismissedAt" column="dismissed_at"/>
        <result property="showAgainAfterDays" column="show_again_after_days"/>
        <result property="nextPromptDate" column="next_prompt_date"/>
        <result property="isReadInBell" column="is_read_in_bell"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <insert id="insertNotification" useGeneratedKeys="true" keyProperty="notificationId">
        INSERT INTO employee_notifications (employee_id, rule_id, notification_type, title_cn, message_cn, generated_at, last_shown_at, dismissed_at, show_again_after_days, next_prompt_date, is_read_in_bell, status, create_time, update_time)
        VALUES (#{employeeId}, #{ruleId}, #{notificationType}, #{titleCn}, #{messageCn}, #{generatedAt}, #{lastShownAt}, #{dismissedAt}, #{showAgainAfterDays}, #{nextPromptDate}, #{isReadInBell}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <update id="updateNotification">
        UPDATE employee_notifications
        <set>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="notificationType != null">notification_type = #{notificationType},</if>
            <if test="titleCn != null">title_cn = #{titleCn},</if>
            <if test="messageCn != null">message_cn = #{messageCn},</if>
            <if test="generatedAt != null">generated_at = #{generatedAt},</if>
            <if test="lastShownAt != null">last_shown_at = #{lastShownAt},</if>
            <if test="dismissedAt != null">dismissed_at = #{dismissedAt},</if>
            <if test="showAgainAfterDays != null">show_again_after_days = #{showAgainAfterDays},</if>
            <if test="nextPromptDate != null">next_prompt_date = #{nextPromptDate},</if>
            <if test="isReadInBell != null">is_read_in_bell = #{isReadInBell},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            update_time = NOW()
        </set>
        WHERE notification_id = #{notificationId}
    </update>

    <select id="findById" resultMap="EmployeeNotificationResultMap">
        SELECT *
        FROM employee_notifications
        WHERE notification_id = #{notificationId}
    </select>

    <select id="findNotificationsForBellByEmployeeId" resultMap="EmployeeNotificationResultMap">
        SELECT *
        FROM employee_notifications
        WHERE employee_id = #{employeeId}
          AND status != 'ARCHIVED' -- Example: Exclude archived notifications
        ORDER BY generated_at DESC
        LIMIT 50 -- Example: Limit to a reasonable number for the bell
    </select>

    <update id="dismissNotificationById">
        UPDATE employee_notifications
        SET dismissed_at = #{dismissedAt},
            next_prompt_date = #{nextPromptDate},
            status = #{status},
            is_read_in_bell = TRUE, -- Dismissing usually implies it's been seen/read
            update_time = NOW()
        WHERE notification_id = #{notificationId}
    </update>

    <update id="markAsReadInBellById">
        UPDATE employee_notifications
        SET is_read_in_bell = #{isReadInBell},
            update_time = NOW()
        WHERE notification_id = #{notificationId}
    </update>

    <update id="dismissMultipleNotificationsByIds">
        UPDATE employee_notifications
        SET dismissed_at = #{dismissedAt},
            next_prompt_date = #{nextPromptDate},
            status = #{status},
            is_read_in_bell = TRUE,
            update_time = NOW()
        WHERE notification_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findExistingNotificationByRule" resultMap="EmployeeNotificationResultMap">
        SELECT *
        FROM employee_notifications
        WHERE employee_id = #{employeeId}
          AND rule_id = #{ruleId}
          AND (
                (status = 'ACTIVE' AND (next_prompt_date IS NULL OR next_prompt_date >= #{today})) OR
                (status = 'DISMISSED' AND next_prompt_date IS NOT NULL AND next_prompt_date >= #{today})
              )
        ORDER BY generated_at DESC
        LIMIT 1
    </select>

    <select id="findActiveAndDueByEmployeeId" resultMap="EmployeeNotificationResultMap">
        SELECT *
        FROM employee_notifications
        WHERE employee_id = #{employeeId}
          AND status = 'ACTIVE'
          AND (next_prompt_date IS NULL OR next_prompt_date &lt;= #{today})
        ORDER BY generated_at DESC
    </select>

    <!-- 删除过期的通知 -->
    <delete id="deleteExpiredNotifications">
        DELETE FROM employee_notifications
        WHERE status IN ('DISMISSED', 'ARCHIVED')
          AND update_time &lt; #{expiredBefore}
    </delete>

</mapper>
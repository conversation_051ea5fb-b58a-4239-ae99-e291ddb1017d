<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    Calendar,
    User,
    CopyDocument,
} from '@element-plus/icons-vue';
import { getEmployeeList, getEmployeePage } from '@/api/employee';
import {
    getEmployeeExpensePage,
    addEmployeeExpense,
    updateEmployeeExpense,
    deleteEmployeeExpense,
    batchDeleteEmployeeExpense,
    getEmployeeExpenseById,
    extendBatchEmployeeExpense
} from '@/api/employeeExpense';
import BatchExtendDialog from '@/components/BatchExtendDialog.vue';

// Data
const employeeExpenseData = ref([]);
const loading = ref(true);
const searchEmployeeName = ref('');
const searchMonth = ref('');
const searchItemName = ref('');

// Employee list for dropdowns (if needed for search or form)
const employeeList = ref([]); // For employee selection in form
const loadingEmployees = ref(false);

// Month options for el-select (used by new dialog and potentially add dialog)
const monthOptions = ref([]);

// Selected records
const selectedExpenses = ref([]);

// --- Refs for new BatchExtendDialog ---
const batchExtendDialogVisible = ref(false);
const preparedItemsForDialog = ref([]); // Data to pass to BatchExtendDialog prop
const batchExtendDialogRef = ref(null); // Ref to access exposed properties/methods of BatchExtendDialog

const employeeExpenseDisplayFields = ref([
    { label: '员工', prop: 'employeeName', minWidth: '100px' },
    { label: '原费用年月', prop: 'originalExpenseDate', width: '110px', align: 'center' },
    { label: '项目名称', prop: 'itemName', minWidth: '140px' },
    { label: '金额', prop: 'amount', width: '100px', align: 'right', isCurrency: true },
    { label: '备注', prop: 'remark', minWidth: '110px' }
]);
// --- End Refs for new BatchExtendDialog ---

// Pagination
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// Dialog control (for Add/Edit dialog)
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' or 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// Form data
const form = reactive({
    id: null,
    employeeId: null,
    employeeIds: [], 
    employeeName: '', 
    expenseDate: '', 
    selectedMonths: [], 
    itemName: '',
    amount: 0,
    remark: '',
});

// Form rules
const rules = {
    employeeId: [{ required: true, message: '请选择员工', trigger: 'change' }],
    employeeIds: [
        { type: 'array', required: true, message: '请至少选择一个员工', trigger: 'change' },
        { validator: (rule, value, callback) => {
                if (!value || value.length === 0) {
                    callback(new Error('请至少选择一个员工'));
                } else {
                    callback();
                }
            }, trigger: 'change'
        }
    ],
    expenseDate: [{ required: true, message: '请选择月份', trigger: 'change' }],
    selectedMonths: [
        { type: 'array', required: true, message: '请至少选择一个月份', trigger: 'change' },
        { validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
                callback(new Error('请至少选择一个月份'));
            } else {
                callback();
            }
          }, trigger: 'change'
        }
    ],
    itemName: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
        { max: 255, message: '项目名称不能超过255个字符', trigger: 'blur' },
    ],
    amount: [
        { required: true, message: '请输入金额', trigger: 'blur' },
        { type: 'number', message: '金额必须为数字', trigger: 'blur' },
        { validator: (rule, value, callback) => { 
            if (value === null || value === undefined) callback(new Error('金额不能为空'));
            else if (value <= 0) callback(new Error('金额必须大于0'));
            else callback();
        }, trigger: 'blur' }
    ],
    remark: [{ max: 255, message: '备注不能超过255个字符', trigger: 'blur' }],
};

// 生成月份选项
const generateMonthOptions = () => {
    const optionsMap = new Map(); 
    const currentDate = new Date();
    for (let i = 0; i < 12; i++) {
        const targetDate = new Date(currentDate);
        targetDate.setDate(1); 
        targetDate.setMonth(currentDate.getMonth() - i);
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1; 
        const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
        if (!optionsMap.has(monthStr)) {
            optionsMap.set(monthStr, { value: monthStr, label: monthStr });
        }
    }
    for (let i = 1; i <= 12; i++) { // Changed to 12 for future months as well
        const targetDate = new Date(currentDate);
        targetDate.setDate(1); 
        targetDate.setMonth(currentDate.getMonth() + i);
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1; 
        const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
        if (!optionsMap.has(monthStr)) {
            optionsMap.set(monthStr, { value: monthStr, label: monthStr });
        }
    }
    monthOptions.value = Array.from(optionsMap.values())
        .sort((a, b) => b.value.localeCompare(a.value));
};

// Load employees for form select
const loadEmployeesForForm = async (query) => {
    if (query) {
        loadingEmployees.value = true;
        try {
            const res = await getEmployeePage({ 
                name: query, 
                pageSize: 20,
                pageNum: 1
            });
            if (res.code === 200) {
                if (Array.isArray(res.data)) {
                    employeeList.value = res.data;
                } else if (res.data && Array.isArray(res.data.list)) {
                    employeeList.value = res.data.list;
                } else if (res.data && Array.isArray(res.data.records)) {
                    employeeList.value = res.data.records;
                } else {
                    employeeList.value = [];
                }
            } else {
                ElMessage.error(res.message || '获取员工列表失败');
            }
        } catch (error) {
            console.error("Failed to load employees:", error);
            ElMessage.error('加载员工列表失败: ' + (error.message || '未知错误'));
        } finally {
            loadingEmployees.value = false;
        }
    }
};

const loadAllEmployees = async () => {
    loadingEmployees.value = true;
    try {
        const res = await getEmployeePage({ 
            pageSize: 100, // Consider larger pageSize if many employees
            pageNum: 1
        });
        if (res.code === 200) {
             if (Array.isArray(res.data)) {
                employeeList.value = res.data;
            } else if (res.data && Array.isArray(res.data.list)) {
                employeeList.value = res.data.list;
            } else if (res.data && Array.isArray(res.data.records)) {
                employeeList.value = res.data.records;
            } else {
                employeeList.value = [];
            }
        } else {
            ElMessage.error(res.message || '获取员工列表失败');
        }
    } catch (error) {
        console.error("Failed to load all employees:", error);
        ElMessage.error('加载员工列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingEmployees.value = false;
    }
};

const loadExpenseData = async () => {
    loading.value = true;
    try {
        let startDate = null;
        let endDate = null;
        if (searchMonth.value) {
            const year = searchMonth.value.substring(0, 4);
            const month = searchMonth.value.substring(5, 7);
            startDate = `${year}-${month}-01`;
            const lastDay = new Date(year, month, 0).getDate();
            endDate = `${year}-${month}-${lastDay}`;
        }

        const params = {
            pageNum: pagination.page,
            pageSize: pagination.size,
            employeeName: searchEmployeeName.value || undefined,
            startDate: startDate || undefined,
            endDate: endDate || undefined,
            itemName: searchItemName.value || undefined,
        };
        const res = await getEmployeeExpensePage(params);
        if (res.code === 200) {
            employeeExpenseData.value = res.data.records || res.data.list || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取员工费用数据失败');
        }
    } catch (error) {
        console.error("Failed to load employee expenses:", error);
        ElMessage.error('加载员工费用数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = null;
    form.employeeId = null;
    form.employeeIds = []; 
    form.employeeName = '';
    form.expenseDate = '';
    form.selectedMonths = []; 
    form.itemName = '';
    form.amount = 0;
    form.remark = '';
    employeeList.value = [];
};

const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    loadAllEmployees();
    if (monthOptions.value.length === 0) {
        generateMonthOptions();
    }
    dialogVisible.value = true;
};

// Function to open add dialog with template data (if needed in future)
// const handleOpenAddFromTemplateDialog = (row) => { ... };


const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();
    try {
        const res = await getEmployeeExpenseById(row.id);
        if (res.code === 200 && res.data) {
            Object.assign(form, res.data);
            form.amount = Number(res.data.amount) || 0;
            if (res.data.employeeId && res.data.employeeName) {
                employeeList.value = [{ 
                    employeeId: res.data.employeeId, 
                    name: res.data.employeeName 
                }];
            }
            dialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取费用详情失败');
        }
    } catch (error) {
        ElMessage.error('获取费用详情失败: ' + (error.message || '未知错误'));
    }
};

const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除员工 "${row.employeeName}" 在 "${row.expenseDate}" 关于 "${row.itemName}" 的费用记录吗？`,
        '删除确认',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    ).then(async () => {
        loading.value = true;
        try {
            const res = await deleteEmployeeExpense(row.id);
            if (res.code === 200) {
                ElMessage.success('删除成功');
                loadExpenseData();
            } else {
                ElMessage.error(res.message || '删除失败');
            }
        } catch (error) {
            ElMessage.error('删除失败: ' + (error.message || '未知错误'));
        } finally {
            loading.value = false;
        }
    }).catch(() => { /* User cancelled */ });
};

const handleBatchDelete = () => {
    if (selectedExpenses.value.length === 0) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }
    const ids = selectedExpenses.value.map((item) => item.id);
    ElMessageBox.confirm(
        `确定要批量删除选中的 ${ids.length} 条费用记录吗？`,
        '批量删除确认',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    ).then(async () => {
        loading.value = true;
        try {
            const res = await batchDeleteEmployeeExpense(ids);
            if (res.code === 200) {
                ElMessage.success('批量删除成功');
                selectedExpenses.value = [];
                loadExpenseData();
            } else {
                ElMessage.error(res.message || '批量删除失败');
            }
        } catch (error) {
            ElMessage.error('批量删除失败: ' + (error.message || '未知错误'));
        } finally {
            loading.value = false;
        }
    }).catch(() => { /* User cancelled */ });
};

const submitForm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                let res;
                if (dialogType.value === 'add' || dialogType.value === 'template') {
                    if (form.employeeIds.length > 0 && form.selectedMonths.length > 0) {
                        const batchData = {
                            employeeIds: form.employeeIds,
                            expenseMonths: form.selectedMonths,
                            itemName: form.itemName,
                            amount: Number(form.amount),
                            remark: form.remark
                        };
                        res = await addEmployeeExpense(batchData);
                    } else {
                        const singleData = { 
                            employeeId: form.employeeId,
                            expenseDate: form.expenseDate,
                            itemName: form.itemName,
                            amount: Number(form.amount),
                            remark: form.remark
                        };
                        res = await addEmployeeExpense(singleData);
                    }
                } else {
                    const dataToSubmit = { ...form, amount: Number(form.amount) };
                    res = await updateEmployeeExpense(dataToSubmit);
                }
                
                if (res.code === 200) {
                    ElMessage.success(dialogType.value === 'edit' ? '更新成功' : (dialogType.value === 'template' ? '延用成功' : '添加成功'));
                    dialogVisible.value = false;
                    loadExpenseData();
                } else {
                    ElMessage.error(res.message || (dialogType.value === 'edit' ? '更新失败' : (dialogType.value === 'template' ? '延用失败' : '添加失败')));
                }
            } catch (error) {
                ElMessage.error('提交失败: ' + (error.message || '未知错误'));
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage.warning('请完善表单信息');
            return false;
        }
    });
};

const handleSelectionChange = (selection) => {
    selectedExpenses.value = selection;
};

const handleSearch = () => {
    pagination.page = 1;
    loadExpenseData();
};

const handleReset = () => {
    searchEmployeeName.value = '';
    searchMonth.value = '';
    searchItemName.value = '';
    pagination.page = 1;
    loadExpenseData();
};

const handleCurrentChange = (page) => {
    pagination.page = page;
    loadExpenseData();
};

const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadExpenseData();
};

// Format currency (keep if used elsewhere in this file, e.g. main table)
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return '¥' + parseFloat(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const formatDate = (dateTimeString) => {
    if (!dateTimeString) return '-';
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });
};

// --- New Batch Extend Functions using BatchExtendDialog.vue ---
const handleOpenBatchExtendDialog = () => {
    if (selectedExpenses.value.length === 0) {
        ElMessage.warning('请至少选择一条费用记录进行延用');
        return;
    }

    const uniqueCheckSet = new Set();
    for (const item of selectedExpenses.value) {
        const key = `${item.employeeId}_${item.itemName}`;
        if (uniqueCheckSet.has(key)) {
            ElMessage.error('不能同时选择相同员工、相同项目的不同月份费用进行延用。');
            return;
        }
        uniqueCheckSet.add(key);
    }

    preparedItemsForDialog.value = selectedExpenses.value.map(item => ({
        originalId: item.id, // This will be used as itemKeyField in the child
        employeeId: item.employeeId,
        employeeName: item.employeeName,
        originalExpenseDate: item.expenseDate ? item.expenseDate.substring(0, 7) : '-',
        itemName: item.itemName,
        amount: Number(item.amount) || 0,
        remark: item.remark || ''
        // No _key or isSelectedForExtend here, child component handles that
    }));
    
    if (monthOptions.value.length === 0) {
         generateMonthOptions(); 
    }
    batchExtendDialogVisible.value = true;
};

const handleConfirmBatchExtend = async (payload) => {
    // payload will be { selectedItems, targetMonths }
    if (!batchExtendDialogRef.value) {
        console.error("BatchExtendDialog ref is not available");
        ElMessage.error('批量延用组件引用错误');
        return;
    }

    const payloadItems = [];
    payload.selectedItems.forEach(item => {
        payload.targetMonths.forEach(monthYYYYMM => {
            const targetDate = `${monthYYYYMM}-01`; 
            payloadItems.push({
                employeeId: item.employeeId,
                // Add employeeName for potential backend use or rich DTO, though not strictly in current payloadItems schema
                employeeName: item.employeeName, // For richer DTO if backend can use it for messages
                expenseDate: targetDate,
                itemName: item.itemName,
                amount: item.amount,
                remark: item.remark
            });
        });
    });

    if (payloadItems.length === 0) {
         ElMessage.warning('未能构建任何有效的延用数据');
         return;
    }
    
    try {
        const res = await extendBatchEmployeeExpense({ items: payloadItems });
        if (res.code === 200 && res.data) {
            const { successfullyExtendedItems, skippedDuplicateItems } = res.data;
            const succeededCount = successfullyExtendedItems ? successfullyExtendedItems.length : 0;
            const skippedCount = skippedDuplicateItems ? skippedDuplicateItems.length : 0;

            const primaryMessage = `批量延用处理完成：成功 ${succeededCount} 项，失败/跳过 ${skippedCount} 项。`;

            if (succeededCount > 0 && skippedCount === 0) { // All attempted and processed were successful
                ElMessage.success(primaryMessage);
            } else { // Some were skipped, or none succeeded but items were processed
                ElMessage.warning(primaryMessage);
            }

            if (skippedCount > 0 && skippedDuplicateItems) {
                let skippedDetails = "<strong>被跳过或延用失败的项目详情：</strong><br/>";
                skippedDuplicateItems.forEach(item => {
                    skippedDetails += `- ${item}<br/>`;
                });
                ElMessageBox.alert(skippedDetails, '跳过项目详情', {
                    confirmButtonText: '我知道了',
                    type: 'warning',
                    dangerouslyUseHTMLString: true, 
                    callback: () => { /* Optional callback */ }
                });
            }

            batchExtendDialogVisible.value = false; 
            loadExpenseData(); 
            selectedExpenses.value = []; 
        } else {
            ElMessage.error(res.message || '批量延用失败，未返回有效数据');
        }
    } catch (error) {
        console.error("批量延用失败:", error);
        ElMessage.error('批量延用操作失败: ' + (error.message || '未知错误'));
    } finally {
        if (batchExtendDialogRef.value && typeof batchExtendDialogRef.value.resetLoadingState === 'function') {
            batchExtendDialogRef.value.resetLoadingState();
        }
    }
};
// --- End New Batch Extend Functions ---

onMounted(() => {
    loadExpenseData();
    generateMonthOptions();
});

</script>

<template>
    <div class="expense-container">
        <!-- Search Toolbar -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchEmployeeName"
                    placeholder="搜索员工姓名"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix><el-icon><User /></el-icon></template>
                </el-input>
                <el-date-picker
                    v-model="searchMonth"
                    type="month"
                    placeholder="选择月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    clearable
                    @change="handleSearch"
                />
                <el-input
                    v-model="searchItemName"
                    placeholder="搜索项目名称"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix><el-icon><Search /></el-icon></template>
                </el-input>
                <el-button type="primary" @click="handleSearch"><el-icon><Search /></el-icon>搜索</el-button>
                <el-button @click="handleReset"><el-icon><RefreshRight /></el-icon>重置</el-button>
            </div>
            <div class="action-box">
                <el-button type="danger" :disabled="selectedExpenses.length === 0" @click="handleBatchDelete">
                    <el-icon><Delete /></el-icon>批量删除
                </el-button>
                <el-button type="primary" class="add-btn" @click="handleAdd">
                    <el-icon><Plus /></el-icon>添加费用
                </el-button>
                <el-button
                    type="success"
                    :disabled="selectedExpenses.length === 0"
                    @click="handleOpenBatchExtendDialog"
                >
                    <el-icon><CopyDocument /></el-icon>延用选中费用
                </el-button>
            </div>
        </div>

        <!-- Expense Table -->
        <el-table
            v-loading="loading"
            :data="employeeExpenseData"
            border
            row-key="id"
            @selection-change="handleSelectionChange"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="员工" prop="employeeName" min-width="150" show-overflow-tooltip />
            <el-table-column label="部门" prop="departmentName" min-width="150" show-overflow-tooltip />
            <el-table-column label="年月" prop="expenseDate" width="120" align="center">
                <template #default="{ row }">
                    <el-tag type="info">
                        {{ row.expenseDate ? row.expenseDate.substring(0, 7) : '-' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="项目名称" prop="itemName" min-width="200" show-overflow-tooltip />
            <el-table-column label="金额" prop="amount" min-width="120" align="right" show-overflow-tooltip>
                <template #default="{ row }">{{ formatCurrency(row.amount) }}</template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" min-width="180" show-overflow-tooltip>
                 <template #default="{ row }">{{ row.remark || '-' }}</template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" width="180" align="center" show-overflow-tooltip>
                <template #default="{ row }">{{ formatDate(row.createTime) }}</template>
            </el-table-column>
            <el-table-column label="更新时间" prop="updateTime" width="180" align="center" show-overflow-tooltip>
                 <template #default="{ row }">{{ formatDate(row.updateTime) }}</template>
            </el-table-column>
            <el-table-column label="操作" width="190" align="center" fixed="right" class-name="operation-column">
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button class="edit-btn" @click="handleEdit(row)" title="编辑"><el-icon><Edit /></el-icon></el-button>
                        <el-button class="delete-btn" @click="handleDelete(row)" title="删除"><el-icon><Delete /></el-icon></el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- Add/Edit Dialog -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' || dialogType === 'template' ? '添加员工费用' : '编辑员工费用'" 
            width="500px"
            destroy-on-close
            class="custom-dialog"
            :close-on-click-modal="false"
        >
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="dialog-form">
                <el-form-item v-if="dialogType === 'edit'" label="选择员工" prop="employeeId" required>
                    <el-select
                        v-model="form.employeeId"
                        placeholder="输入员工姓名搜索"
                        filterable
                        remote
                        :remote-method="loadEmployeesForForm" 
                        :loading="loadingEmployees"
                        style="width: 100%"
                        @change="val => { const emp = employeeList.find(e => e.employeeId === val); if(emp) form.employeeName = emp.name; }"
                    >
                        <el-option
                            v-for="item in employeeList"
                            :key="item.employeeId"
                            :label="item.name + (item.departmentName ? ' (' + item.departmentName + ')' : '')"
                            :value="item.employeeId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="dialogType === 'add' || dialogType === 'template'" label="选择员工" prop="employeeIds" required>
                    <el-select
                        v-model="form.employeeIds"
                        placeholder="请选择一个或多个员工"
                        filterable
                        multiple
                        clearable
                        :loading="loadingEmployees"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in employeeList"
                            :key="item.employeeId"
                            :label="item.name + (item.departmentName ? ' (' + item.departmentName + ')' : '')"
                            :value="item.employeeId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="dialogType === 'edit'" label="月份" prop="expenseDate" required>
                    <el-date-picker
                        v-model="form.expenseDate"
                        type="month"
                        placeholder="选择月份"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                        :disabled="dialogType === 'edit' && form.id !== null" 
                    />
                </el-form-item>
                <el-form-item v-if="dialogType === 'add' || dialogType === 'template'" label="月份" prop="selectedMonths" required>
                    <el-select
                        v-model="form.selectedMonths"
                        multiple
                        filterable
                        placeholder="请选择一个或多个月份"
                        style="width: 100%"
                        clearable
                    >
                        <el-option
                            v-for="item in monthOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="项目名称" prop="itemName" required>
                    <el-input v-model="form.itemName" placeholder="请输入项目名称" />
                </el-form-item>
                <el-form-item label="金额" prop="amount" required>
                    <el-input-number v-model="form.amount" :precision="2" :step="50" :min="0.01" style="width: 100%" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息 (可选)" maxlength="255" show-word-limit />
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" :loading="formLoading" @click="submitForm(formRef)">确定</el-button>
            </div>
        </el-dialog>

        <!-- USE THE NEW BatchExtendDialog COMPONENT -->
        <BatchExtendDialog
            ref="batchExtendDialogRef" 
            v-model:isVisible="batchExtendDialogVisible"
            :items-to-extend="preparedItemsForDialog"
            :month-options="monthOptions"
            entity-name="员工费用"
            item-key-field="originalId" 
            :item-display-fields="employeeExpenseDisplayFields"
            @submit-extend="handleConfirmBatchExtend"
        />

    </div>
</template>

<style scoped>
/* Styles are copied from Salary.vue for consistency */
.expense-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px); 
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap; 
}

.search-box .el-input,
.search-box .el-select,
.search-box .el-date-picker {
    width: 200px; 
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; 
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1; 
}

:deep(.custom-table) {
    margin-bottom: 60px; 
    border-radius: 6px;
    overflow: hidden; 
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto; 
}
:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}


:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center; 
}
:deep(.el-table th.el-table__cell>.cell) { 
    text-align: center;
}


:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9; 
}
.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}
.edit-btn, .delete-btn, .copy-btn {
    border: none;
    width: 32px; 
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}
.edit-btn { background-color: #e6f1fc; color: #409eff; }
.edit-btn:hover { background-color: #409eff; color: white; }
.delete-btn { background-color: #ffebec; color: #f56c6c; }
.delete-btn:hover { background-color: #f56c6c; color: white; }
/* .copy-btn { background-color: #f0f9eb; color: #67c23a; } // Keep if a general copy button exists */
/* .copy-btn:hover { background-color: #67c23a; color: white; } */


.custom-dialog { 
    border-radius: 8px;
}
.dialog-form { 
    padding: 0 20px;
    max-height: 60vh; 
    overflow-y: auto;
}
.dialog-form::-webkit-scrollbar { width: 6px; }
.dialog-form::-webkit-scrollbar-thumb { background-color: #c0c4cc; border-radius: 3px; }
.dialog-form::-webkit-scrollbar-track { background-color: #f5f7fa; }

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px; 
    padding: 0 20px 10px 20px; 
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0; 
}
:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0px; 
}
:deep(.custom-dialog .el-dialog__footer) {
    display: none; 
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select .el-input__wrapper),
:deep(.custom-dialog .el-input-number) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}
:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select .el-input__wrapper:hover),
:deep(.custom-dialog .el-input-number:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}
:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-input-number.is-focus) {
     box-shadow: 0 0 0 1px #409eff inset;
}

/* REMOVE OLD Batch Extend Dialog Styles if they were specific and not covered by reusable component or general dialog styles */
/* For example:
.batch-extend-dialog .dialog-tip { ... }
.batch-extend-dialog .extend-items-table { ... }
.batch-extend-dialog .empty-extend-dialog { ... }
:deep(.batch-extend-dialog .el-dialog__body) { ... }
*/

</style> 
package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 部门实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Department {
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 部门负责人ID
     */
    private Integer leaderId;
    
    /**
     * 部门负责人姓名（非数据库字段）
     */
    private String departmentLeader;
    
    /**
     * 部门描述
     */
    private String departmentDescription;
    
    /**
     * 父部门ID
     */
    private Integer parentDepartmentId;
    
    /**
     * 部门状态(Active/Inactive)
     */
    private String status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 员工数量
     */
    private Integer employeeCount;
} 
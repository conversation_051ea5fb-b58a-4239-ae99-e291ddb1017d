<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    Calendar,
    Money,
    Upload,
} from '@element-plus/icons-vue';
import {
    getSalaryPage,
    getSalaryById,
    addSalary,
    updateSalary,
    deleteSalary,
    batchDeleteSalary,
} from '@/api/salary';
import { getDepartmentList } from '@/api/department';
import { getEmployeeList, getEmployeePage } from '@/api/employee';
import ExcelImportDialog from '@/components/ExcelImportDialog.vue';
import ImportErrorsDialog from '@/components/ImportErrorsDialog.vue';

// 工资数据
const salaryData = ref([]);
const loading = ref(true);
const searchEmployeeName = ref('');
const searchDepartmentId = ref('');
const searchYearMonth = ref('');

// 部门和员工列表
const departmentList = ref([]);
const employeeList = ref([]);
const loadingDepartments = ref(false);
const loadingEmployees = ref(false);
const formDepartmentId = ref(''); // 用于部门选择框的绑定值

// 选中的记录
const selectedSalaries = ref([]);

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 表单数据
const form = reactive({
    id: null,
    employeeId: null,
    employeeName: '',
    departmentId: null,
    date: '',
    basicSalary: 0,
    performanceBonus: 0,
    fullAttendanceBonus: 0, 
    businessOperationBonus: 0,
    reimbursement: 0,
    privateAccount: 0,
    leaveDeduction: 0,
    deduction: 0,
    lateDeduction: 0,
    socialSecurityPersonal: 0,
    providentFund: 0,
    tax: 0,
    waterElectricityFee: 0,
    sumSalary: 0,
    actualSalary: 0,
    totalSalary: 0,
    remark: '',
});

// 表单规则
const rules = {
    departmentId: [
        { required: true, message: '请选择部门', trigger: 'change' },
    ],
    employeeId: [{ required: true, message: '请选择员工', trigger: 'change' }],
    date: [{ required: true, message: '请选择年月', trigger: 'change' }],
    basicSalary: [
        { required: true, message: '请输入基本工资', trigger: 'blur' },
        { type: 'number', message: '基本工资必须为数字', trigger: 'blur' },
    ],
    performanceBonus: [
        { required: true, message: '请输入绩效奖金', trigger: 'blur' },
        { type: 'number', message: '绩效奖金必须为数字', trigger: 'blur' },
    ],
    fullAttendanceBonus: [
        { required: true, message: '请输入全勤奖金', trigger: 'blur' },
        { type: 'number', message: '全勤奖金必须为数字', trigger: 'blur' },
    ],
    businessOperationBonus: [
        { required: true, message: '请输入业务奖金', trigger: 'blur' },
        { type: 'number', message: '业务奖金必须为数字', trigger: 'blur' },
    ],
    reimbursement: [
        { required: true, message: '请输入报销金额', trigger: 'blur' },
        { type: 'number', message: '报销金额必须为数字', trigger: 'blur' },
    ],
    privateAccount: [
        { required: true, message: '请输入私账金额', trigger: 'blur' },
        { type: 'number', message: '私账金额必须为数字', trigger: 'blur' }
        // 私账可正可负，不加非负校验
    ],
    leaveDeduction: [
        { required: true, message: '请输入请假扣款', trigger: 'blur' },
        { type: 'number', message: '请假扣款必须为数字', trigger: 'blur' },
    ],
    deduction: [
        { required: true, message: '请输入扣款', trigger: 'blur' },
        { type: 'number', message: '扣款必须为数字', trigger: 'blur' },
    ],
    lateDeduction: [
        { required: true, message: '请输入迟到扣款', trigger: 'blur' },
        { type: 'number', message: '迟到扣款必须为数字', trigger: 'blur' },
    ],
    socialSecurityPersonal: [
        { required: true, message: '请输入社保个人部分', trigger: 'blur' },
        { type: 'number', message: '社保个人部分必须为数字', trigger: 'blur' },
    ],
    providentFund: [
        { required: true, message: '请输入公积金', trigger: 'blur' },
        { type: 'number', message: '公积金必须为数字', trigger: 'blur' },
    ],
    tax: [
        { required: true, message: '请输入个税', trigger: 'blur' },
        { type: 'number', message: '个税必须为数字', trigger: 'blur' },
    ],
    waterElectricityFee: [
        { required: true, message: '请输入水电费', trigger: 'blur' },
        { type: 'number', message: '水电费必须为数字', trigger: 'blur' },
    ],
    sumSalary: [
        { required: true, message: '请输入应发金额', trigger: 'blur' },
        { type: 'number', message: '应发金额必须为数字', trigger: 'blur' }
        // 应发金额通常不为负，但允许业务调整，暂不加硬性非负校验，或根据实际业务添加
    ],
    actualSalary: [
        { required: true, message: '请输入实发工资', trigger: 'blur' },
        { type: 'number', message: '实发工资必须为数字', trigger: 'blur' }
        // 实发工资可正可负
    ],
    totalSalary: [
        { required: true, message: '请输入合计金额', trigger: 'blur' },
        { type: 'number', message: '合计金额必须为数字', trigger: 'blur' }
        // 合计金额可正可负
    ],
    remark: [
        { max: 255, message: '备注不能超过255个字符', trigger: 'blur' },
    ],
};

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data;
        } else {
            ElMessage.error(res.message || '获取部门列表失败');
        }
    } catch (error) {
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 加载员工列表
const loadEmployeeList = async () => {
    loadingEmployees.value = true;
    try {
        const res = await getEmployeeList();
        if (res.code === 200) {
            employeeList.value = res.data;
        } else {
            ElMessage.error(res.message || '获取员工列表失败');
        }
    } catch (error) {
        ElMessage.error('加载员工列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingEmployees.value = false;
    }
};

// 远程搜索员工
const handleEmployeeRemoteSearch = async (query) => {
    if (!formDepartmentId.value) {
        ElMessage.warning('请先选择部门');
        return;
    }
    
    if (!query) {
        // 如果没有查询内容，清空选项但不加载数据
        employeeList.value = [];
        return;
    }
    
    // 确保部门列表已加载
    if (departmentList.value.length === 0) {
        await loadDepartmentList();
    }
    
    loadingEmployees.value = true;
    try {
        // 使用部门ID和员工名称进行过滤搜索
        const res = await getEmployeePage({
            pageNum: 1,
            pageSize: 10,
            name: query,
            departmentId: formDepartmentId.value
        });
        
        if (res.code === 200) {
            let employees = [];
            if (res.data && res.data.list) {
                employees = res.data.list;
            } else if (Array.isArray(res.data)) {
                employees = res.data;
            }
            
            // 为每个员工添加部门名称
            employees.forEach(emp => {
                if (emp.departmentId) {
                    const dept = departmentList.value.find(d => 
                        d.departmentId === emp.departmentId || 
                        d.departmentId === parseInt(emp.departmentId, 10));
                    emp.departmentName = dept ? dept.departmentName : '未知部门';
                } else {
                    emp.departmentName = '未知部门';
                }
            });
            
            employeeList.value = employees;
        } else {
            ElMessage.error(res.message || '搜索员工失败');
        }
    } catch (error) {
        ElMessage.error('搜索员工失败: ' + (error.message || '未知错误'));
    } finally {
        loadingEmployees.value = false;
    }
};

// 加载工资数据
const loadSalaryData = async () => {
    loading.value = true;
    salaryData.value = [];

    try {
        const params = {
            page: pagination.page,
            size: pagination.size,
            employeeName: searchEmployeeName.value || undefined,
            departmentId: searchDepartmentId.value || undefined,
            yearMonth: searchYearMonth.value || undefined,
        };

        const res = await getSalaryPage(params);

        if (res.code === 200) {
            salaryData.value = res.data.list || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取工资数据失败');
        }
    } catch (error) {
        ElMessage.error('加载工资数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 处理部门选择变化
const handleDepartmentChange = (departmentId) => {
    form.employeeId = null; // 清空员工选择
    form.employeeName = '';
    
    // 同步设置 form.departmentId
    form.departmentId = departmentId;
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = null;
    form.employeeId = null;
    form.employeeName = '';
    form.departmentId = null;
    form.date = '';
    form.basicSalary = 0;
    form.performanceBonus = 0;
    form.fullAttendanceBonus = 0; 
    form.businessOperationBonus = 0;
    form.reimbursement = 0;
    form.privateAccount = 0;
    form.leaveDeduction = 0;
    form.deduction = 0;
    form.lateDeduction = 0;
    form.socialSecurityPersonal = 0;
    form.providentFund = 0;
    form.tax = 0;
    form.waterElectricityFee = 0;
    form.sumSalary = 0;
    form.actualSalary = 0;
    form.totalSalary = 0;
    form.remark = '';
    
    formDepartmentId.value = '';
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();

    try {
        if (departmentList.value.length === 0) {
            await loadDepartmentList();
        }
        
        const res = await getSalaryById(row.id);
        if (res.code === 200) {
            const data = res.data;
            Object.assign(form, data);
            form.basicSalary = data.basicSalary || 0;
            form.performanceBonus = data.performanceBonus || 0;
            form.fullAttendanceBonus = data.fullAttendanceBonus || 0;
            form.businessOperationBonus = data.businessOperationBonus || 0;
            form.reimbursement = data.reimbursement || 0;
            form.privateAccount = data.privateAccount || 0;
            form.leaveDeduction = data.leaveDeduction || 0;
            form.deduction = data.deduction || 0;
            form.lateDeduction = data.lateDeduction || 0;
            form.socialSecurityPersonal = data.socialSecurityPersonal || 0;
            form.providentFund = data.providentFund || 0;
            form.tax = data.tax || 0;
            form.waterElectricityFee = data.waterElectricityFee || 0;
            form.sumSalary = data.sumSalary || 0;
            form.actualSalary = data.actualSalary || 0;
            form.totalSalary = data.totalSalary || 0;

            if (data.departmentId) {
                form.departmentId = data.departmentId;
                formDepartmentId.value = data.departmentId;
            } else if (data.department) {
                const dept = departmentList.value.find(d => d.departmentName === data.department);
                if (dept) {
                    form.departmentId = dept.departmentId;
                    formDepartmentId.value = dept.departmentId;
                }
            }

            if (data.employeeId && data.employeeName) {
                employeeList.value = [{
                    employeeId: data.employeeId,
                    name: data.employeeName,
                    positionName: data.position || '',
                    departmentId: form.departmentId,
                    departmentName: data.department || getDepartmentName(form.departmentId)
                }];
            } else {
                 employeeList.value = [];
            }

            dialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取工资详情失败');
        }
    } catch (error) {
        ElMessage.error('获取工资详情失败: ' + (error.message || '未知错误'));
    }
};

// 确认删除工资记录
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除员工 "${row.employeeName}" 在 "${row.date}" 的工资记录吗？此操作不可撤销！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await deleteSalary(row.id);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除成功',
                        duration: 2000,
                    });
                    loadSalaryData();
                } else {
                    ElMessage.error(res.message || '删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error('删除失败: ' + (error.message || '未知错误'));
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedSalaries.value.length === 0) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }

    const ids = selectedSalaries.value.map((item) => item.id);
    const names = selectedSalaries.value
        .map((item) => `${item.employeeName}(${item.date})`)
        .join('、');

    ElMessageBox.confirm(
        `确定要删除以下员工的工资记录吗？\n${names}\n此操作不可撤销！`,
        '批量删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await batchDeleteSalary(ids);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '批量删除成功',
                        duration: 2000,
                    });
                    selectedSalaries.value = [];
                    loadSalaryData();
                } else {
                    ElMessage.error(res.message || '批量删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error(
                    '批量删除失败: ' + (error.message || '未知错误')
                );
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 员工选择改变事件
const handleEmployeeChange = (employeeId) => {
    const employee = employeeList.value.find(
        (emp) => emp.employeeId === employeeId
    );
    if (employee) {
        form.employeeName = employee.name;
    }
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                const submitData = {
                    id: form.id, 
                    employeeId: form.employeeId,
                    date: form.date,
                    basicSalary: form.basicSalary,
                    performanceBonus: form.performanceBonus,
                    fullAttendanceBonus: form.fullAttendanceBonus, 
                    businessOperationBonus: form.businessOperationBonus,
                    reimbursement: form.reimbursement,
                    privateAccount: form.privateAccount,
                    leaveDeduction: form.leaveDeduction,
                    deduction: form.deduction,
                    lateDeduction: form.lateDeduction,
                    socialSecurityPersonal: form.socialSecurityPersonal,
                    providentFund: form.providentFund,
                    tax: form.tax,
                    waterElectricityFee: form.waterElectricityFee,
                    sumSalary: form.sumSalary,
                    actualSalary: form.actualSalary,
                    totalSalary: form.totalSalary,
                    remark: form.remark,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 新增工资记录
                    res = await addSalary(submitData);
                } else {
                    // 更新工资记录
                    res = await updateSalary(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加成功'
                                : '更新成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadSalaryData();
                } else {
                    ElMessage.error(
                        res.message ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败')
                    );
                }
            } catch (error) {
                ElMessage.error('提交失败: ' + (error.message || '未知错误'));
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage.warning('请完善表单信息');
            return false;
        }
    });
};

// 表格选中行变化
const handleSelectionChange = (selection) => {
    selectedSalaries.value = selection;
};

// 搜索
const handleSearch = () => {
    pagination.page = 1;
    loadSalaryData();
};

// 重置搜索
const handleReset = () => {
    searchEmployeeName.value = '';
    searchDepartmentId.value = '';
    searchYearMonth.value = '';
    pagination.page = 1;
    loadSalaryData();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadSalaryData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadSalaryData();
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 获取部门名称
const getDepartmentName = (departmentId) => {
    const department = departmentList.value.find(
        (item) => item.departmentId === departmentId
    );
    return department ? department.departmentName : '';
};

// 格式化总工资显示
const formattedTotalSalary = computed(() => {
    return formatCurrency(form.totalSalary);
});

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadSalaryData();
});

// 新增：Excel导入相关状态和方法
const salaryImportDialogVisible = ref(false);
const salaryImportResultDialogVisible = ref(false);
const salaryImportResultData = ref(null);
const salaryImportUploadUrl = ref('/api/salary/import'); // 后端工资导入接口URL

// 打开工资导入对话框
const handleOpenSalaryImportDialog = () => {
    salaryImportDialogVisible.value = true;
};

// 处理工资导入成功事件
const handleSalaryImportSuccess = (importResult, importType) => {
    if (importType === 'salary') {
        salaryImportResultData.value = importResult;
        // 无论成功与否，都显示结果对话框
        salaryImportResultDialogVisible.value = true;
        
        // 如果有成功导入的记录，或即使有失败/跳过但仍希望刷新列表
        if (importResult.successCount > 0 || importResult.failureCount > 0 || (importResult.generalErrors && importResult.generalErrors.length > 0)) {
            loadSalaryData(); // 刷新工资列表数据
        }
        // ExcelImportDialog 组件在成功后会自动关闭，无需在此处关闭 salaryImportDialogVisible
    }
};

// 工资导入成功消息格式化
const formatSalarySuccessMsg = (importResult) => {
  return `工资数据全部导入成功！共处理 ${importResult.processedRows || 0} 行，成功导入 ${importResult.successCount || 0} 条记录。`;
};

// 工资导入部分成功/有错误消息格式化
const formatSalaryPartialSuccessMsg = (importResult) => {
  let msg = `工资数据导入处理完成。共处理 ${importResult.processedRows || 0} 行，成功 ${importResult.successCount || 0} 行，失败/跳过 ${importResult.failureCount || 0} 行。`;
  if (importResult.generalErrors && importResult.generalErrors.length > 0) {
    msg += ` 通用错误: ${importResult.generalErrors.join('; ')}`;
  }
  return msg;
};
</script>

<template>
    <div class="salary-container">
        <!-- 搜索工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchEmployeeName"
                    placeholder="搜索员工姓名"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>

                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    :loading="loadingDepartments"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>

                <el-date-picker
                    v-model="searchYearMonth"
                    type="month"
                    placeholder="选择年月"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                />

                <el-button
                    type="primary"
                    @click="handleSearch"
                >
                    <el-icon>
                        <Search />
                    </el-icon>搜索
                </el-button>

                <el-button @click="handleReset">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>

            <div class="action-box">
                <el-button
                    type="danger"
                    :disabled="selectedSalaries.length === 0"
                    @click="handleBatchDelete"
                >
                    <el-icon>
                        <Delete />
                    </el-icon>批量删除
                </el-button>

                <el-button
                    type="primary"
                    class="add-btn"
                    @click="handleAdd"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加工资记录
                </el-button>
                
                <el-button
                    type="success"
                    @click="handleOpenSalaryImportDialog"
                >
                    <el-icon><Upload /></el-icon>
                    导入工资
                </el-button>
            </div>
        </div>

        <!-- 工资表格 -->
        <el-table
            v-loading="loading"
            :data="salaryData"
            border
            row-key="id"
            @selection-change="handleSelectionChange"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            />

            <el-table-column
                label="员工姓名"
                prop="employeeName"
                min-width="100"
                show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
                label="部门"
                prop="department"
                min-width="120"
                show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
                label="职位"
                prop="position"
                min-width="120"
                show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
                label="年月"
                prop="date"
                width="100"
                align="center"
            >
                <template #default="{ row }">
                    <el-tag type="info">{{ row.date }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="基本工资"
                prop="basicSalary"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.basicSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="奖金"
                prop="performanceBonus"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.performanceBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="全勤奖"
                prop="fullAttendanceBonus"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.fullAttendanceBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                prop="businessOperationBonus"
                min-width="120"
                align="right"
            >
                <template #header>
                    <el-tooltip content="业务与操作奖金" placement="top">
                        <span>业务与操作奖金</span>
                    </el-tooltip>
                </template>
                <template #default="{ row }">
                    {{ formatCurrency(row.businessOperationBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实得金额"
                prop="sumSalary"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.sumSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="请假"
                prop="leaveDeduction"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.leaveDeduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="扣借款"
                prop="deduction"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.deduction) }}
                </template>
            </el-table-column>

            <el-table-column
                prop="lateDeduction"
                min-width="120"
                align="right"
            >
                <template #header>
                    <el-tooltip content="迟到与缺卡" placement="top">
                        <span>迟到与缺卡</span>
                    </el-tooltip>
                </template>
                <template #default="{ row }">
                    {{ formatCurrency(row.lateDeduction) }}
                </template>
            </el-table-column>

            <el-table-column
                prop="socialSecurityPersonal"
                min-width="130"
                align="right"
            >
                <template #header>
                    <el-tooltip content="社会保险费个人部分" placement="top">
                        <span>社会保险费个人部分</span>
                    </el-tooltip>
                </template>
                <template #default="{ row }">
                    {{ formatCurrency(row.socialSecurityPersonal) }}
                </template>
            </el-table-column>

            <el-table-column
                label="公积金"
                prop="providentFund"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.providentFund) }}
                </template>
            </el-table-column>

            <el-table-column
                prop="tax"
                min-width="120"
                align="right"
            >
                <template #header>
                    <el-tooltip content="代扣代缴个税" placement="top">
                        <span>代扣代缴个税</span>
                    </el-tooltip>
                </template>
                <template #default="{ row }">
                    {{ formatCurrency(row.tax) }}
                </template>
            </el-table-column>

            <el-table-column
                label="水电费"
                prop="waterElectricityFee"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.waterElectricityFee) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实发工资"
                prop="actualSalary"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="报销"
                prop="reimbursement"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.reimbursement) }}
                </template>
            </el-table-column>

            <el-table-column
                label="私帐"
                prop="privateAccount"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.privateAccount) }}
                </template>
            </el-table-column>

            <el-table-column
                label="合计"
                prop="totalSalary"
                min-width="120"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <span class="total-salary">{{ formatCurrency(row.totalSalary) }}</span>
                </template>
            </el-table-column>

            <el-table-column
                label="备注"
                prop="remark"
                min-width="150"
                align="left"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ row.remark || '-' }}
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                width="150"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加工资记录' : '编辑工资记录'"
            width="550px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-width="150px"
                class="dialog-form"
            >
                <el-form-item
                    label="部门"
                    prop="departmentId"
                    required
                >
                    <el-select
                        v-model="formDepartmentId"
                        placeholder="请选择部门"
                        style="width: 100%"
                        @change="handleDepartmentChange"
                        :loading="loadingDepartments"
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="员工"
                    prop="employeeId"
                    required
                >
                    <el-select
                        v-model="form.employeeId"
                        placeholder="请先选择部门再输入员工名称搜索"
                        :disabled="!formDepartmentId"
                        @change="handleEmployeeChange"
                        remote
                        filterable
                        :remote-method="handleEmployeeRemoteSearch"
                        :loading="loadingEmployees"
                        style="width: 100%"
                    >
                        <template #empty>
                            <p class="empty-text">{{ formDepartmentId ? '请输入员工名称搜索' : '请先选择部门' }}</p>
                        </template>
                        <el-option
                            v-for="employee in employeeList"
                            :key="employee.employeeId"
                            :label="employee.name + (employee.departmentName ? ` (${employee.departmentName}${employee.positionName ? '-' + employee.positionName : ''})` : (employee.positionName ? ` (${employee.positionName})` : ''))"
                            :value="employee.employeeId"
                        />
                    </el-select>
                    <div
                        v-if="!formDepartmentId && dialogType === 'add'"
                        class="el-form-item__help"
                    >
                    </div>
                </el-form-item>

                <el-form-item
                    label="年月"
                    prop="date"
                    required
                >
                    <el-date-picker
                        v-model="form.date"
                        type="month"
                        placeholder="选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                        :disabled="dialogType === 'edit'"
                    />
                </el-form-item>

                <el-form-item
                    label="基本工资"
                    prop="basicSalary"
                    required
                >
                    <el-input-number
                        v-model="form.basicSalary"
                        :precision="2"
                        :step="500"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="奖金"
                    prop="performanceBonus"
                    required
                >
                    <el-input-number
                        v-model="form.performanceBonus"
                        :precision="2"
                        :step="500"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="全勤奖"
                    prop="fullAttendanceBonus"
                    required
                >
                    <el-input-number
                        v-model="form.fullAttendanceBonus"
                        :precision="2"
                        :step="500"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="业务与操作奖金"
                    prop="businessOperationBonus"
                    required
                >
                    <el-input-number
                        v-model="form.businessOperationBonus"
                        :precision="2"
                        :step="500"
                        style="width: 100%"
                    />
                </el-form-item>

                <!-- 实得金额 -->
                <el-form-item
                    label="实得金额"
                    prop="sumSalary"
                    required
                >
                    <el-input-number
                        v-model="form.sumSalary"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="请假"
                    prop="leaveDeduction"
                    required
                >
                    <el-input-number
                        v-model="form.leaveDeduction"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="扣借款"
                    prop="deduction"
                    required
                >
                    <el-input-number
                        v-model="form.deduction"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="迟到与缺卡"
                    prop="lateDeduction"
                    required
                >
                    <el-input-number
                        v-model="form.lateDeduction"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="社会保险费个人部分"
                    prop="socialSecurityPersonal"
                    required
                >
                    <el-input-number
                        v-model="form.socialSecurityPersonal"
                        :precision="2"
                        :step="50"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="公积金"
                    prop="providentFund"
                    required
                >
                    <el-input-number
                        v-model="form.providentFund"
                        :precision="2"
                        :step="50"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="代扣代缴个税"
                    prop="tax"
                    required
                >
                    <el-input-number
                        v-model="form.tax"
                        :precision="2"
                        :step="50"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="水电费"
                    prop="waterElectricityFee"
                    required
                >
                    <el-input-number
                        v-model="form.waterElectricityFee"
                        :precision="2"
                        :step="10"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="实发工资"
                    prop="actualSalary"
                    required
                >
                    <el-input-number
                        v-model="form.actualSalary"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                label="报销"
                prop="reimbursement"
                required
            >
                <el-input-number
                    v-model="form.reimbursement"
                    :precision="2"
                    :step="100"
                    style="width: 100%"
                />
            </el-form-item>

            <el-form-item
                label="私帐"
                prop="privateAccount"
                required
            >
                <el-input-number
                    v-model="form.privateAccount"
                    :precision="2"
                    :step="100"
                    style="width: 100%"
                />
            </el-form-item>

                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注信息" maxlength="255" show-word-limit />
                </el-form-item>

                <el-form-item
                    label="合计"
                    prop="totalSalary"
                    required
                >
                    <el-input-number
                        v-model="form.totalSalary"
                        :precision="2"
                        :step="100"
                        style="width: 100%"
                    >
                        <template #prefix>
                            <el-icon class="salary-icon">
                                <Money />
                            </el-icon>
                        </template>
                    </el-input-number>
                </el-form-item>
            </el-form>

            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>

        <!-- 新增：工资Excel导入对话框 -->
        <ExcelImportDialog
            v-if="salaryImportDialogVisible"
            :model-value="salaryImportDialogVisible"
            @update:modelValue="salaryImportDialogVisible = $event"
            importType="salary"
            dialogTitle="导入工资数据"
            templateFileName="工资导入模板.xlsx"
            :uploadUrl="salaryImportUploadUrl"
            :maxFileSizeMB="200" 
            @import-success="handleSalaryImportSuccess"
            :successMessageFormatter="formatSalarySuccessMsg"
            :partialSuccessMessageFormatter="formatSalaryPartialSuccessMsg"
        />

        <!-- 新增：工资导入错误详情对话框 -->
        <ImportErrorsDialog
            v-if="salaryImportResultDialogVisible"
            :model-value="salaryImportResultDialogVisible"
            @update:modelValue="salaryImportResultDialogVisible = $event"
            :importResult="salaryImportResultData"
            title="工资导入结果详情"
        />
    </div>
</template>

<style scoped>
.salary-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input,
.search-box .el-select,
.search-box .el-date-picker {
    width: 220px;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.total-salary {
    font-weight: bold;
    color: #303133;
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-form::-webkit-scrollbar {
    width: 6px;
}

.dialog-form::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
}

.dialog-form::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.form-help-text {
    font-size: 12px;
    color: #909399;
    line-height: 1;
    padding-top: 4px;
    margin-left: 2px;
}

.salary-icon {
    color: #409eff;
    font-size: 16px;
    margin-right: 4px;
}

:deep(.el-form-item:last-child .el-form-item__content) {
    margin-bottom: 10px;
}

:deep(.el-form-item:last-child .el-input__wrapper) {
    background-color: #ffffff;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}

:deep(.el-form-item:last-child .el-input__inner) {
    color: #303133;
    font-size: 16px;
    font-weight: bold;
    text-align: right;
}
</style> 
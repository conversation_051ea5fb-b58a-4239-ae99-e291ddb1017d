package org.example.company_management.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import org.example.company_management.dto.ClientStatisticsDto;
import org.example.company_management.dto.DepartmentReportEditDTO;
import org.example.company_management.dto.SalesDailyReportDto;
import org.example.company_management.entity.Employee;
import org.example.company_management.entity.SalesDailyReport;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.service.SalesDailyReportService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * {{CHENGQI: 销售日报控制器}}
 * {{CHENGQI: 任务ID: P2-LD-006}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报REST API接口，遵循RESTful设计规范}}
 */
@RestController
@RequestMapping("/sales-report")
@CrossOrigin(origins = "*")
public class SalesDailyReportController {

    @Autowired
    private SalesDailyReportService salesDailyReportService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private ObjectMapper objectMapper;

    // ==================== 核心业务接口 ====================

    /**
     * 提交或更新销售日报
     * POST /sales-report
     */
    @PostMapping
    public Result<SalesDailyReport> submitReport(@Valid @RequestBody SalesDailyReportDto reportDto) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 转换DTO为实体类
            SalesDailyReport report = convertDtoToEntity(reportDto);
            report.setEmployeeId(currentEmployeeId);

            return salesDailyReportService.submitReport(report);
        } catch (Exception e) {
            return Result.error("提交日报失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前员工客户统计信息
     * GET /sales-report/my-client-statistics
     */
    @GetMapping("/my-client-statistics")
    public Result<ClientStatisticsDto> getMyClientStatistics() {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            return salesDailyReportService.getMyClientStatistics(currentEmployeeId);
        } catch (Exception e) {
            return Result.error("查询客户统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定日期的个人日报详情
     * GET /sales-report/{date}
     */
    @GetMapping("/{date}")
    public Result<SalesDailyReport> getReportByDate(
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            return salesDailyReportService.getReportByEmployeeAndDate(currentEmployeeId, date);
        } catch (Exception e) {
            return Result.error("查询日报失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取日报详情
     * GET /sales-report/detail/{id}
     */
    @GetMapping("/detail/{id}")
    public Result<SalesDailyReport> getReportById(@PathVariable Long id) {
        try {
            String currentRole = getCurrentUserRole();
            Integer currentEmployeeId = getCurrentEmployeeId();
            Integer currentDepartmentId = getCurrentDepartmentId();

            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 权限验证
            if (!hasPermissionToAccess(id, currentEmployeeId, currentRole, currentDepartmentId)) {
                return Result.error("权限不足，无法查看该日报");
            }

            return salesDailyReportService.getReportById(id);
        } catch (Exception e) {
            return Result.error("查询日报详情失败: " + e.getMessage());
        }
    }

    /**
     * 删除销售日报
     * DELETE /sales-report/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteReport(@PathVariable Long id) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            return salesDailyReportService.deleteReport(id, currentEmployeeId);
        } catch (Exception e) {
            return Result.error("删除日报失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除销售日报（管理员权限）
     * DELETE /sales-report/batch-delete
     */
    @DeleteMapping("/batch-delete")
    public Result<Void> batchDeleteReports(@RequestBody Map<String, List<Long>> requestBody) {
        try {
            List<Long> ids = requestBody.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.error("ID列表不能为空");
            }

            // 权限验证，只有管理员可以批量删除
            String currentRole = getCurrentUserRole();
            if (!"admin".equals(currentRole)) {
                return Result.error("权限不足，只有管理员可以批量删除");
            }

            return salesDailyReportService.batchDeleteReports(ids);
        } catch (Exception e) {
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    // ==================== 查询接口 ====================

    /**
     * 分页查询销售日报列表
     * GET /sales-report/page
     */
    @GetMapping("/page")
    public Result<PageResult<SalesDailyReport>> getReportPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer employeeId,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) List<Integer> departmentIds,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(required = false) String responsibilityLevel) {
        try {
            // 根据当前用户角色进行权限过滤
            String currentRole = getCurrentUserRole();
            Integer currentEmployeeId = getCurrentEmployeeId();
            Integer currentDepartmentId = getCurrentDepartmentId();

            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            Map<String, Object> params = new HashMap<>();
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            params.put("responsibilityLevel", responsibilityLevel);

            // 根据角色添加权限过滤
            if ("employee".equals(currentRole)) {
                // 普通员工只能查看自己的日报
                params.put("employeeId", currentEmployeeId);
            } else if ("manager".equals(currentRole)) {
                // 部门经理可以查看本部门员工的日报
                if (currentDepartmentId != null) {
                    params.put("departmentId", currentDepartmentId);
                } else {
                    // 如果经理没有部门信息，只能查看自己的日报
                    params.put("employeeId", currentEmployeeId);
                }
            } else if ("admin".equals(currentRole)) {
                // 管理员可以查看所有日报，使用前端传入的参数
                params.put("employeeId", employeeId);
                params.put("departmentId", departmentId);
                params.put("departmentIds", departmentIds);
            } else {
                // 未知角色，只能查看自己的日报
                params.put("employeeId", currentEmployeeId);
            }

            PageResult<SalesDailyReport> pageResult = salesDailyReportService.getReportPage(pageNum, pageSize, params);
            return Result.success("查询成功", pageResult);
        } catch (Exception e) {
            return Result.error("分页查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取员工负责的客户列表（支持搜索，限制最多10个）
     * GET /sales-report/my-clients
     */
    @GetMapping("/my-clients")
    public Result<List<Map<String, Object>>> getMyClients(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 支持keyword和name两种参数，keyword优先
            String searchName = keyword != null ? keyword : name;

            // 调用Service获取员工负责的客户（支持搜索和限制数量）
            return salesDailyReportService.getMyClients(currentEmployeeId, searchName, category, status);
        } catch (Exception e) {
            return Result.error("查询客户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取员工最近的日报
     * GET /sales-report/recent
     */
    @GetMapping("/recent")
    public Result<List<SalesDailyReport>> getRecentReports(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            return salesDailyReportService.getRecentReports(currentEmployeeId, limit);
        } catch (Exception e) {
            return Result.error("查询最近日报失败: " + e.getMessage());
        }
    }

    /**
     * 部门负责人编辑员工日报
     * PUT /sales-report/department/edit
     */
    @PutMapping("/department/edit")
    public Result<String> editDepartmentReport(@Valid @RequestBody DepartmentReportEditDTO dto) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            String currentRole = getCurrentUserRole();
            Integer currentDepartmentId = getCurrentDepartmentId();

            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 权限检查：只有部门经理和管理员可以编辑
            if (!"manager".equals(currentRole) && !"admin".equals(currentRole)) {
                return Result.error("权限不足，只有部门负责人和管理员可以编辑日报");
            }

            // 检查是否有权限编辑该日报
            if (!hasPermissionToAccess(dto.getId(), currentEmployeeId, currentRole, currentDepartmentId)) {
                return Result.error("权限不足，无法编辑该日报");
            }

            return salesDailyReportService.editDepartmentReport(dto, currentEmployeeId);
        } catch (Exception e) {
            return Result.error("编辑日报失败: " + e.getMessage());
        }
    }

    /**
     * 更新日报评价
     * PUT /sales-report/evaluation
     */
    @PutMapping("/evaluation")
    public Result<String> updateEvaluation(
            @RequestParam Long reportId,
            @RequestParam String managerEvaluation) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            String currentRole = getCurrentUserRole();
            Integer currentDepartmentId = getCurrentDepartmentId();

            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 权限检查：只有部门经理和管理员可以评价
            if (!"manager".equals(currentRole) && !"admin".equals(currentRole)) {
                return Result.error("权限不足，只有部门负责人和管理员可以评价日报");
            }

            // 检查是否有权限评价该日报
            if (!hasPermissionToAccess(reportId, currentEmployeeId, currentRole, currentDepartmentId)) {
                return Result.error("权限不足，无法评价该日报");
            }

            return salesDailyReportService.updateEvaluation(reportId, managerEvaluation, currentEmployeeId);
        } catch (Exception e) {
            return Result.error("更新评价失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取当前登录的员工ID
     *
     * @return 当前登录的员工ID，如果未登录则返回null
     */
    private Integer getCurrentEmployeeId() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("employeeId")) {
            Object idObject = employeeInfo.get("employeeId");
            if (idObject instanceof Integer) {
                return (Integer) idObject;
            } else if (idObject instanceof Number) {
                return ((Number) idObject).intValue();
            }
        }
        return null;
    }

    /**
     * 获取当前登录用户的角色
     *
     * @return 当前登录用户的角色，如果未登录则返回null
     */
    private String getCurrentUserRole() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("role")) {
            return (String) employeeInfo.get("role");
        }
        return null;
    }

    /**
     * 获取当前登录用户的部门ID
     *
     * @return 当前登录用户的部门ID，如果未登录或无部门则返回null
     */
    private Integer getCurrentDepartmentId() {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return null;
            }

            // 通过EmployeeService查询员工的部门ID
            Employee employee = employeeService.getById(currentEmployeeId);
            if (employee != null) {
                return employee.getDepartmentId();
            }
            return null;
        } catch (Exception e) {
            // 查询失败时返回null，避免影响主要业务流程
            return null;
        }
    }

    /**
     * 检查当前用户是否有权限访问指定的日报
     *
     * @param reportId 日报ID
     * @param currentEmployeeId 当前员工ID
     * @param currentRole 当前用户角色
     * @param currentDepartmentId 当前用户部门ID
     * @return 是否有权限访问
     */
    private boolean hasPermissionToAccess(Long reportId, Integer currentEmployeeId, String currentRole, Integer currentDepartmentId) {
        return salesDailyReportService.hasPermissionToAccess(reportId, currentEmployeeId, currentRole, currentDepartmentId);
    }

    /**
     * 将DTO转换为实体类
     *
     * @param dto 销售日报DTO
     * @return 销售日报实体类
     */
    private SalesDailyReport convertDtoToEntity(SalesDailyReportDto dto) {
        try {
            SalesDailyReport entity = new SalesDailyReport();

            // 设置当前日期为日报日期
            entity.setReportDate(LocalDate.now());

            // 设置基本字段
            entity.setResponsibilityLevel(dto.getResponsibilityLevel());
            entity.setDailyResults(dto.getDailyResults());
            entity.setMeetingReport(dto.getMeetingReport());
            entity.setWorkDiary(dto.getWorkDiary());

            // 注：createTime和updateTime将在Service层设置

            // 转换客户列表为JSON字符串
            if (dto.getInquiryClients() != null && !dto.getInquiryClients().isEmpty()) {
                Map<String, Object> inquiryData = new HashMap<>();
                inquiryData.put("clientIds", dto.getInquiryClients());
                inquiryData.put("lastUpdated", LocalDateTime.now().toString());
                entity.setInquiryClients(objectMapper.writeValueAsString(inquiryData));
            }

            if (dto.getShippingClients() != null && !dto.getShippingClients().isEmpty()) {
                Map<String, Object> shippingData = new HashMap<>();
                shippingData.put("clientIds", dto.getShippingClients());
                shippingData.put("lastUpdated", LocalDateTime.now().toString());
                entity.setShippingClients(objectMapper.writeValueAsString(shippingData));
            }

            if (dto.getKeyDevelopmentClients() != null && !dto.getKeyDevelopmentClients().isEmpty()) {
                Map<String, Object> keyDevData = new HashMap<>();
                keyDevData.put("clientIds", dto.getKeyDevelopmentClients());
                keyDevData.put("lastUpdated", LocalDateTime.now().toString());
                entity.setKeyDevelopmentClients(objectMapper.writeValueAsString(keyDevData));
            }

            // 转换检查清单为JSON字符串
            if (dto.getEndOfDayChecklist() != null && !dto.getEndOfDayChecklist().isEmpty()) {
                Map<String, Object> checklistData = new HashMap<>();
                checklistData.put("items", dto.getEndOfDayChecklist());
                checklistData.put("completedCount", dto.getEndOfDayChecklist().size());
                checklistData.put("totalCount", 5); // 总共5个检查项
                checklistData.put("lastUpdated", LocalDateTime.now().toString());
                entity.setEndOfDayChecklist(objectMapper.writeValueAsString(checklistData));
            }

            return entity;
        } catch (Exception e) {
            throw new RuntimeException("DTO转换为实体类失败: " + e.getMessage(), e);
        }
    }
}

// {{CHENGQI: 任务P2-LD-006完成时间: 2025-06-04 10:52:42 +08:00}}
// {{CHENGQI: 验收状态: Controller层创建完成，包含所有REST API接口}}
// {{CHENGQI: 设计原则应用: RESTful设计规范，统一异常处理，参数验证}}
// {{CHENGQI: 下一步: 执行任务P2-LD-007创建DTO类}}

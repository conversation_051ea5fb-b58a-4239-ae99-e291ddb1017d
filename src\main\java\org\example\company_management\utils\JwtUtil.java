package org.example.company_management.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.Map;

/**
 * JWT工具类
 */
@Component
public class JwtUtil {
    /**
     * 密钥
     */
    private static final Key SECRET_KEY = Keys.secretKeyFor(SignatureAlgorithm.HS256);
    
    /**
     * 有效期（毫秒）
     */
    private static final long EXPIRATION = 1000 * 60 * 60 * 24; // 24小时
    
    /**
     * 生成token
     *
     * @param claims 载荷
     * @return token
     */
    public static String generateToken(Map<String, Object> claims) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + EXPIRATION);
        
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expirationDate)
                .signWith(SECRET_KEY)
                .compact();
    }
    
    /**
     * 解析token
     *
     * @param token token
     * @return 载荷
     */
    public static Claims parseToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(SECRET_KEY)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
    
    /**
     * 校验token是否过期
     *
     * @param token token
     * @return 是否过期
     */
    public static boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }
    
    /**
     * 校验token是否有效
     *
     * @param token token
     * @return 是否有效
     */
    public static boolean validateToken(String token) {
        try {
            parseToken(token);
            return !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从token中获取用户ID
     *
     * @param token token
     * @return 用户ID
     */
    public static Integer getUserId(String token) {
        Claims claims = parseToken(token);
        return (Integer) claims.get("employeeId");
    }
    
    /**
     * 从token中获取用户名 (现在修改为获取手机号)
     *
     * @param token token
     * @return 用户手机号
     */
    public static String getUsername(String token) {
        Claims claims = parseToken(token);
        return (String) claims.get("phone");
    }
    
    /**
     * 从token中获取指定的声明
     *
     * @param token token
     * @param key 声明的key
     * @return 声明的值
     */
    public static String getClaim(String token, String key) {
        Claims claims = parseToken(token);
        return claims.get(key) != null ? claims.get(key).toString() : null;
    }
} 
package org.example.company_management.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 员工费用实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeExpense {
    /**
     * 费用记录ID (主键,自增)
     */
    private Long id;

    /**
     * 员工ID (外键,关联 employee 表)
     */
    private Long employeeId;

    /**
     * 员工姓名 (通过JOIN查询得到，非表字段)
     */
    private String employeeName;

    /**
     * 部门ID (外键,关联 department 表)
     */
    private Long departmentId;

    /**
     * 部门名称 (通过JOIN查询得到，非表字段)
     */
    private String departmentName;
    /**
     * 费用日期 (YYYY-MM-DD)
     */
    private LocalDate expenseDate;

    /**
     * 费用金额
     */
    private BigDecimal amount;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;
} 
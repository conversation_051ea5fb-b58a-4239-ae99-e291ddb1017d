package org.example.company_management.service;

import org.example.company_management.entity.Employee;

/**
 * 认证服务接口
 */
public interface AuthService {
    /**
     * 管理员登录（后台系统专用）
     *
     * @param phone    手机号
     * @param password 密码
     * @return 员工信息和token
     */
    Employee login(String phone, String password);

    /**
     * 员工客户端登录
     *
     * @param phone    手机号
     * @param password 密码
     * @return 员工信息
     */
    Employee employeeLogin(String phone, String password);

    Employee getEmployeeByPhone(String phone);

    /**
     * 根据ID查询员工信息
     *
     * @param employeeId 员工ID
     * @return 员工信息
     */
    Employee getEmployeeById(Integer employeeId);

    /**
     * 管理员注册
     *
     * @param name             姓名
     * @param phone            手机号
     * @param password         密码
     * @return 注册的管理员信息
     */
    Employee registerAdmin(String name, String phone, String password);

    /**
     * 更新员工个人信息
     *
     * @param employeeId  员工ID
     * @param name        姓名
     * @param phone       手机号
     * @param email       邮箱 (可选)
     * @param newPassword 新密码（可选）
     * @param oldPassword 旧密码（当修改密码时必填）
     * @return 更新后的员工信息
     */
    Employee updateProfile(Integer employeeId, String name, String phone, String email, String newPassword, String oldPassword);
} 
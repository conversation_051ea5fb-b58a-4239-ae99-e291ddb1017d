import request from '@/utils/request'

const api_name = '/department-expense'

/**
 * 分页查询部门开销列表
 * @param params 查询参数, 例如: { pageNum: 1, pageSize: 10, departmentId: 1, startDate: '2023-01-01', endDate: '2023-01-31', description: '办公' }
 * @returns Promise
 */
export function getDepartmentExpensePage(params) {
    return request({
        url: `${api_name}/page`,
        method: 'get',
        params
    })
}

/**
 * 批量新增部门开销
 * @param data 部门开销数据 (DepartmentExpenseDTO structure for batch)
 * @returns Promise
 */
export function addBatchDepartmentExpense(data) {
    return request({
        url: `${api_name}/batch`,
        method: 'post',
        data
    })
}

/**
 * 根据ID获取部门开销详情
 * @param id 部门开销ID
 * @returns Promise
 */
export function getDepartmentExpenseById(id) {
    return request({
        url: `${api_name}/${id}`,
        method: 'get'
    })
}

/**
 * 修改部门开销
 * @param data 部门开销数据
 * @returns Promise
 */
export function updateDepartmentExpense(data) {
    return request({
        url: api_name,
        method: 'put',
        data
    })
}

/**
 * 根据ID删除部门开销
 * @param id 部门开销ID
 * @returns Promise
 */
export function deleteDepartmentExpense(id) {
    return request({
        url: `${api_name}/${id}`,
        method: 'delete'
    })
}

/**
 * 批量删除部门开销
 * @param ids ID列表
 * @returns Promise
 */
export function batchDeleteDepartmentExpense(ids) {
    return request({
        url: `${api_name}/batch-delete`,
        method: 'post',
        data: ids
    })
}

/**
 * 批量延用部门开销 (基于选择的现有开销复制到新的月份)
 * @param data 请求体, e.g., { items: [ { departmentId, departmentName (optional), expenseDate, itemName, amount, remark }, ... ] }
 * @returns Promise
 */
export function extendBatchDepartmentExpense(data) {
    return request({
        url: `${api_name}/extend-batch`,
        method: 'post',
        data
    });
} 
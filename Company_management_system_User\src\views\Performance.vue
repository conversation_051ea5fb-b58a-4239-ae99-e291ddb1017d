<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import {
    getEmployeePerformances,
    getPerformanceDetail,
} from '../api/performance';
import { ElMessage, ElLoading } from 'element-plus';
import { Search, RefreshRight, Calendar } from '@element-plus/icons-vue';

// 业绩数据
const performanceList = ref([]);
const loading = ref(false);
// 选中的日期
const selectedMonth = ref('');
// 详情数据
const performanceDetail = ref(null);
// 详情对话框
const detailDialogVisible = ref(false);

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 加载业绩列表
const loadPerformanceList = async () => {
    try {
        loading.value = true;
        let params = {
            page: pagination.page,
            size: pagination.size,
        };

        if (selectedMonth.value) {
            // 转换日期格式 YYYY-MM-DD -> YYYY-MM
            params.date = selectedMonth.value.substring(0, 7);
        }

        const response = await getEmployeePerformances(params);

        if (response.code === 200) {
            performanceList.value = response.data || [];
            if (response.data && typeof response.data === 'object') {
                // 处理返回的分页数据
                if (Array.isArray(response.data.records)) {
                    performanceList.value = response.data.records;
                    pagination.total = response.data.total || 0;
                } else {
                    performanceList.value = response.data;
                    pagination.total = response.data.length || 0;
                }
            }
        } else {
            ElMessage.error(response.message || '获取业绩数据失败');
        }
    } catch (error) {
        console.error('获取业绩列表失败:', error);
        ElMessage.error('获取业绩列表失败，请稍后再试');
    } finally {
        loading.value = false;
    }
};

// 根据日期过滤业绩
const filterByMonth = async () => {
    pagination.page = 1; // 重置页码
    await loadPerformanceList();
};

// 重置过滤条件
const resetFilter = async () => {
    selectedMonth.value = '';
    pagination.page = 1; // 重置页码
    await loadPerformanceList();
};

// 查看详情
const viewDetail = async (row) => {
    try {
        loading.value = true;
        const response = await getPerformanceDetail(row.date);

        if (response.code === 200) {
            performanceDetail.value = response.data;
            detailDialogVisible.value = true;
        } else {
            ElMessage.error(response.message || '获取业绩详情失败');
        }
    } catch (error) {
        console.error('获取业绩详情失败:', error);
        ElMessage.error('获取业绩详情失败，请稍后再试');
    } finally {
        loading.value = false;
    }
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadPerformanceList();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadPerformanceList();
};

// 计算完成率
const getCompletionRate = (estimated, actual) => {
    if (!estimated || estimated === 0) return 0;
    return Math.round((actual / estimated) * 100);
};

// 获取完成状态
const getCompletionStatus = (rate) => {
    if (rate >= 100) return 'success';
    if (rate >= 80) return 'warning';
    return 'exception';
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    // 检查日期是否为YYYY-MM格式
    const datePattern = /^\d{4}-\d{2}$/;
    if (datePattern.test(dateString)) {
        const [year, month] = dateString.split('-');
        return `${year}年${month}月`;
    }
    return dateString;
};

// 初始加载
onMounted(() => {
    loadPerformanceList();
});
</script>

<template>
    <div class="performance-container">
        <!-- 搜索工具栏 -->
        <div class="toolbar">
            <div class="filter-actions">
                <el-date-picker
                    v-model="selectedMonth"
                    type="month"
                    placeholder="选择月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM-DD"
                    :prefix-icon="Calendar"
                    @change="filterByMonth"
                    clearable
                />
                <el-button
                    @click="resetFilter"
                    :disabled="!selectedMonth"
                >
                    <el-icon>
                        <RefreshRight />
                    </el-icon>
                    重置
                </el-button>
            </div>
        </div>

        <!-- 业绩表格 -->
        <el-table
            :data="performanceList"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            row-key="id"
            :max-height="'calc(100vh - 280px)'"
            class="custom-table"
        >
            <el-table-column
                label="年月"
                prop="date"
                width="120"
                align="center"
            >
                <template #default="{ row }">
                    <el-tag type="info">{{ row.date }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="预估业绩"
                prop="estimatedPerformance"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.estimatedPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实际业绩"
                prop="actualPerformance"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月备用金"
                prop="totalPettyCash"
                min-width="150"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.totalPettyCash) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月发布工资"
                min-width="150"
                align="right"
                prop="totalSalary" 
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <span
                        v-if="row.totalSalary !== null && row.totalSalary !== undefined"
                    >
                        {{ formatCurrency(row.totalSalary) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月平均部门开销"
                prop="averageDepartmentExpense"
                min-width="170" 
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                    {{ formatCurrency(row.averageDepartmentExpense) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月员工费用"
                prop="totalEmployeeOtherExpenses"
                min-width="160"
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                    {{ formatCurrency(row.totalEmployeeOtherExpenses) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月预计盈亏"
                prop="estimatedMonthlyProfitLoss"
                min-width="160"
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                    <span class="black-bold-text">
                        {{ formatCurrency(row.estimatedMonthlyProfitLoss) }}
                    </span>
                </template>
            </el-table-column>

            <el-table-column
                label="本月实际盈亏"
                prop="actualMonthlyProfitLoss"
                min-width="160"
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                     <span class="black-bold-text">
                        {{ formatCurrency(row.actualMonthlyProfitLoss) }}
                    </span>
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                width="120"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <el-button
                        type="primary"
                        size="small"
                        @click="viewDetail(row)"
                    >
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <div
            v-if="performanceList.length === 0 && !loading"
            class="empty-block"
        >
            <el-empty description="暂无业绩数据" />
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 详情对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="业绩详情"
            width="520px"
            destroy-on-close
            class="custom-dialog"
            :close-on-click-modal="false"
        >
            <div
                v-if="performanceDetail"
                class="detail-container"
            >
                <div class="detail-header">
                    <span class="date-label">{{ formatDate(performanceDetail.date) }}</span>
                    <div class="completion-rate">
                        <el-progress
                            type="circle"
                            :percentage="getCompletionRate(performanceDetail.estimatedPerformance, performanceDetail.actualPerformance)"
                            :status="getCompletionRate(performanceDetail.estimatedPerformance, performanceDetail.actualPerformance) >= 100 ? 'success' : 'warning'"
                            :width="80"
                        ></el-progress>
                        <span class="rate-text">完成率</span>
                    </div>
                </div>

                <div class="detail-content">
                    <div class="detail-item">
                        <span class="detail-label">预估业绩</span>
                        <span class="detail-value">{{ formatCurrency(performanceDetail.estimatedPerformance) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">实际业绩</span>
                        <span class="detail-value highlight">{{ formatCurrency(performanceDetail.actualPerformance) }}</span>
                    </div>
                    <el-divider></el-divider>
                    <div
                        class="detail-item"
                        v-if="performanceDetail.totalSalary !== null && performanceDetail.totalSalary !== undefined"
                    >
                        <span class="detail-label">总工资</span>
                        <span class="detail-value total">{{ formatCurrency(performanceDetail.totalSalary) }}</span>
                    </div>

                    <div class="detail-item" v-if="performanceDetail.totalPettyCash !== null && performanceDetail.totalPettyCash !== undefined">
                        <span class="detail-label">本月备用金</span>
                        <span class="detail-value">{{ formatCurrency(performanceDetail.totalPettyCash) }}</span>
                    </div>
                    <div class="detail-item" v-if="performanceDetail.averageDepartmentExpense !== null && performanceDetail.averageDepartmentExpense !== undefined">
                        <span class="detail-label">本月平均部门开销</span>
                        <span class="detail-value">{{ formatCurrency(performanceDetail.averageDepartmentExpense) }}</span>
                    </div>
                    <div class="detail-item" v-if="performanceDetail.totalEmployeeOtherExpenses !== null && performanceDetail.totalEmployeeOtherExpenses !== undefined">
                        <span class="detail-label">本月员工费用</span>
                        <span class="detail-value">{{ formatCurrency(performanceDetail.totalEmployeeOtherExpenses) }}</span>
                    </div>
                    
                    <el-divider></el-divider>

                    <div class="detail-item" v-if="performanceDetail.estimatedMonthlyProfitLoss !== null && performanceDetail.estimatedMonthlyProfitLoss !== undefined">
                        <span class="detail-label">本月预计盈亏</span>
                        <span class="detail-value black-bold-text">{{ formatCurrency(performanceDetail.estimatedMonthlyProfitLoss) }}</span>
                    </div>
                    <div class="detail-item" v-if="performanceDetail.actualMonthlyProfitLoss !== null && performanceDetail.actualMonthlyProfitLoss !== undefined">
                        <span class="detail-label">本月实际盈亏</span>
                        <span class="detail-value black-bold-text">{{ formatCurrency(performanceDetail.actualMonthlyProfitLoss) }}</span>
                    </div>

                    <div class="detail-item">
                        <span class="detail-label">状态</span>
                        <el-tag
                            size="small"
                            :type="getCompletionRate(performanceDetail.estimatedPerformance, performanceDetail.actualPerformance) >= 100 ? 'success' : 'warning'"
                        >
                            {{ getCompletionRate(performanceDetail.estimatedPerformance, performanceDetail.actualPerformance) >= 100 ? '已完成' : '未完成' }}
                        </el-tag>
                    </div>
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style scoped>
.performance-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.empty-block {
    padding: 30px 0;
    text-align: center;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.detail-completion {
    display: flex;
    align-items: center;
    gap: 8px;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__header-wrapper th) {
    background-color: #f5f7fa;
    font-weight: 600;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 20px;
}

:deep(.el-descriptions__label) {
    font-weight: 600;
}

:deep(.el-tag.el-tag--info) {
    color: #909399;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-actions {
        width: 100%;
        margin-top: 10px;
    }

    .pagination-container {
        position: static;
        margin-top: 20px;
        width: 100%;
        display: flex;
        justify-content: center;
    }
}

/* 详情弹窗样式 */
.detail-container {
    padding: 0;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.date-label {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
}

.completion-rate {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.rate-text {
    font-size: 14px;
    color: #909399;
}

.detail-content {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-size: 15px;
    color: #606266;
}

.detail-value {
    font-size: 16px;
    color: #303133;
    font-weight: 500;
}

.detail-value.highlight {
    color: #409eff;
    font-weight: 600;
}

.detail-value.total {
    color: #303133;
    font-weight: 700;
}

.detail-value.salary {
    color: #303133;
    font-weight: 700;
}

:deep(.el-divider--horizontal) {
    margin: 16px 0;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 24px;
}

:deep(.custom-dialog .el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
}

/* 格式化工具函数 */
.formatDate {
    font-size: 14px;
    color: #909399;
}

.total-salary {
    font-weight: bold;
    color: #303133;
}

.black-bold-text {
    font-weight: bold;
    color: #303133;
}
</style> 
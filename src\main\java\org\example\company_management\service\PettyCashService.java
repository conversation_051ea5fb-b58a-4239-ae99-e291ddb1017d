package org.example.company_management.service;

import org.example.company_management.entity.PettyCash;
import org.example.company_management.utils.PageResult;

import java.util.List;
import java.util.Map;

public interface PettyCashService {
    
    /**
     * 分页查询备用金记录
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult<PettyCash> getPettyCashByPage(Map<String, Object> params);
    
    /**
     * 根据ID查询备用金记录
     * @param id 备用金ID
     * @return 备用金记录
     */
    PettyCash getPettyCashById(Integer id);
    
    /**
     * 添加备用金记录
     * @param pettyCash 备用金信息
     * @return 新增ID
     */
    Integer addPettyCash(PettyCash pettyCash);
    
    /**
     * 更新备用金记录
     * @param pettyCash 备用金信息
     * @return 是否成功
     */
    boolean updatePettyCash(PettyCash pettyCash);
    
    /**
     * 删除备用金记录
     * @param id 备用金ID
     * @return 是否成功
     */
    boolean deletePettyCash(Integer id);
    
    /**
     * 批量删除备用金记录
     * @param ids 备用金ID列表
     * @return 是否成功
     */
    boolean batchDeletePettyCash(List<Integer> ids);
    
    /**
     * 审核备用金申请
     * @param id 备用金ID
     * @param status 状态
     * @return 是否成功
     */
    boolean approvePettyCash(Integer id, String status);
    
    /**
     * 获取待审批的备用金列表
     * @return 待审批备用金列表
     */
    List<PettyCash> getPendingPettyCash();
    
    /**
     * 获取待审批的备用金数量
     * @return 待审批数量
     */
    int getPendingPettyCashCount();
    
    /**
     * 分页查询当前用户的备用金记录
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult<PettyCash> getMyPettyCashByPage(Map<String, Object> params);
    
    /**
     * 更新当前用户的备用金记录（仅允许更新待审核状态的记录）
     * @param id 备用金ID
     * @param pettyCash 备用金信息
     * @return 是否成功
     */
    boolean updateMyPettyCash(Integer id, PettyCash pettyCash);
    
    /**
     * 提交备用金申请到审核状态（将状态从"待审核"改为"审核中"）
     * @param id 备用金ID
     * @return 是否成功
     */
    boolean submitToApproval(Integer id);
    
    /**
     * 取消备用金申请（将状态改为"已取消"）
     * @param id 备用金ID
     * @return 是否成功
     */
    boolean cancelPettyCash(Integer id);
    
    /**
     * 根据部门ID列表分页查询备用金记录
     * @param params 查询参数 (departmentIds, page, size, purpose, status)
     * @return 分页结果
     */
    PageResult<PettyCash> getPettyCashByDepartmentIds(Map<String, Object> params);
} 
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.NotificationRuleMapper">

    <resultMap id="NotificationRuleResultMap" type="org.example.company_management.entity.NotificationRule">
        <id property="ruleId" column="rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="description" column="description"/>
        <result property="targetPositionName" column="target_position_name"/>
        <result property="conditionType" column="condition_type"/>
        <result property="messageTemplateCn" column="message_template_cn"/>
        <result property="defaultShowAgainAfterDays" column="default_show_again_after_days"/>
        <result property="priority" column="priority"/>
        <result property="isActive" column="is_active"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getAllActiveRules" resultMap="NotificationRuleResultMap">
        SELECT rule_id, rule_name, description, target_position_name, condition_type, message_template_cn, default_show_again_after_days, priority, is_active, create_time, update_time
        FROM notification_rules
        WHERE is_active = TRUE
        ORDER BY priority ASC, rule_id ASC
    </select>

    <select id="getActiveRulesByPositionName" resultMap="NotificationRuleResultMap">
        SELECT rule_id, rule_name, description, target_position_name, condition_type, message_template_cn, default_show_again_after_days, priority, is_active, create_time, update_time
        FROM notification_rules
        WHERE is_active = TRUE AND target_position_name = #{positionName}
        ORDER BY priority ASC, rule_id ASC
    </select>

    <select id="getActiveRulesForAllPositions" resultMap="NotificationRuleResultMap">
        SELECT rule_id, rule_name, description, target_position_name, condition_type, message_template_cn, default_show_again_after_days, priority, is_active, create_time, update_time
        FROM notification_rules
        WHERE is_active = TRUE AND target_position_name IS NULL
        ORDER BY priority ASC, rule_id ASC
    </select>

</mapper> 
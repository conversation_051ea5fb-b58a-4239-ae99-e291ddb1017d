<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    View,
    Loading,
} from '@element-plus/icons-vue';
import {
    getPositionPage,
    addPosition,
    updatePosition,
    deletePosition,
} from '@/api/position';
import { getDepartmentList } from '@/api/department';

// 职位数据
const positionData = ref([]);
const loading = ref(true);
const searchText = ref('');
const searchDepartmentId = ref('');

// 部门列表（用于下拉选择）
const departmentList = ref([]);
const loadingDepartments = ref(false);

// 分页设置
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 表单数据
const form = reactive({
    position_id: null,
    position_name: '',
    position_description: '',
    department_id: null,
    department_ids: [],
    status: 'Active',
});

// 表单规则
const rules = {
    position_name: [
        { required: true, message: '请输入职位名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    department_ids: [
        { required: true, message: '请选择所属部门', trigger: 'change' },
        { type: 'array', min: 1, message: '至少选择一个部门', trigger: 'change' }
    ],
    position_description: [
        { max: 255, message: '长度不能超过 255 个字符', trigger: 'blur' },
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
};

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data || [];
        } else {
            ElMessage.error(res.msg || '获取部门列表失败');
        }
    } catch (error) {
        console.error('加载部门列表失败:', error);
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 加载职位数据
const loadPositionData = async () => {
    loading.value = true;
    positionData.value = [];

    try {
        const res = await getPositionPage({
            pageNum: pagination.currentPage,
            pageSize: pagination.pageSize,
            positionName: searchText.value || undefined,
            departmentId: searchDepartmentId.value || undefined,
        });

        if (res.code === 200) {
            positionData.value = res.data.list || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.msg || '获取职位数据失败');
        }
    } catch (error) {
        console.error('加载职位数据失败:', error);
        ElMessage.error('加载职位数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.position_id = null;
    form.position_name = '';
    form.position_description = '';
    form.department_id = null;
    form.department_ids = [];
    form.status = 'Active';
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 打开编辑对话框
const handleEdit = (row) => {
    dialogType.value = 'edit';
    resetForm();

    // 将后端返回的数据格式转换为表单所需格式
    form.position_id = row.positionId;
    form.position_name = row.positionName;
    form.position_description = row.positionDescription || '';
    form.department_id = row.departmentId;
    form.department_ids = row.departmentIds || [];
    form.status = row.status;

    dialogVisible.value = true;
};

// 确认删除职位
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除职位 "${row.positionName}" 吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await deletePosition(row.positionId);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除职位成功',
                        duration: 2000,
                    });
                    loadPositionData();
                } else {
                    ElMessage({
                        type: 'error',
                        message: res.msg || '删除失败',
                        duration: 3000,
                    });
                    loading.value = false;
                }
            } catch (error) {
                console.error('删除职位失败:', error);
                ElMessage({
                    type: 'error',
                    message: '删除失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                // 准备提交的数据，转换为后端需要的格式
                const submitData = {
                    positionId: form.position_id,
                    positionName: form.position_name,
                    positionDescription: form.position_description,
                    departmentId: form.department_ids && form.department_ids.length > 0 ? form.department_ids[0] : null,
                    departmentIds: form.department_ids,
                    status: form.status,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 添加职位
                    res = await addPosition(submitData);
                } else {
                    // 更新职位
                    res = await updatePosition(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加职位成功'
                                : '更新职位成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadPositionData();
                } else {
                    ElMessage({
                        type: 'error',
                        message:
                            res.msg ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败'),
                        duration: 3000,
                    });
                }
            } catch (error) {
                console.error('提交表单失败:', error);
                ElMessage({
                    type: 'error',
                    message: '提交失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage({
                type: 'warning',
                message: '请完善表单信息',
                duration: 2000,
            });
            return false;
        }
    });
};

// 处理表格行类名
const tableRowClassName = ({ row }) => {
    if (row.status === 'Inactive') {
        return 'inactive-row';
    }
    return '';
};

// 搜索
const handleSearch = () => {
    pagination.currentPage = 1;
    loadPositionData();
};

// 刷新
const handleRefresh = () => {
    searchText.value = '';
    searchDepartmentId.value = '';
    pagination.currentPage = 1;
    loadPositionData();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    loadPositionData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    loadPositionData();
};

// 格式化时间
const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    // 只返回日期部分，不包含时间
    const date = new Date(dateTimeString);
    return date
        .toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
        })
        .replace(/\//g, '-');
};

// 获取部门名称
const getDepartmentName = (departmentId) => {
    const department = departmentList.value.find(
        (item) => item.departmentId === departmentId
    );
    return department ? department.departmentName : '';
};

// 获取多部门名称列表
const getDepartmentNames = (row) => {
    if (row.departmentNames && row.departmentNames.length > 0) {
        return row.departmentNames.join(', ');
    } else if (row.departmentId) {
        return getDepartmentName(row.departmentId);
    }
    return '';
};

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadPositionData();
});
</script>

<template>
    <div class="position-container">
        <!-- 头部搜索和操作栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchText"
                    placeholder="搜索职位名称"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    style="width: 220px"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>
                <el-button
                    type="primary"
                    @click="handleSearch"
                >搜索</el-button>
                <el-button @click="handleRefresh">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>
            <div class="action-box">
                <el-button
                    type="primary"
                    @click="handleAdd"
                    class="add-btn"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加职位
                </el-button>
            </div>
        </div>

        <!-- 表格 -->
        <el-table
            v-loading="loading"
            :data="positionData"
            border
            row-key="positionId"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="index"
                width="70"
                align="center"
                label="序号"
                fixed
                class-name="index-column"
            />
            <el-table-column
                prop="positionName"
                label="职位名称"
                min-width="150"
                show-overflow-tooltip
            />
            <el-table-column
                label="所属部门"
                min-width="150"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ getDepartmentNames(row) }}
                </template>
            </el-table-column>
            <el-table-column
                prop="positionDescription"
                label="职位描述"
                min-width="200"
                show-overflow-tooltip
            />
            <el-table-column
                prop="status"
                label="状态"
                width="100"
                align="center"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <el-tag
                        :type="row.status === 'Active' ? 'success' : 'danger'"
                        class="status-tag"
                    >
                        {{ row.status === 'Active' ? '启用' : '禁用' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                label="创建时间"
                min-width="160"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatDateTime(row.createTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="更新时间"
                min-width="160"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatDateTime(row.updateTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="180"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.currentPage"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加职位' : '编辑职位'"
            width="500px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="left"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="职位名称"
                    prop="position_name"
                    required
                >
                    <el-input
                        v-model="form.position_name"
                        placeholder="请输入职位名称"
                    />
                </el-form-item>
                <el-form-item
                    label="所属部门"
                    prop="department_ids"
                    required
                >
                    <el-select
                        v-model="form.department_ids"
                        placeholder="请选择所属部门"
                        style="width: 100%"
                        :loading="loadingDepartments"
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="职位描述"
                    prop="position_description"
                >
                    <el-input
                        v-model="form.position_description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入职位描述"
                    />
                </el-form-item>
                <el-form-item
                    label="状态"
                    prop="status"
                    required
                >
                    <el-select
                        v-model="form.status"
                        placeholder="请选择状态"
                        style="width: 100%"
                    >
                        <el-option
                            label="启用"
                            value="Active"
                        />
                        <el-option
                            label="禁用"
                            value="Inactive"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<style scoped>
.position-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input {
    width: 220px;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.status-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

:deep(.status-tag:hover) {
    transform: scale(1.1);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.inactive-row) {
    color: #c0c4cc;
    background-color: #f9f9f9;
}
</style> 
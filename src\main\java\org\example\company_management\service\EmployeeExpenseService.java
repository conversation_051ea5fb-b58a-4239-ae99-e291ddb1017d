package org.example.company_management.service;

import org.example.company_management.dto.BatchExtendExpenseRequestDTO;
import org.example.company_management.dto.EmployeeExpenseDTO;
import org.example.company_management.dto.ExtendBatchResultDTO;
import org.example.company_management.utils.PageResult;

import java.util.List;
import java.util.Map;

public interface EmployeeExpenseService {
    /**
     * 分页查询员工费用
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult getPage(Map<String, Object> params);

    /**
     * 根据ID查询员工费用
     *
     * @param id 费用ID
     * @return 员工费用DTO
     */
    EmployeeExpenseDTO getById(Long id);

    /**
     * 新增员工费用 (单条或批量)
     *
     * @param employeeExpenseDTO 员工费用DTO
     */
    void createSingleOrBatch(EmployeeExpenseDTO employeeExpenseDTO);

    /**
     * 更新员工费用
     *
     * @param employeeExpenseDTO 员工费用DTO
     */
    void update(EmployeeExpenseDTO employeeExpenseDTO);

    /**
     * 根据ID删除员工费用
     *
     * @param id 费用ID
     */
    void deleteById(Long id);

    /**
     * 批量删除员工费用
     *
     * @param ids ID列表
     */
    void batchDeleteByIds(List<Long> ids);

    /**
     * 批量延用员工费用 (基于选择的现有费用复制到新的月份)
     *
     * @param batchExtendExpenseRequestDTO 包含多个待创建费用项的请求体
     * @return ExtendBatchResultDTO 包含成功和跳过项目详情的结果
     */
    ExtendBatchResultDTO extendBatch(BatchExtendExpenseRequestDTO batchExtendExpenseRequestDTO);

    /**
     * 分页查询员工费用 (用户视图)
     *
     * @param params 查询条件 (Map 包含 pageNum, pageSize, departmentIds, employeeName, itemName, month)
     * @return PageResult 分页结果
     */
    PageResult getPageForUserView(Map<String, Object> params);
} 
server:
  port: 8080
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************
    username: root
    password: 123456
  sql:
    init:
      mode: never
      schema-locations: classpath:sql/init.sql

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: org.example.company_management.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# PageHelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true

# JWT配置
jwt:
  secret: company_management_system  # 替换为您的JWT密钥
  expiration: 86400000  # 24小时

import request from './request'; 

/**
 * 获取当前用户需要弹窗显示的通知
 * @returns {Promise<any>}
 */
export const getPopupNotifications = () => {
    return request({
        url: '/notifications/popup',
        method: 'get'
    });
};

/**
 * 获取用户铃铛中显示的通知列表
 * @returns {Promise<any>}
 */
export const getBellNotifications = () => {
    return request({
        url: '/notifications/bell',
        method: 'get'
    });
};

/**
 * 用户点击"我知道了"标记通知
 * @param {number|string} notificationId 通知ID
 * @returns {Promise<any>}
 */
export const dismissNotification = (notificationId) => {
    return request({
        url: `/notifications/${notificationId}/dismiss`,
        method: 'post'
    });
};

/**
 * 用户在铃铛中标记通知为已读
 * @param {number|string} notificationId 通知ID
 * @returns {Promise<any>}
 */
export const markAsReadInBell = (notificationId) => {
    return request({
        url: `/notifications/${notificationId}/read-in-bell`,
        method: 'post'
    });
};

/**
 * 批量处理通知（例如，用户点击"全部处理"）
 * @param {Array<number|string>} notificationIds 通知ID列表
 * @returns {Promise<any>}
 */
export const dismissMultipleNotifications = (notificationIds) => {
    return request({
        url: '/notifications/dismiss-multiple',
        method: 'post',
        data: { ids: notificationIds } // POST请求的数据通常在 data 字段
    });
};

/**
 * (新功能) 主动触发一次当前登录用户的通知分析任务
 * @returns {Promise<any>}
 */
export const triggerNotificationAnalysis = () => {
    return request({
        url: '/notifications/trigger-analysis',
        method: 'post'
        //不需要请求体
    });
}; 
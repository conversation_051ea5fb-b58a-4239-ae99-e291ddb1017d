<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.EmployeeOtherExpenseMapper">

    <!-- Assume a EmployeeOtherExpenseResultMap exists if other methods use it -->

    <select id="sumAmountByEmployeeIdAndYearMonth" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM employee_other_expense
        WHERE employee_id = #{employeeId}
          AND DATE_FORMAT(expense_date, '%Y-%m') = #{yearMonth}
    </select>

    <!-- Other mappings -->
</mapper> 
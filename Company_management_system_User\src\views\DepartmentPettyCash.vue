<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import {
    // Assuming these APIs will be created or already exist
    // getDepartmentPettyCash // This will be from a new api/pettyCash.js file
    getDepartmentPettyCash
} from '@/api/pettyCash'; // Placeholder for actual API import
import {
    getResponsibleDepartmentTree
} from '@/api/department'; // Corrected path
import { ElMessage, ElLoading } from 'element-plus';
import { Search, RefreshRight, Calendar, Tickets } from '@element-plus/icons-vue'; // Using Tickets for purpose or similar icon

// Petty Cash data
const pettyCashList = ref([]);
const loading = ref(false);
// Filter states
const purposeSearch = ref(''); // For petty cash purpose
const statusSearch = ref('');   // For petty cash status
const dateSearch = ref(''); // 新增年月筛选

// Department selection related (copied from DepartmentSalary.vue)
const loadingDepartments = ref(false);
const cascaderOptions = ref([]);
const selectedDepartments = ref([]);
const selectedDepartmentIds = ref([]);
const cascaderProps = {
    checkStrictly: true,
    emitPath: false,
    expandTrigger: 'hover',
    multiple: true,
};
const leadingDepartmentIds = ref([]);

// Pagination settings (copied from DepartmentSalary.vue)
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// MOCK API for getDepartmentPettyCash - replace with actual import later
// const getDepartmentPettyCash = async (params) => {
//     console.log('Mock API: getDepartmentPettyCash called with params:', params);
//     return new Promise((resolve) => {
//         setTimeout(() => {
//             const allMockPettyCash = [
//                 { id: 1, employee_name: '张三', employee_id: 1, department_id:1, department_name: '销售一部', purpose: '交通费补贴', amount: 200.00, status: '已审核', create_time: '2023-03-01T10:00:00Z' },
//                 { id: 2, employee_name: '李四', employee_id: 2, department_id:1, department_name: '销售一部', purpose: '客户招待', amount: 500.00, status: '审核中', create_time: '2023-03-05T14:30:00Z' },
//                 { id: 3, employee_name: '王五', employee_id: 3, department_id:2, department_name: '销售二部', purpose: '办公用品采购', amount: 150.00, status: '已拒绝', create_time: '2023-03-10T09:00:00Z' },
//             ];
//             // Basic filtering for mock
//             let filtered = allMockPettyCash;
//             if (params.departmentIds && params.departmentIds.length > 0) {
//                  filtered = filtered.filter(pc => params.departmentIds.includes(pc.department_id));
//             }
//             if (params.purpose) {
//                 filtered = filtered.filter(pc => pc.purpose.includes(params.purpose));
//             }
//             if (params.status) {
//                 filtered = filtered.filter(pc => pc.status === params.status);
//             }
//             if (params.startDate && params.endDate) {
//                  filtered = filtered.filter(pc => new Date(pc.create_time) >= new Date(params.startDate) && new Date(pc.create_time) <= new Date(params.endDate));
//             }

//             const pageData = filtered.slice((params.page - 1) * params.size, params.page * params.size);
//             resolve({ code: 200, data: { records: pageData, total: filtered.length }, message: '查询成功' });
//         }, 300);
//     });
// };


// Load petty cash list (adapted from DepartmentClient.vue)
const loadDepartmentPettyCashList = async () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.warning('请先选择要查看的部门');
        pettyCashList.value = [];
        pagination.total = 0;
        return;
    }

    let hasInvalidDepartment = false;
    for (const deptId of selectedDepartmentIds.value) {
        if (!isLeadingDepartment(deptId)) {
            hasInvalidDepartment = true;
            break;
        }
    }

    if (hasInvalidDepartment) {
        ElMessage.warning('您只能查看自己负责的部门及其下级部门的备用金数据');
        pettyCashList.value = [];
        pagination.total = 0;
        return;
    }

    try {
        loading.value = true;
        let params = {
            page: pagination.page,
            size: pagination.size,
            departmentIds: selectedDepartmentIds.value,
        };

        if (purposeSearch.value && purposeSearch.value.trim() !== '') {
            params.purpose = purposeSearch.value.trim();
        }
        if (statusSearch.value) {
            params.status = statusSearch.value;
        }
        if (dateSearch.value) { // 新增年月参数处理
            params.date = dateSearch.value;
        }

        // const response = await getDepartmentPettyCash(params); // Using the mock function
        const response = await getDepartmentPettyCash(params); // Use the imported API function

        if (response.code === 200 && response.data) {
            // pettyCashList.value = response.data.records || [];
            // pagination.total = response.data.total || 0;
            // if (pettyCashList.value.length === 0) {
            //     console.log('未查询到部门备用金记录');
            // }
            // Use handlePaginationData to process response
            if (Array.isArray(response.data.list)) { // Check if list exists
                pettyCashList.value = response.data.list;
            } else if (Array.isArray(response.data.records)) { // Fallback for records, though service now uses 'list'
                pettyCashList.value = response.data.records;
            }
             else {
                pettyCashList.value = [];
            }
            handlePaginationData(response.data); // Call the new handler
            if (pettyCashList.value.length === 0) {
                 console.log('未查询到部门备用金记录');
            }
        } else {
            ElMessage.error(response.message || '获取部门备用金数据失败');
            pettyCashList.value = []; // Clear list on error
            pagination.total = 0;    // Reset total on error
        }
    } catch (error) {
        console.error('获取部门备用金列表失败:', error);
        ElMessage.error('获取部门备用金列表失败，请稍后再试');
        pettyCashList.value = []; // Clear list on error
        pagination.total = 0;    // Reset total on error
    } finally {
        loading.value = false;
    }
};

/**
 * 处理分页数据，支持不同的API返回格式 (Adapted from DepartmentClient.vue)
 * @param {Object} data API返回的数据对象
 */
const handlePaginationData = (data) => {
    // 提取总记录数
    pagination.total = data.total || 0;
    
    // 根据API返回同步页码信息
    // 我们的新API /petty-cash/department-records 返回 pageNum 和 pageSize in PageResult
    if (data.pageNum !== undefined) {
        pagination.page = data.pageNum;
    } else if (data.page !== undefined) { // Fallback if 'page' is used
        pagination.page = data.page;
    }
    
    if (data.pageSize !== undefined) {
        pagination.size = data.pageSize;
    } else if (data.size !== undefined) { // Fallback if 'size' is used
        pagination.size = data.size;
    }
};

// Department handling functions (copied & adapted from DepartmentSalary.vue)
const isLeadingDepartment = (departmentId) => {
    if (leadingDepartmentIds.value.includes(departmentId)) return true;
    for (const option of cascaderOptions.value) {
        if (leadingDepartmentIds.value.includes(option.value)) {
            const isChildOfLeadingDept = (children, targetId) => {
                if (!children || children.length === 0) return false;
                for (const child of children) {
                    if (child.value === targetId) return true;
                    if (child.children && isChildOfLeadingDept(child.children, targetId)) return true;
                }
                return false;
            };
            if (isChildOfLeadingDept(option.children, departmentId)) return true;
        }
    }
    return false;
};

const getAllChildDepartmentIds = (departmentId) => {
    const childIds = [];
    const findDepartmentNode = (nodes, targetId) => {
        if (!nodes || !nodes.length) return null;
        for (const node of nodes) {
            if (node.value === targetId) return node;
            if (node.children && node.children.length) {
                const foundNode = findDepartmentNode(node.children, targetId);
                if (foundNode) return foundNode;
            }
        }
        return null;
    };
    const collectChildIds = (node) => {
        if (!node.children || !node.children.length) return;
        for (const child of node.children) {
            childIds.push(child.value);
            collectChildIds(child);
        }
    };
    const departmentNode = findDepartmentNode(cascaderOptions.value, departmentId);
    if (departmentNode) collectChildIds(departmentNode);
    return childIds;
};

const convertTreeToOptions = (nodes) => {
    if (!nodes || !nodes.length) return [];
    return nodes.map(node => ({
        value: node.departmentId,
        label: node.departmentName,
        children: convertTreeToOptions(node.children || [])
    }));
};

const loadDepartments = async () => {
    try {
        loadingDepartments.value = true;
        const response = await getResponsibleDepartmentTree();
        if (response.code === 200) {
            cascaderOptions.value = (response.data || []).map(dept => ({
                value: dept.departmentId,
                label: dept.departmentName,
                children: convertTreeToOptions(dept.children || [])
            }));
            leadingDepartmentIds.value = (response.data || []).map(dept => dept.departmentId);
            if (leadingDepartmentIds.value.length > 0) {
                const defaultDepartmentId = leadingDepartmentIds.value[0];
                const childDepartmentIds = getAllChildDepartmentIds(defaultDepartmentId);
                selectedDepartments.value = [defaultDepartmentId, ...childDepartmentIds];
                selectedDepartmentIds.value = [defaultDepartmentId, ...childDepartmentIds];
                await loadDepartmentPettyCashList();
            } else {
                ElMessage.warning('您没有任何可以查看备用金的部门');
            }
        } else {
            ElMessage.error(response.message || '获取部门信息失败');
        }
    } catch (error) {
        console.error('加载部门信息失败:', error);
        ElMessage.error('加载部门信息失败，请稍后再试');
    } finally {
        loadingDepartments.value = false;
    }
};

const handleCascaderChange = (values) => {
    if (!values || values.length === 0) {
        selectedDepartmentIds.value = [];
        selectedDepartments.value = [];
        pagination.page = 1;
        loadDepartmentPettyCashList();
        return;
    }
    const previousSelection = selectedDepartmentIds.value || [];
    const newlySelected = values.filter(id => !previousSelection.includes(id));
    const deselected = previousSelection.filter(id => !values.includes(id));
    let allDepartmentIds = [...values];
    let uiSelectedDepartments = [...values];

    for (const departmentId of newlySelected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            childIds.forEach(childId => {
                if (!allDepartmentIds.includes(childId)) allDepartmentIds.push(childId);
                if (!uiSelectedDepartments.includes(childId)) uiSelectedDepartments.push(childId);
            });
        }
    }
    for (const departmentId of deselected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            allDepartmentIds = allDepartmentIds.filter(id => !childIds.includes(id));
            uiSelectedDepartments = uiSelectedDepartments.filter(id => !childIds.includes(id));
        }
    }
    selectedDepartments.value = uiSelectedDepartments;
    selectedDepartmentIds.value = allDepartmentIds;
    pagination.page = 1;
    loadDepartmentPettyCashList();
};

// Search or filter
const handleSearchOrFilter = async () => {
    pagination.page = 1;
    await loadDepartmentPettyCashList();
};

// Reset filter
const resetFilter = async () => {
    purposeSearch.value = '';
    statusSearch.value = '';
    dateSearch.value = ''; // 重置年月筛选
    pagination.page = 1;
    if (selectedDepartmentIds.value && selectedDepartmentIds.value.length > 0) {
        await loadDepartmentPettyCashList();
    }
};

// Pagination handlers
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadDepartmentPettyCashList();
};

const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadDepartmentPettyCashList();
};

// Format currency
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// Format date (YYYY-MM-DD HH:mm:ss)
const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return date.toLocaleString('zh-CN', {
        year: 'numeric', month: '2-digit', day: '2-digit',
        hour: '2-digit', minute: '2-digit', second: '2-digit',
        hour12: false
    }).replace(/\//g, '-');
};

// Petty cash statuses for dropdown
const pettyCashStatuses = ref([
    { value: '审核中', label: '审核中' },
    { value: '已审核', label: '已审核' },
    { value: '已拒绝', label: '已拒绝' },
]);

// Initial load
onMounted(async () => {
    await loadDepartments();
});

</script>

<template>
    <div class="department-content-container"> 
        <!-- Toolbar -->
        <div class="toolbar">
            <div class="filter-actions">
                <el-cascader
                    v-model="selectedDepartments"
                    :options="cascaderOptions"
                    :props="cascaderProps"
                    placeholder="请选择您负责的部门"
                    clearable
                    :loading="loadingDepartments"
                    @change="handleCascaderChange"
                    style="width: 280px; margin-right: 10px;"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="2"
                />

                <el-input
                    v-model="purposeSearch"
                    placeholder="用途关键字"
                    clearable
                    @keyup.enter="handleSearchOrFilter"
                    @clear="handleSearchOrFilter"
                    style="width: 200px; margin-right: 10px;"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearchOrFilter" />
                    </template>
                </el-input>

                <el-select v-model="statusSearch" placeholder="审批状态" clearable @change="handleSearchOrFilter" style="width: 150px; margin-right: 10px;">
                    <el-option
                        v-for="item in pettyCashStatuses"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>

                <!-- 新增年月筛选器 -->
                <el-date-picker
                    v-model="dateSearch"
                    type="month"
                    placeholder="选择年月"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    clearable
                    @change="handleSearchOrFilter"
                    @clear="handleSearchOrFilter"
                    style="width: 150px; margin-right: 10px;"
                />
                
                <el-button @click="resetFilter">
                    <el-icon><RefreshRight /></el-icon>
                    重置
                </el-button>
            </div>
        </div>

        <!-- Petty Cash Table -->
        <el-table
            :data="pettyCashList"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            row-key="id" 
            :max-height="'calc(100vh - 280px)'"
            class="custom-table"
        >
            <el-table-column label="申请员工" prop="employeeName" min-width="120" align="center" />
            <el-table-column label="职位" prop="position" min-width="120" align="center" show-overflow-tooltip />
            <el-table-column label="部门" prop="department" min-width="150" align="center" show-overflow-tooltip />
            <el-table-column label="申请年月" prop="date" min-width="120" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    <span class="date-month">{{ row.date }}</span>
                </template>
            </el-table-column>
            <el-table-column label="用途" prop="purpose" min-width="200" align="center" show-overflow-tooltip />
            <el-table-column label="金额" prop="amount" min-width="120" align="center">
                <template #default="{ row }">
                    {{ formatCurrency(row.amount) }}
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="status" min-width="100" align="center">
                 <template #default="{ row }">
                    <el-tag 
                        :type="row.status === '已审核' ? 'success' : row.status === '已拒绝' ? 'danger' : (row.status === '审核中' || row.status === '待审核') ? 'primary' : 'warning'"
                        disable-transitions
                    >{{ row.status }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="create_time" min-width="160" align="center">
                <template #default="{ row }">
                    {{ formatDate(row.createTime) }}
                </template>
            </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- No data tip -->
        <div v-if="!loading && pettyCashList.length === 0" class="empty-data">
            <el-empty description="暂无部门备用金数据"></el-empty>
        </div>
    </div>
</template>

<style scoped>
.department-content-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap; 
}

.custom-table {
    margin-bottom: 60px; 
    border-radius: 6px;
    overflow: hidden; 
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; 
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); 
    z-index: 1; 
}

.empty-data {
    margin: 40px 0;
    text-align: center;
}

:deep(.el-table__header-wrapper th) {
    background-color: #f5f7fa; 
    font-weight: 600;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important; 
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 添加日期样式 CSS 类 */
.date-month {
    display: inline-block;
    padding: 2px 8px;
    background-color: rgb(244, 244, 245);
    color: #909399;
    border-radius: 4px;
    font-weight: 500;
    border: 1px solid #e9e9eb;
}
</style> 
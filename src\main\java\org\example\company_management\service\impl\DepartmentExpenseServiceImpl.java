package org.example.company_management.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.DepartmentExpenseDTO;
// import org.example.company_management.dto.DepartmentExpensePageQueryDTO; // No longer used
import org.example.company_management.entity.DepartmentExpense;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.DepartmentExpenseMapper;
import org.example.company_management.service.DepartmentExpenseService;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.math.BigDecimal;

import org.example.company_management.dto.DepartmentBatchExtendExpenseRequestDTO;
import org.example.company_management.dto.DepartmentExtendedExpenseItemDTO;
import org.example.company_management.dto.DepartmentExtendBatchResultDTO;

@Service
@Slf4j
public class DepartmentExpenseServiceImpl implements DepartmentExpenseService {

    @Autowired
    private DepartmentExpenseMapper departmentExpenseMapper;

    @Override
    public PageResult getPage(Map<String, Object> params) {
        // 从Map中安全地获取分页参数，提供默认值
        Integer pageNum = params.containsKey("pageNum") && params.get("pageNum") != null ? Integer.parseInt(params.get("pageNum").toString()) : 1;
        Integer pageSize = params.containsKey("pageSize") && params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;

        PageHelper.startPage(pageNum, pageSize);
        // Mapper的selectPage方法现在也期望一个Map<String, Object>
        Page<DepartmentExpense> page = departmentExpenseMapper.selectPage(params);
        // 使用四参数构造函数: pageNum, pageSize, total, list
        return new PageResult(pageNum, pageSize, page.getTotal(), page.getResult());
    }

    @Override
    public DepartmentExpenseDTO getById(Long id) {
        DepartmentExpense departmentExpense = departmentExpenseMapper.selectById(id);
        if (departmentExpense == null) {
            throw new BusinessException("部门开销记录不存在");
        }
        DepartmentExpenseDTO departmentExpenseDTO = new DepartmentExpenseDTO();
        BeanUtils.copyProperties(departmentExpense, departmentExpenseDTO);
        // 如果 departmentExpense 实体中没有 departmentName，但 DTO 需要，
        // 并且 mapper.selectById 查询 JOIN 了 department 表并填充了 department_name (如xml所示)
        // BeanUtils 可能无法直接映射 d.name 到 departmentName。
        // 需要确认 DepartmentExpense 实体是否有 departmentName 字段，或者在此处手动设置。
        // 假设DepartmentExpense实体通过@Transient或类似方式处理了department_name，或者DTO映射已考虑此情况
        if (departmentExpense.getDepartmentName() != null) { // 假设实体有 getDepartmentName() 方法
             departmentExpenseDTO.setDepartmentName(departmentExpense.getDepartmentName());
        }
        return departmentExpenseDTO;
    }

    @Override
    public void update(DepartmentExpenseDTO departmentExpenseDTO) {
        if (departmentExpenseDTO.getId() == null) {
            throw new BusinessException("更新部门开销时ID不能为空");
        }
        // 先查询是否存在，确保是更新操作且记录存在
        DepartmentExpense existingExpense = departmentExpenseMapper.selectById(departmentExpenseDTO.getId());
        if (existingExpense == null) {
            throw new BusinessException("要更新的部门开销记录不存在");
        }

        DepartmentExpense departmentExpense = new DepartmentExpense();
        BeanUtils.copyProperties(departmentExpenseDTO, departmentExpense);
        departmentExpense.setUpdateTime(LocalDateTime.now());
        // createTime 不应在更新时改变，确保 DTO 中不包含 createTime，或者 BeanUtils.copyProperties 忽略它
        // 或者从 existingExpense 中复制 createTime
        departmentExpense.setCreateTime(existingExpense.getCreateTime());

        int affectedRows = departmentExpenseMapper.update(departmentExpense);
        if (affectedRows == 0) {
            // 此处理论上不应发生，因为上面已经检查过记录存在
            throw new BusinessException("部门开销记录更新失败");
        }
        log.info("修改部门开销成功: {}", departmentExpense.getId());
    }

    @Override
    public void deleteById(Long id) {
        int affectedRows = departmentExpenseMapper.deleteById(id);
        if (affectedRows == 0) {
            throw new BusinessException("部门开销记录不存在或删除失败");
        }
        log.info("删除部门开销成功: {}", id);
    }

    @Override
    @Transactional // 批量操作建议添加事务
    public void batchDeleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("批量删除的ID列表不能为空");
        }
        int affectedRows = departmentExpenseMapper.batchDeleteByIds(ids);
        log.info("批量删除部门开销, 影响行数: {}, IDs: {}", affectedRows, ids);
        if (affectedRows != ids.size()){
             log.warn("批量删除部门开销可能部分未成功，请求删除数量: {}, 实际删除数量: {}", ids.size(), affectedRows);
             // 可以根据业务需求决定是否抛出异常或仅记录警告
        }
    }

    @Override
    @Transactional // Ensure atomicity for batch operation
    public void addBatch(DepartmentExpenseDTO dto) {
        log.info("批量新增部门开销请求 (多月份选择): {}", dto);

        List<Long> departmentIds = dto.getDepartmentIds();
        List<String> selectedMonths = dto.getExpenseMonths(); // 'YYYY-MM' strings

        if (departmentIds == null || departmentIds.isEmpty()) {
            throw new BusinessException("批量添加时部门列表不能为空");
        }
        if (selectedMonths == null || selectedMonths.isEmpty()) {
            throw new BusinessException("批量添加时开销月份列表不能为空");
        }

        for (String monthStr : selectedMonths) {
            LocalDate expenseDateForMonth;
            try {
                if (!monthStr.matches("^\\d{4}-\\d{2}$")) { // Regex to validate YYYY-MM format
                    throw new BusinessException("月份格式无效: " + monthStr + ". 请使用 YYYY-MM 格式.");
                }
                YearMonth ym = YearMonth.parse(monthStr); // Parses 'YYYY-MM'
                expenseDateForMonth = ym.atDay(1); // First day of the month
            } catch (DateTimeParseException e) {
                log.error("无效的月份格式: {} (DateTimeParseException)", monthStr, e);
                throw new BusinessException("月份格式无效: " + monthStr + ". 解析错误.");
            }

            for (Long deptId : departmentIds) {
                DepartmentExpense departmentExpense = new DepartmentExpense();
                departmentExpense.setDepartmentId(deptId);
                departmentExpense.setExpenseDate(expenseDateForMonth);
                departmentExpense.setItemName(dto.getItemName());
                departmentExpense.setAmount(dto.getAmount());
                departmentExpense.setRemark(dto.getRemark());
                departmentExpense.setCreateTime(LocalDateTime.now());
                departmentExpense.setUpdateTime(LocalDateTime.now());
                
                departmentExpenseMapper.insert(departmentExpense);
                log.debug("已插入开销记录 (批量): DeptID={}, Date={}, Item={}", deptId, expenseDateForMonth, dto.getItemName());
            }
        }
        log.info("批量新增部门开销完成 (多月份选择)");
    }

    @Transactional
    @Override
    public DepartmentExtendBatchResultDTO extendBatch(DepartmentBatchExtendExpenseRequestDTO requestDTO) {
        DepartmentExtendBatchResultDTO result = new DepartmentExtendBatchResultDTO();
        int itemsInRequest = (requestDTO.getItems() == null) ? 0 : requestDTO.getItems().size();
        result.setTotalProcessed(itemsInRequest);

        log.info("批量延用部门开销请求，共处理 {} 项", itemsInRequest);

        if (requestDTO.getItems() == null || requestDTO.getItems().isEmpty()) {
            log.warn("批量延用项目列表为空或不存在，操作终止。");
            return result;
        }

        List<String> successfullyExtendedDescriptions = new ArrayList<>();
        List<String> skippedDuplicateDescriptions = new ArrayList<>();
        int successfulCreations = 0;
        int skippedDuplicates = 0;

        for (DepartmentExtendedExpenseItemDTO itemDto : requestDTO.getItems()) {
            // Construct a user-friendly description string.
            String departmentIdentifier = (itemDto.getDepartmentName() != null && !itemDto.getDepartmentName().isEmpty()) 
                                          ? itemDto.getDepartmentName() 
                                          : "ID:" + itemDto.getDepartmentId();
            String itemDescription = String.format("部门 %s - 项目 \"%s\" - 至 %s",
                    departmentIdentifier,
                    itemDto.getItemName(),
                    itemDto.getExpenseDate().toString().substring(0, 7)); // YYYY-MM

            int existingCount = departmentExpenseMapper.countByDepartmentIdAndExpenseDateAndItemName(
                    itemDto.getDepartmentId(),
                    itemDto.getExpenseDate(), // This is the target date (YYYY-MM-01)
                    itemDto.getItemName()
            );

            if (existingCount > 0) {
                skippedDuplicates++;
                skippedDuplicateDescriptions.add(itemDescription + " (已存在)");
                log.warn("跳过重复的部门开销延用: {}", itemDescription);
            } else {
                DepartmentExpense departmentExpense = new DepartmentExpense();
                departmentExpense.setDepartmentId(itemDto.getDepartmentId());
                departmentExpense.setExpenseDate(itemDto.getExpenseDate());
                departmentExpense.setItemName(itemDto.getItemName());
                departmentExpense.setAmount(itemDto.getAmount());
                departmentExpense.setRemark(itemDto.getRemark());
                departmentExpense.setCreateTime(LocalDateTime.now());
                departmentExpense.setUpdateTime(LocalDateTime.now());

                departmentExpenseMapper.insert(departmentExpense);
                successfulCreations++;
                successfullyExtendedDescriptions.add(itemDescription);
                log.debug("已插入部门开销记录 (批量延用): {}", itemDescription);
            }
        }

        result.setSuccessfullyExtendedItems(successfullyExtendedDescriptions);
        result.setSkippedDuplicateItems(skippedDuplicateDescriptions);
        result.setTotalSuccessfullyExtended(successfulCreations);
        result.setTotalSkippedAsDuplicates(skippedDuplicates);

        log.info("批量延用部门开销完成。成功创建 {} 条记录, 跳过 {} 条重复记录。", successfulCreations, skippedDuplicates);
        return result;
    }

    @Override
    public PageResult getPageForUserView(Map<String, Object> params) {
        // 从Map中安全地获取分页参数，提供默认值
        Integer pageNum = params.containsKey("pageNum") && params.get("pageNum") != null ? Integer.parseInt(params.get("pageNum").toString()) : 1;
        Integer pageSize = params.containsKey("pageSize") && params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;

        // 处理月份参数，转换为 startDate 和 endDate
        if (params.containsKey("month") && params.get("month") != null && !params.get("month").toString().isEmpty()) {
            String monthStr = params.get("month").toString();
            if (monthStr.matches("^\\d{4}-\\d{2}$")) { // 校验 YYYY-MM 格式
                try {
                    YearMonth yearMonth = YearMonth.parse(monthStr);
                    params.put("startDate", yearMonth.atDay(1));
                    params.put("endDate", yearMonth.atEndOfMonth());
                    // params.remove("month"); // 可选：从参数中移除原始month字符串，避免混淆
                } catch (DateTimeParseException e) {
                    log.error("getPageForUserView: 无效的月份格式 provided: {}, for params: {}", monthStr, params, e);
                    throw new BusinessException("月份格式无效: " + monthStr + ". 请使用 YYYY-MM 格式.");
                }
            } else {
                 log.warn("getPageForUserView: 月份格式不符合 YYYY-MM: {}, for params: {}", monthStr, params);
                 // 可以选择抛出异常，或者如果业务允许，可以忽略此月份参数继续查询
                 // 为了严格性，这里我们选择抛出异常，因为前端的 el-date-picker type="month" 应该保证格式
                 throw new BusinessException("月份格式无效: " + monthStr + ". 必须是 YYYY-MM 格式.");
            }
        }
        // departmentIds 和 itemName 直接从params传递给mapper，mapper的XML会处理它们

        PageHelper.startPage(pageNum, pageSize);
        Page<DepartmentExpense> page = departmentExpenseMapper.selectPageForUserView(params);
        return new PageResult(pageNum, pageSize, page.getTotal(), page.getResult());
    }
} 
package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.PettyCash;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface PettyCashMapper {
    /**
     * 分页查询备用金记录
     * @param params 查询参数
     * @return 备用金列表
     */
    List<PettyCash> selectByPage(Map<String, Object> params);
    
    /**
     * 查询备用金记录总数
     * @param params 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> params);
    
    /**
     * 根据ID查询备用金记录
     * @param id 备用金ID
     * @return 备用金记录
     */
    PettyCash selectById(Integer id);
    
    /**
     * 添加备用金记录
     * @param pettyCash 备用金信息
     * @return 影响行数
     */
    int insert(PettyCash pettyCash);
    
    /**
     * 更新备用金记录
     * @param pettyCash 备用金信息
     * @return 影响行数
     */
    int update(PettyCash pettyCash);
    
    /**
     * 删除备用金记录
     * @param id 备用金ID
     * @return 影响行数
     */
    int deleteById(Integer id);
    
    /**
     * 批量删除备用金记录
     * @param ids 备用金ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(List<Integer> ids);
    
    /**
     * 审核备用金申请
     * @param params 参数，包含id和status
     * @return 影响行数
     */
    int approve(Map<String, Object> params);
    
    /**
     * 获取待审批的备用金列表（状态为"审核中"的记录）
     * @return 待审批备用金列表
     */
    List<PettyCash> selectPending();
    
    /**
     * 获取待审批的备用金数量
     * @return 待审批数量
     */
    int countPending();
    
    /**
     * 根据员工ID分页查询备用金记录
     * @param params 查询参数
     * @return 备用金列表
     */
    List<PettyCash> selectByEmployeeId(Map<String, Object> params);
    
    /**
     * 查询指定员工的备用金记录总数
     * @param params 查询参数
     * @return 总数
     */
    int countTotalByEmployeeId(Map<String, Object> params);
    
    /**
     * 提交备用金申请到审核状态（将状态从"待审核"改为"审核中"）
     * @param id 备用金ID
     * @return 影响行数
     */
    int submitToApproval(Integer id);
    
    /**
     * 取消备用金申请（将状态改为"已取消"）
     * @param id 备用金ID
     * @return 影响行数
     */
    int cancelPettyCash(Integer id);
    
    /**
     * 根据部门ID列表分页查询备用金记录
     * @param params 查询参数 (departmentIds, purpose, status, offset, limit)
     * @return 备用金列表
     */
    List<PettyCash> selectByDepartmentIds(Map<String, Object> params);
    
    /**
     * 根据部门ID列表查询备用金记录总数
     * @param params 查询参数 (departmentIds, purpose, status)
     * @return 总数
     */
    int countTotalByDepartmentIds(Map<String, Object> params);
    
    /**
     * 根据员工ID和日期（YYYY-MM）查询已批准的备用金总额。
     * "已批准"通常意味着状态不是"已拒绝"。
     *
     * @param employeeId 员工ID
     * @param date       日期 (格式 YYYY-MM)
     * @return 备用金总额，如果无记录则返回0
     */
    BigDecimal sumApprovedAmountByEmployeeIdAndDate(@Param("employeeId") Integer employeeId, @Param("date") String date);
} 
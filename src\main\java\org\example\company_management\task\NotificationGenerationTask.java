package org.example.company_management.task;

import org.example.company_management.entity.Employee;
import org.example.company_management.mapper.EmployeeMapper;
import org.example.company_management.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NotificationGenerationTask {

    private static final Logger log = LoggerFactory.getLogger(NotificationGenerationTask.class);
    private static final String BUSINESS_STAFF_POSITION_NAME = "业务员"; //与NotificationServiceImpl中保持一致

    private final NotificationService notificationService;
    private final EmployeeMapper employeeMapper; 

    @Autowired
    public NotificationGenerationTask(NotificationService notificationService, EmployeeMapper employeeMapper) {
        this.notificationService = notificationService;
        this.employeeMapper = employeeMapper;
    }

    /**
     * 定时任务，例如每天凌晨2点执行，检查并生成通知。
     * Cron表达式: 秒 分 时 日 月 周
     * "0 0 2 * * ?" 表示每天凌晨2点0分0秒执行
     */
    @Scheduled(cron = "0 0 2 * * ?") //每天凌晨2点执行
    // @Scheduled(cron = "0 */5 * * * ?") // 用于测试：每5分钟执行一次
    public void triggerNotificationChecks() {
        log.info("开始执行定时任务：检查并生成员工通知...");

        // 1. 获取所有需要检查通知的员工 (例如，所有"业务员")
        //    未来可以扩展为从配置或数据库读取目标职位/员工列表
        List<Integer> employeeIdsToCheck = employeeMapper.findEmployeeIdsByPositionName(BUSINESS_STAFF_POSITION_NAME);

        if (employeeIdsToCheck.isEmpty()) {
            log.info("没有找到职位为 '{}' 的员工，本次通知检查任务结束。", BUSINESS_STAFF_POSITION_NAME);
            return;
        }

        log.info("将为 {} 名 {} 检查通知。", employeeIdsToCheck.size(), BUSINESS_STAFF_POSITION_NAME);

        for (Integer employeeId : employeeIdsToCheck) {
            try {
                // 注意：generateNotificationsForEmployee 需要 Employee 对象，而不仅仅是ID
                // 我们需要从 employeeId 获取 Employee 对象
                // Employee employee = employeeMapper.selectById(employeeId); // 或者 findByIdWithPositionName
                Employee employee = employeeMapper.findByIdWithPositionName(employeeId); // 使用已有的包含职位信息的方法
                
                if (employee != null && "Active".equalsIgnoreCase(employee.getStatus())) { //确保员工是激活状态
                    log.debug("为员工ID {} ({}) 生成通知...", employeeId, employee.getName());
                    notificationService.generateNotificationsForEmployee(employee);
                } else if (employee == null) {
                    log.warn("未找到员工ID {} 的详细信息，跳过通知生成。", employeeId);
                } else {
                     log.info("员工ID {} ({}) 状态为 {}，跳过通知生成。", employeeId, employee.getName(), employee.getStatus());
                }
            } catch (Exception e) {
                log.error("为员工ID {} 生成通知时发生错误: {}", employeeId, e.getMessage(), e);
                // 考虑是否需要更复杂的错误处理，例如重试或标记员工
            }
        }
        log.info("定时任务：员工通知检查与生成已完成。");
    }

    /**
     * 每周日凌晨3点执行通知清理任务
     * 清理超过30天的过期通知
     */
    @Scheduled(cron = "0 0 3 * * SUN") // 每周日凌晨3点执行
    public void cleanupExpiredNotifications() {
        log.info("开始执行通知清理任务...");

        try {
            notificationService.cleanupExpiredNotifications();
            log.info("通知清理任务完成。");
        } catch (Exception e) {
            log.error("执行通知清理任务时发生错误: {}", e.getMessage(), e);
        }
    }
}
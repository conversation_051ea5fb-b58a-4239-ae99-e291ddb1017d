package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * {{CHENGQI: 销售日报实体类}}
 * {{CHENGQI: 任务ID: P1-LD-002}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报数据模型，遵循SOLID原则的单一职责设计}}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesDailyReport {
    
    /**
     * 日报ID
     */
    private Long id;
    
    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空")
    private Integer employeeId;
    
    /**
     * 日报日期
     */
    @NotNull(message = "日报日期不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate reportDate;
    
    // ==================== 统计字段（系统自动计算） ====================
    
    /**
     * 年度新客户总数
     */
    @Min(value = 0, message = "年度新客户总数不能为负数")
    private Integer yearlyNewClients;
    
    /**
     * 当月新客户总数
     */
    @Min(value = 0, message = "当月新客户总数不能为负数")
    private Integer monthlyNewClients;
    
    /**
     * 距离上次出新客户天数
     */
    @Min(value = 0, message = "距离上次出新客户天数不能为负数")
    private Integer daysSinceLastNewClient;
    
    // ==================== 客户选择字段（JSON存储） ====================
    
    /**
     * 询价客户ID列表（JSON格式）
     * 格式: {"clientIds": [1,2,3], "lastUpdated": "2025-06-04T10:00:00+08:00"}
     */
    private String inquiryClients;
    
    /**
     * 出货客户ID列表（JSON格式）
     * 格式: {"clientIds": [1,2,3], "lastUpdated": "2025-06-04T10:00:00+08:00"}
     */
    private String shippingClients;
    
    /**
     * 重点开发客户ID列表（JSON格式）
     * 格式: {"clientIds": [1,2,3], "lastUpdated": "2025-06-04T10:00:00+08:00"}
     */
    private String keyDevelopmentClients;
    
    // ==================== 评估字段 ====================
    
    /**
     * 责任心评级
     */
    @NotBlank(message = "责任心评级不能为空")
    @Pattern(regexp = "^(优秀|中等|差)$", message = "责任心评级必须为：优秀、中等、差")
    private String responsibilityLevel;
    
    // ==================== 工作检查清单 ====================
    
    /**
     * 下班准备工作检查清单（JSON格式）
     * 格式: {
     *   "items": {
     *     "deskOrganized": true,
     *     "emailsHandled": false,
     *     "meetingsCompleted": true,
     *     "materialsReady": true,
     *     "greetedLeader": false
     *   },
     *   "completedCount": 3,
     *   "totalCount": 5,
     *   "lastUpdated": "2025-06-04T10:00:00+08:00"
     * }
     */
    private String endOfDayChecklist;
    
    // ==================== 文本输入字段 ====================
    
    /**
     * 今日效果
     */
    @Size(max = 2000, message = "今日效果内容不能超过2000字符")
    private String dailyResults;
    
    /**
     * 会议报告
     */
    @Size(max = 2000, message = "会议报告内容不能超过2000字符")
    private String meetingReport;
    
    /**
     * 工作日记
     */
    @Size(max = 2000, message = "工作日记内容不能超过2000字符")
    private String workDiary;

    // ==================== 领导评价字段 ====================

    /**
     * 领导评价
     */
    @Size(max = 2000, message = "领导评价内容不能超过2000字符")
    private String managerEvaluation;

    /**
     * 评价时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime evaluationTime;

    /**
     * 评价人ID
     */
    private Integer evaluatorId;

    /**
     * 评价人姓名（非数据库字段）
     */
    private String evaluatorName;

    // ==================== 系统字段 ====================
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    // ==================== 关联对象（非数据库字段） ====================
    
    /**
     * 员工信息（非数据库字段）
     */
    private Employee employee;
    
    /**
     * 员工姓名（非数据库字段）
     */
    private String employeeName;
    
    /**
     * 部门名称（非数据库字段）
     */
    private String departmentName;
    
    // ==================== 业务方法 ====================
    
    /**
     * 检查是否为当天的日报
     * @return true如果是当天的日报
     */
    public boolean isToday() {
        return this.reportDate != null && this.reportDate.equals(LocalDate.now());
    }
    
    /**
     * 检查是否可以编辑
     * 业务规则：只能编辑当天的日报
     * @return true如果可以编辑
     */
    public boolean isEditable() {
        return isToday();
    }
    
    /**
     * 获取责任心评级的数值分数
     * @return 分数：优秀=5, 中等=3, 差=1
     */
    public int getResponsibilityScore() {
        if (responsibilityLevel == null) {
            return 0;
        }
        switch (responsibilityLevel) {
            case "优秀":
                return 5;
            case "中等":
                return 3;
            case "差":
                return 1;
            default:
                return 0;
        }
    }
    
    /**
     * 检查统计数据是否需要更新
     * 如果统计字段都为0，说明需要重新计算
     * @return true如果需要更新统计数据
     */
    public boolean needsStatisticsUpdate() {
        return (yearlyNewClients == null || yearlyNewClients == 0) &&
               (monthlyNewClients == null || monthlyNewClients == 0) &&
               (daysSinceLastNewClient == null || daysSinceLastNewClient == 0);
    }
}

// {{CHENGQI: 任务P1-LD-002完成时间: 2025-06-04 10:52:42 +08:00}}
// {{CHENGQI: 验收状态: 实体类创建完成，包含所有字段映射、验证注解、业务方法}}
// {{CHENGQI: 设计原则应用: 单一职责原则(SRP)，数据验证完整，JSON字段处理合理}}
// {{CHENGQI: 下一步: 执行任务P1-LD-003创建Mapper接口}}

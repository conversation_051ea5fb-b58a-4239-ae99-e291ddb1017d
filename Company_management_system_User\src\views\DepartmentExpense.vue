<template>
    <div class="department-expense-container">
        <el-card class="department-expense-list-card">
            <!-- 头部搜索和操作栏 -->
            <div class="toolbar">
                <div class="search-box">
                    <!-- 添加部门选择级联选择器 -->
                    <el-cascader
                        v-model="selectedDepartments"
                        :options="cascaderOptions"
                        :props="cascaderProps"
                        placeholder="请选择您负责的部门"
                        clearable
                        :loading="loadingDepartments"
                        @change="handleCascaderChange"
                        style="width: 280px; margin-right: 10px;"
                        collapse-tags
                        collapse-tags-tooltip
                        :max-collapse-tags="2"
                    />
                    <el-input
                        v-model="searchItemName"
                        placeholder="搜索项目名称"
                        clearable
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                    >
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                    <el-date-picker
                        v-model="searchDate"
                        type="month"
                        placeholder="选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM" 
                        clearable
                        style="width: 140px; margin-left: 10px; margin-right: 10px;"
                        @change="handleSearch"
                        @clear="handleSearch"
                    />
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="handleReset">
                        <el-icon><RefreshRight /></el-icon>重置
                    </el-button>
                </div>
            </div>

            <el-table
                v-loading="loading"
                :data="departmentExpenseList"
                border
                row-key="id"
                :max-height="'calc(100vh - 220px)'" 
                class="custom-table"
                :header-cell-style="{ background: '#f7f7f7', color: '#606266' }"
            >
                <el-table-column type="index" width="60" align="center" label="序号" class-name="index-column" />
                <el-table-column prop="departmentName" label="所属部门" min-width="150" show-overflow-tooltip />
                <el-table-column prop="itemName" label="项目名称" min-width="200" show-overflow-tooltip />
                <el-table-column prop="amount" label="金额" min-width="120" align="right">
                    <template #default="scope">
                        {{ formatCurrency(scope.row.amount) }}
                    </template>
                </el-table-column>
                <el-table-column prop="expenseDate" label="年月" min-width="120" show-overflow-tooltip align="center">
                    <template #default="scope">
                        <span class="date-month">{{ formatDate(scope.row.expenseDate) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip />
                <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                        {{ formatDateTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                        {{ formatDateTime(scope.row.updateTime) }}
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <div class="pagination-container">
                <el-pagination
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    v-model:currentPage="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    :page-sizes="[10, 20, 50, 100]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
            
            <!-- 添加空数据提示 -->
            <div v-if="!loading && departmentExpenseList.length === 0" class="empty-data">
                <el-empty description="暂无部门开销数据"></el-empty>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { fetchDepartmentExpenses } from '@/api/departmentExpense';
import { getResponsibleDepartmentTree } from '@/api/department';
import { useAuthStore } from '@/stores/token';
import { Search, RefreshRight } from '@element-plus/icons-vue';

const authStore = useAuthStore();

const loading = ref(false);

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const searchItemName = ref('');
const searchDate = ref('');

const loadingDepartments = ref(false);
const cascaderOptions = ref([]);
const selectedDepartments = ref([]);
const selectedDepartmentIds = ref([]);
const cascaderProps = {
    checkStrictly: true,
    emitPath: false,
    expandTrigger: 'hover',
    multiple: true,
};
const leadingDepartmentIds = ref([]);

const departmentExpenseList = ref([]);

const convertTreeToOptions = (nodes) => {
    if (!nodes || !nodes.length) return [];
    return nodes.map(node => ({
        value: node.departmentId,
        label: node.departmentName,
        children: convertTreeToOptions(node.children || [])
    }));
};

const isLeadingDepartment = (departmentId) => {
    if (leadingDepartmentIds.value.includes(departmentId)) return true;
    for (const option of cascaderOptions.value) {
        if (leadingDepartmentIds.value.includes(option.value)) {
            const isChildOfLeadingDept = (children, targetId) => {
                if (!children || children.length === 0) return false;
                for (const child of children) {
                    if (child.value === targetId) return true;
                    if (child.children && isChildOfLeadingDept(child.children, targetId)) return true;
                }
                return false;
            };
            if (isChildOfLeadingDept(option.children, departmentId)) return true;
        }
    }
    return false;
};

const loadDepartments = async () => {
    try {
        loadingDepartments.value = true;
        const response = await getResponsibleDepartmentTree();
        if (response.code === 200) {
            cascaderOptions.value = (response.data || []).map(dept => ({
                value: dept.departmentId,
                label: dept.departmentName,
                children: convertTreeToOptions(dept.children || [])
            }));
            leadingDepartmentIds.value = (response.data || []).map(dept => dept.departmentId);
            if (leadingDepartmentIds.value.length > 0) {
                const defaultDepartmentId = leadingDepartmentIds.value[0];
                const childDepartmentIds = getAllChildDepartmentIds(defaultDepartmentId);
                selectedDepartments.value = [defaultDepartmentId, ...childDepartmentIds];
                selectedDepartmentIds.value = [defaultDepartmentId, ...childDepartmentIds];
                await fetchData();
            } else {
                ElMessage.warning('您没有任何可以查看部门开销的部门');
            }
        } else {
            ElMessage.error(response.message || '获取部门信息失败');
        }
    } catch (error) {
        ElMessage.error('加载部门信息失败，请稍后再试');
    } finally {
        loadingDepartments.value = false;
    }
};

const getAllChildDepartmentIds = (departmentId) => {
    const childIds = [];
    const findDepartmentNode = (nodes, targetId) => {
        if (!nodes || !nodes.length) return null;
        for (const node of nodes) {
            if (node.value === targetId) return node;
            if (node.children && node.children.length) {
                const foundNode = findDepartmentNode(node.children, targetId);
                if (foundNode) return foundNode;
            }
        }
        return null;
    };
    const collectChildIds = (node) => {
        if (!node.children || !node.children.length) return;
        for (const child of node.children) {
            childIds.push(child.value);
            collectChildIds(child);
        }
    };
    const departmentNode = findDepartmentNode(cascaderOptions.value, departmentId);
    if (departmentNode) collectChildIds(departmentNode);
    return childIds;
};

const handleCascaderChange = (values) => {
    if (!values || values.length === 0) {
        selectedDepartmentIds.value = [];
        selectedDepartments.value = [];
        currentPage.value = 1;
        fetchData();
        return;
    }
    const previousSelection = selectedDepartmentIds.value || [];
    const newlySelected = values.filter(id => !previousSelection.includes(id));
    const deselected = previousSelection.filter(id => !values.includes(id));
    let allDepartmentIds = [...values];
    let uiSelectedDepartments = [...values];

    for (const departmentId of newlySelected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            childIds.forEach(childId => {
                if (!allDepartmentIds.includes(childId)) allDepartmentIds.push(childId);
                if (!uiSelectedDepartments.includes(childId)) uiSelectedDepartments.push(childId);
            });
        }
    }
    for (const departmentId of deselected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            allDepartmentIds = allDepartmentIds.filter(id => !childIds.includes(id));
            uiSelectedDepartments = uiSelectedDepartments.filter(id => !childIds.includes(id));
        }
    }
    selectedDepartments.value = uiSelectedDepartments;
    selectedDepartmentIds.value = allDepartmentIds;
    currentPage.value = 1;
    fetchData();
};

onMounted(() => {
    loadDepartments();
});

const fetchData = async () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.info('请选择要查询的部门后进行搜索');
        departmentExpenseList.value = [];
        total.value = 0;
        loading.value = false;
        return;
    }

    loading.value = true;
    try {
        const departmentIdsArray = Array.isArray(selectedDepartmentIds.value) 
            ? selectedDepartmentIds.value 
            : [selectedDepartmentIds.value];

        let params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            departmentIds: departmentIdsArray,
            itemName: searchItemName.value || undefined,
            month: searchDate.value || undefined,
        };

        const res = await fetchDepartmentExpenses(params);
        
        if (res.code === 200 && res.data) {
            departmentExpenseList.value = res.data.list || res.data.records || [];
            total.value = res.data.total || 0;
            
            if (departmentExpenseList.value.length === 0 && total.value === 0) {
                ElMessage.info('未查询到符合条件的部门开销数据');
            }
        } else {
            ElMessage.error(res.message || '获取部门开销列表失败');
            departmentExpenseList.value = [];
            total.value = 0;
        }
    } catch (error) {
        if (error.response) {
        }
        ElMessage.error('网络错误，获取部门开销列表失败，请稍后重试');
        departmentExpenseList.value = [];
        total.value = 0;
    } finally {
        loading.value = false;
    }
};

const formatCurrency = (value) => {
    if (value === undefined || value === null) return '¥0.00';
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2
    }).format(value);
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${year}-${month}`;
};

const formatDateTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

const handleSearch = () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.warning('请先选择要查询的部门');
        return;
    }
    currentPage.value = 1;
    fetchData();
};

const handleReset = () => {
    searchItemName.value = '';
    searchDate.value = '';
    if (selectedDepartmentIds.value && selectedDepartmentIds.value.length > 0) {
        currentPage.value = 1;
        fetchData();
    } else {
        ElMessage.warning('请先选择要查询的部门');
    }
};

const handleSizeChange = (val) => {
    pageSize.value = val;
    fetchData();
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchData();
};
</script>

<style scoped>
.department-expense-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-box .el-input {
    width: 220px;
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.el-pagination .el-select .el-input) {
    width: 120px;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center; 
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

@media screen and (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .search-box {
        width: 100%;
        flex-direction: column;
        gap: 10px;
    }
    
    .search-box .el-input,
    .search-box .el-date-picker,
    .search-box .el-button {
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
    
    .pagination-container {
        position: static;
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
}

.empty-data {
    margin: 40px 0;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

/* 添加日期样式 CSS 类 */
.date-month {
    display: inline-block;
    padding: 2px 8px;
    background-color: rgb(244, 244, 245);
    color: #909399;
    border-radius: 4px;
    font-weight: 500;
    border: 1px solid #e9e9eb;
}
</style> 
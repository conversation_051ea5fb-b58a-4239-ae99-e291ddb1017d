package org.example.company_management.service.impl;

import org.example.company_management.entity.NotificationRule;
import org.example.company_management.mapper.NotificationRuleMapper;
import org.example.company_management.service.NotificationRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NotificationRuleServiceImpl implements NotificationRuleService {

    private final NotificationRuleMapper notificationRuleMapper;

    @Autowired
    public NotificationRuleServiceImpl(NotificationRuleMapper notificationRuleMapper) {
        this.notificationRuleMapper = notificationRuleMapper;
    }

    @Override
    public List<NotificationRule> getAllActiveRules() {
        return notificationRuleMapper.getAllActiveRules();
    }

    @Override
    public List<NotificationRule> getActiveRulesByPositionName(String positionName) {
        if (positionName == null || positionName.trim().isEmpty()) {
            // Or throw an IllegalArgumentException, or return an empty list based on desired behavior
            return List.of(); 
        }
        return notificationRuleMapper.getActiveRulesByPositionName(positionName);
    }

    @Override
    public List<NotificationRule> getActiveRulesForAllPositions() {
        return notificationRuleMapper.getActiveRulesForAllPositions();
    }
} 
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/token'
import { getUserInfo } from '@/api/auth'
import { ElMessage } from 'element-plus'

// 导入视图组件
const Login = () => import('../views/Login.vue')
const Dashboard = () => import('../views/Dashboard.vue')
// const Profile = () => import('../views/Profile.vue')

// 组织管理
const Department = () => import('../views/Department.vue')
const Position = () => import('../views/Position.vue')

// 人员管理
const Employee = () => import('../views/Employee.vue')
const Client = () => import('../views/Client.vue')

// 业绩管理
const PerformanceAnalysis = () => import('../views/PerformanceAnalysis.vue')
const Salary = () => import('../views/Salary.vue')
const PettyCash = () => import('../views/PettyCash.vue') // 备用金管理
const DepartmentExpense = () => import('../views/DepartmentExpense.vue') // 新增：部门开销
const EmployeeExpense = () => import('../views/EmployeeExpense.vue') // 新增：员工费用

// {{CHENGQI: 销售日报管理}}
// {{CHENGQI: 任务ID: P3-LD-011}}
// {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
const SalesReportManagement = () => import('../views/SalesReportManagementBasic.vue') // 销售日报管理

// 欢迎页面（作为仪表盘的子路由）
const Welcome = () => import('../views/Welcome.vue')

const routes = [
	{
		path: '/',
		redirect: '/dashboard',
	},
	{
		path: '/login',
		name: 'Login',
		component: Login,
		meta: { requiresAuth: false },
	},
	{
		path: '/dashboard',
		name: 'Dashboard',
		component: Dashboard,
		meta: { requiresAuth: true },
		children: [
			{
				path: '',
				name: 'Welcome',
				component: Welcome,
				meta: { requiresAuth: true },
			},
			{
				path: '/department',
				name: 'Department',
				component: Department,
				meta: { requiresAuth: true },
			},
			{
				path: '/position',
				name: 'Position',
				component: Position,
				meta: { requiresAuth: true },
			},
			{
				path: '/employee',
				name: 'Employee',
				component: Employee,
				meta: { requiresAuth: true },
			},
			{
				path: '/client',
				name: 'Client',
				component: Client,
				meta: { requiresAuth: true },
			},
			{
				path: '/performance-analysis',
				name: 'PerformanceAnalysis',
				component: PerformanceAnalysis,
				meta: { requiresAuth: true },
			},
			{
				path: '/salary',
				name: 'Salary',
				component: Salary,
				meta: { requiresAuth: true },
			},
			{
				path: '/petty-cash',
				name: 'PettyCash',
				component: PettyCash,
				meta: { requiresAuth: true },
			},
			{
				path: '/department-expense',
				name: 'DepartmentExpense',
				component: DepartmentExpense,
				meta: { requiresAuth: true },
			},
			{
				path: '/employee-expense',
				name: 'EmployeeExpense',
				component: EmployeeExpense,
				meta: { requiresAuth: true },
			},
			// {{CHENGQI: 销售日报管理路由}}
			// {{CHENGQI: 任务ID: P3-LD-011}}
			// {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
			{
				path: '/sales-report-management',
				name: 'SalesReportManagement',
				component: SalesReportManagement,
				meta: { requiresAuth: true },
			},

		],
	},
	// 捕获所有未匹配的路由，并重定向到仪表盘
	{
		path: '/:pathMatch(.*)*',
		redirect: '/dashboard',
	},
]

const router = createRouter({
	history: createWebHistory(),
	routes,
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
	const authStore = useAuthStore()

	// 如果有token但没有用户信息，尝试获取用户信息
	if (authStore.isAuthenticated && !authStore.user) {
		try {
			const res = await getUserInfo()
			if (res.code === 200) {
				authStore.setUser(res.data)
			} else {
				// 获取用户信息失败，可能是token无效
				authStore.logout()
				ElMessage.error('登录已过期，请重新登录')
				next({ name: 'Login' })
				return
			}
		} catch (error) {
			authStore.logout()
			ElMessage.error('获取用户信息失败，请重新登录')
			next({ name: 'Login' })
			return
		}
	}

	// 需要登录的页面
	if (to.meta.requiresAuth && !authStore.isAuthenticated) {
		next({ name: 'Login' })
	}
	// 已登录用户访问登录页面，重定向到仪表盘
	else if (to.name === 'Login' && authStore.isAuthenticated) {
		next({ name: 'Dashboard' })
	} else {
		next()
	}
})

export default router

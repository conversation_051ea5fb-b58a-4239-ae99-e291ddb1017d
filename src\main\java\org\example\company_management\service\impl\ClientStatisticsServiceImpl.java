package org.example.company_management.service.impl;

import org.example.company_management.dto.ClientStatisticsDto;
import org.example.company_management.mapper.ClientMapper;
import org.example.company_management.service.ClientStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * {{CHENGQI: 客户统计服务实现类}}
 * {{CHENGQI: 任务ID: P1-LD-004}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 客户统计业务逻辑实现，遵循SOLID原则，添加缓存优化性能}}
 */
@Service
public class ClientStatisticsServiceImpl implements ClientStatisticsService {
    
    @Autowired
    private ClientMapper clientMapper;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public ClientStatisticsDto calculateClientStatistics(Integer employeeId) {
        if (employeeId == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }
        
        ClientStatisticsDto dto = new ClientStatisticsDto();
        dto.setEmployeeId(employeeId);
        dto.setYearlyNewClients(calculateYearlyNewClients(employeeId));
        dto.setMonthlyNewClients(calculateMonthlyNewClients(employeeId));
        dto.setDaysSinceLastNewClient(calculateDaysSinceLastNewClient(employeeId));
        dto.setCalculatedAt(LocalDateTime.now().format(FORMATTER));
        
        return dto;
    }
    
    @Override
    @Cacheable(value = "yearlyNewClients", key = "#employeeId + '_' + T(java.time.LocalDate).now().getYear()")
    public Integer calculateYearlyNewClients(Integer employeeId) {
        if (employeeId == null) {
            return 0;
        }

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("employeeId", employeeId);
            params.put("year", java.time.LocalDate.now().getYear());
            // 不再限制单一状态，而是排除特定状态
            // params.put("status", "已合作"); // 移除单一状态限制

            Integer count = clientMapper.countNewClientsByEmployeeAndYear(params);
            return count != null ? count : 0;
        } catch (Exception e) {
            // 记录日志但不抛出异常，返回默认值
            System.err.println("计算年度新客户总数失败: " + e.getMessage());
            return 0;
        }
    }
    
    @Override
    @Cacheable(value = "monthlyNewClients", key = "#employeeId + '_' + T(java.time.LocalDate).now().getYear() + '_' + T(java.time.LocalDate).now().getMonthValue()")
    public Integer calculateMonthlyNewClients(Integer employeeId) {
        if (employeeId == null) {
            return 0;
        }

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("employeeId", employeeId);
            params.put("year", java.time.LocalDate.now().getYear());
            params.put("month", java.time.LocalDate.now().getMonthValue());
            // 不再限制单一状态，而是排除特定状态
            // params.put("status", "已合作"); // 移除单一状态限制

            Integer count = clientMapper.countNewClientsByEmployeeAndMonth(params);
            return count != null ? count : 0;
        } catch (Exception e) {
            // 记录日志但不抛出异常，返回默认值
            System.err.println("计算当月新客户总数失败: " + e.getMessage());
            return 0;
        }
    }
    
    @Override
    @Cacheable(value = "daysSinceLastNewClient", key = "#employeeId")
    public Integer calculateDaysSinceLastNewClient(Integer employeeId) {
        if (employeeId == null) {
            return -1;
        }

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("employeeId", employeeId);
            // 不再限制单一状态，而是排除特定状态
            // params.put("status", "已合作"); // 移除单一状态限制

            Integer days = clientMapper.calculateDaysSinceLastNewClient(params);
            return days != null ? days : -1;
        } catch (Exception e) {
            // 记录日志但不抛出异常，返回默认值
            System.err.println("计算距离上次新客户天数失败: " + e.getMessage());
            return -1;
        }
    }
}

// {{CHENGQI: 任务P1-LD-004实现类完成时间: 2025-06-04 10:52:42 +08:00}}
// {{CHENGQI: 验收状态: Service实现类创建完成，包含缓存优化、异常处理、参数验证}}
// {{CHENGQI: 设计原则应用: 单一职责原则(SRP)，依赖注入，缓存优化性能}}
// {{CHENGQI: 修改时间: 2025-06-04 15:54:04 +08:00}}
// {{CHENGQI: 修改内容: 优化客户统计查询条件，不再限制单一"已合作"状态，改为排除"报价中"、"已拒绝"、"已取消"、"已删除"状态，包含"审批审核中"等其他有效状态}}

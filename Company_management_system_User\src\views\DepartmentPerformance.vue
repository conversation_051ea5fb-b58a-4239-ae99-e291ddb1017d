<script setup>
import { ref, onMounted, reactive, provide, computed } from 'vue';
import {
    getDepartmentMultiPerformances,
    getDepartmentPerformanceStats
} from '../api/performance';
import {
    getCurrentDepartment,
    getSubDepartments,
    getDepartmentById,
    getResponsibleDepartmentTree
} from '../api/department';
import { ElMessage, ElLoading } from 'element-plus';
import { Search, RefreshRight, Calendar, ArrowDown, PieChart } from '@element-plus/icons-vue';
import PerformanceStatistics from '../components/PerformanceStatistics.vue';

// 业绩数据
const performanceList = ref([]);
const loading = ref(false);
// 选中的日期范围
const dateRange = ref([]);
// 员工姓名搜索 (新增)
const employeeNameSearch = ref('');

// 业绩统计相关
const statisticsData = ref([]);
const statisticsLoading = ref(false);

// 获取当前月份的日期范围（月初和月末）
const getCurrentMonthRange = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    // 格式化为 YYYY-MM-DD
    const monthFormatted = month < 10 ? `0${month}` : month;
    const startDate = `${year}-${monthFormatted}-01`;
    const endDate = `${year}-${monthFormatted}-${new Date(year, month, 0).getDate()}`;
    
    return [startDate, endDate];
};

// 部门选择相关
const currentDepartment = ref(null);
const loadingDepartments = ref(false);

// 级联选择器相关
const cascaderOptions = ref([]);
const selectedDepartments = ref([]);
const selectedDepartmentIds = ref([]);
const cascaderProps = {
    checkStrictly: true, // 设置为true，父子节点选择状态独立
    emitPath: false, // 只返回选中的节点的值，而不是整个路径
    expandTrigger: 'hover', // 鼠标悬停时展开子节点
    multiple: true // 启用多选模式
};

// 存储用户负责的部门ID列表
const leadingDepartmentIds = ref([]);

// 业绩统计弹窗相关
const statisticsDialogVisible = ref(false);

// 判断是否是用户负责的部门
const isLeadingDepartment = (departmentId) => {
    // 直接匹配部门ID
    if (leadingDepartmentIds.value.includes(departmentId)) {
        return true;
    }
    
    // 检查是否是负责部门的子部门
    for (const option of cascaderOptions.value) {
        if (leadingDepartmentIds.value.includes(option.value)) {
            // 递归检查子部门
            const isChildOfLeadingDept = (children, targetId) => {
                if (!children || children.length === 0) {
                    return false;
                }
                
                for (const child of children) {
                    if (child.value === targetId) {
                        return true;
                    }
                    
                    if (child.children && isChildOfLeadingDept(child.children, targetId)) {
                        return true;
                    }
                }
                
                return false;
            };
            
            if (isChildOfLeadingDept(option.children, departmentId)) {
                return true;
            }
        }
    }
    
    return false;
};

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 加载部门业绩列表
const loadDepartmentPerformanceList = async () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.warning('请先选择要查看的部门');
        return;
    }
    
    let hasInvalidDepartment = false;
    for (const deptId of selectedDepartmentIds.value) {
        if (!isLeadingDepartment(deptId)) {
            hasInvalidDepartment = true;
            break;
        }
    }
    
    if (hasInvalidDepartment) {
        ElMessage.warning('您只能查看自己负责的部门及其下级部门的业绩数据');
        performanceList.value = [];
        pagination.total = 0;
        return;
    }
    
    try {
        loading.value = true;
        let params = {
            page: pagination.page,
            size: pagination.size,
            departmentIds: selectedDepartmentIds.value
        };

        if (dateRange.value && dateRange.value.length === 2) {
            params.startDate = dateRange.value[0].substring(0, 7);
            params.endDate = dateRange.value[1].substring(0, 7);
        }

        if (employeeNameSearch.value && employeeNameSearch.value.trim() !== '') { // 新增
            params.employeeName = employeeNameSearch.value.trim();
        }

        const response = await getDepartmentMultiPerformances(params);

        if (response.code === 200) {
            let records = [];
            if (response.data && typeof response.data === 'object') {
                // 处理返回的分页数据
                if (Array.isArray(response.data.records)) {
                    // 过滤掉 null 和 undefined 记录
                    records = response.data.records.filter(record => record !== null && record !== undefined);
                    
                    // 为每条记录处理 id 和 departmentName
                    records = records.map((record, index) => {
                        // 如果记录没有 id，使用日期和索引生成唯一标识
                        if (!record.id) {
                            record.id = `${record.date}_${record.departmentId || selectedDepartmentIds.value[0]}_${index}`;
                        }
                        
                        // 确保 departmentName 字段存在
                        if (!record.departmentName && record.department) {
                            record.departmentName = record.department;
                        } else if (!record.departmentName) {
                            record.departmentName = currentDepartment.value ? currentDepartment.value.name : '未知部门';
                        }
                        
                        return record;
                    });
                    
                    pagination.total = response.data.total || 0;
                } else if (Array.isArray(response.data)) {
                    // 如果直接返回数组
                    records = response.data.filter(record => record !== null && record !== undefined);
                    
                    // 处理数据
                    records = records.map((record, index) => {
                        if (!record.id) {
                            record.id = `${record.date}_${record.departmentId || selectedDepartmentIds.value[0]}_${index}`;
                        }
                        
                        if (!record.departmentName && record.department) {
                            record.departmentName = record.department;
                        } else if (!record.departmentName) {
                            record.departmentName = currentDepartment.value ? currentDepartment.value.name : '未知部门';
                        }
                        
                        return record;
                    });
                    
                    pagination.total = records.length || 0;
                } else {
                    records = [];
                    pagination.total = 0;
                }
            }
            
            performanceList.value = records;
            
            // 如果没有查询到记录，显示提示
            if (records.length === 0) {
                console.log('未查询到业绩记录');
            }
        } else {
            ElMessage.error(response.message || '获取部门业绩数据失败');
        }
    } catch (error) {
        console.error('获取部门业绩列表失败:', error);
        ElMessage.error('获取部门业绩列表失败，请稍后再试');
    } finally {
        loading.value = false;
    }
};

// 加载部门及子部门数据
const loadDepartments = async () => {
    try {
        loadingDepartments.value = true;
        
        // 获取当前用户负责的部门树
        const response = await getResponsibleDepartmentTree();
        
        if (response.code === 200) {
            // 直接使用返回的部门树数据
            const departmentTree = response.data;
            
            // 转换为级联选择器所需的格式
            cascaderOptions.value = departmentTree.map(dept => ({
                value: dept.departmentId,
                label: dept.departmentName,
                children: convertTreeToOptions(dept.children || [])
            }));
            
            // 保存用户负责的顶级部门ID列表，用于权限验证
            leadingDepartmentIds.value = departmentTree.map(dept => dept.departmentId);
            
            // 设置默认选中的部门
            if (leadingDepartmentIds.value.length > 0) {
                // 选择用户负责的第一个部门作为默认值
                const defaultDepartmentId = leadingDepartmentIds.value[0];
                
                // 获取默认部门的所有子部门ID
                const childDepartmentIds = getAllChildDepartmentIds(defaultDepartmentId);
                
                // 更新UI显示，同时勾选父部门和所有子部门
                selectedDepartments.value = [defaultDepartmentId, ...childDepartmentIds];
                
                // 更新实际用于查询的部门ID列表
                selectedDepartmentIds.value = [defaultDepartmentId, ...childDepartmentIds];
                
                // 加载业绩数据
                await loadDepartmentPerformanceList();
            } else {
                ElMessage.warning('您没有任何可以查看业绩的部门');
            }
        } else {
            ElMessage.error(response.message || '获取部门信息失败');
        }
    } catch (error) {
        console.error('加载部门信息失败:', error);
        ElMessage.error('加载部门信息失败，请稍后再试');
    } finally {
        loadingDepartments.value = false;
    }
};

// 将后端返回的树结构转换为级联选择器所需的格式
const convertTreeToOptions = (nodes) => {
    if (!nodes || !nodes.length) return [];
    
    return nodes.map(node => ({
        value: node.departmentId,
        label: node.departmentName,
        children: convertTreeToOptions(node.children || [])
    }));
};

// 递归获取部门的所有子部门ID
const getAllChildDepartmentIds = (departmentId) => {
    const childIds = [];
    
    // 找到对应的部门节点
    const findDepartmentNode = (nodes, targetId) => {
        if (!nodes || !nodes.length) return null;
        
        for (const node of nodes) {
            if (node.value === targetId) {
                return node;
            }
            
            if (node.children && node.children.length) {
                const foundNode = findDepartmentNode(node.children, targetId);
                if (foundNode) return foundNode;
            }
        }
        
        return null;
    };
    
    // 递归收集子部门ID
    const collectChildIds = (node) => {
        if (!node.children || !node.children.length) return;
        
        for (const child of node.children) {
            childIds.push(child.value);
            collectChildIds(child);
        }
    };
    
    const departmentNode = findDepartmentNode(cascaderOptions.value, departmentId);
    if (departmentNode) {
        collectChildIds(departmentNode);
    }
    
    return childIds;
};

// 处理级联选择器变化
const handleCascaderChange = (values) => {
    if (!values || values.length === 0) {
        selectedDepartmentIds.value = [];
        selectedDepartments.value = [];
        pagination.page = 1; // 重置页码
        loadDepartmentPerformanceList();
        return;
    }
    
    // 获取当前选择和之前选择的差异
    const previousSelection = selectedDepartmentIds.value || [];
    
    // 找出新选中的部门（添加的部门）
    const newlySelected = values.filter(id => !previousSelection.includes(id));
    
    // 找出被取消选择的部门（移除的部门）
    const deselected = previousSelection.filter(id => !values.includes(id));
    
    // 初始化结果数组（从用户的当前选择开始）
    let allDepartmentIds = [...values];
    let uiSelectedDepartments = [...values];
    
    // 第一步：处理新选中的父部门，自动添加其所有子部门
    for (const departmentId of newlySelected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            // 添加子部门ID到选择列表，避免重复
            for (const childId of childIds) {
                if (!allDepartmentIds.includes(childId)) {
                    allDepartmentIds.push(childId);
                }
                // 同时添加到UI显示列表，使子部门在UI上也被选中
                if (!uiSelectedDepartments.includes(childId)) {
                    uiSelectedDepartments.push(childId);
                }
            }
        }
    }
    
    // 第二步：处理被取消选择的父部门，自动取消其所有子部门
    for (const departmentId of deselected) {
        // 获取该部门的所有子部门ID
        const childIds = getAllChildDepartmentIds(departmentId);
        
        // 从结果数组中移除被取消的子部门
        if (childIds.length > 0) {
            // 从allDepartmentIds中移除所有子部门
            allDepartmentIds = allDepartmentIds.filter(id => !childIds.includes(id));
            
            // 从UI显示列表中移除所有子部门
            uiSelectedDepartments = uiSelectedDepartments.filter(id => !childIds.includes(id));
        }
    }
    
    // 更新级联选择器显示的值（包含父部门和所有子部门）
    selectedDepartments.value = uiSelectedDepartments;
    // 更新实际用于查询的部门ID列表
    selectedDepartmentIds.value = allDepartmentIds;
    
    pagination.page = 1; // 重置页码
    loadDepartmentPerformanceList();
};

// 根据日期或员工姓名过滤业绩 (重命名并修改)
const handleSearchOrFilter = async () => {
    pagination.page = 1; // 重置页码
    await loadDepartmentPerformanceList();
};

// 重置过滤条件 (修改)
const resetFilter = async () => {
    dateRange.value = []; // 清空日期范围
    employeeNameSearch.value = ''; // 新增：清空员工姓名搜索
    pagination.page = 1; // 重置页码
    await loadDepartmentPerformanceList();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadDepartmentPerformanceList();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadDepartmentPerformanceList();
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    // 检查日期是否为YYYY-MM格式
    const datePattern = /^\d{4}-\d{2}$/;
    if (datePattern.test(dateString)) {
        const [year, month] = dateString.split('-');
        return `${year}年${month}月`;
    }
    return dateString;
};

const getProfitLossClass = (value) => {
  if (value === null || value === undefined) return '';
  const numValue = parseFloat(value);
  if (numValue > 0) return 'profit-positive';
  if (numValue < 0) return 'profit-negative';
  return '';
};

// 打开业绩统计弹窗
const openStatisticsDialog = async () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.warning('请先选择要统计的部门');
        return;
    }
    
    // 检查权限
    let hasInvalidDepartment = false;
    for (const deptId of selectedDepartmentIds.value) {
        if (!isLeadingDepartment(deptId)) {
            hasInvalidDepartment = true;
            break;
        }
    }
    
    if (hasInvalidDepartment) {
        ElMessage.warning('您只能统计自己负责的部门及其下级部门的业绩数据');
        return;
    }
    
    try {
        // 设置加载状态
        statisticsLoading.value = true;
        
        // 准备请求参数
        const params = {
            departmentIds: selectedDepartmentIds.value
        };
        
        // 添加日期范围参数
        if (dateRange.value && dateRange.value.length === 2) {
            params.startDate = dateRange.value[0].substring(0, 7);
            params.endDate = dateRange.value[1].substring(0, 7);
        }
        
        // 调用统计API
        const response = await getDepartmentPerformanceStats(params);
        
        if (response.code === 200) {
            statisticsData.value = response.data || [];
            
            // 显示统计弹窗
            statisticsDialogVisible.value = true;
        } else {
            ElMessage.error(response.message || '获取部门业绩统计数据失败');
        }
    } catch (error) {
        console.error('获取部门业绩统计数据失败:', error);
        ElMessage.error('获取部门业绩统计数据失败，请稍后再试');
    } finally {
        statisticsLoading.value = false;
    }
};

// 初始加载
onMounted(async () => {
    // 设置默认日期范围为当前月份
    dateRange.value = getCurrentMonthRange();
    
    await loadDepartments();
});
</script>

<template>
    <div class="department-performance-container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="filter-actions">
                <!-- 部门级联选择器 -->
                <el-cascader
                    v-model="selectedDepartments"
                    :options="cascaderOptions"
                    :props="cascaderProps"
                    placeholder="请选择您负责的部门"
                    clearable
                    :loading="loadingDepartments"
                    @change="handleCascaderChange"
                    style="width: 280px"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="3"
                />
                
                <!-- 日期选择器 -->
                <el-date-picker
                    v-model="dateRange"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM-DD"
                    :prefix-icon="Calendar"
                    @change="handleSearchOrFilter"
                    clearable
                    style="margin-right: 10px;"
                />

                <!-- 员工姓名搜索框 (新增) -->
                <el-input
                    v-model="employeeNameSearch"
                    placeholder="请输入员工姓名"
                    clearable
                    @keyup.enter="handleSearchOrFilter"
                    @clear="handleSearchOrFilter"
                    style="width: 200px; margin-right: 10px;"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearchOrFilter" />
                    </template>
                </el-input>
                
                <el-button
                    @click="resetFilter"
                    :disabled="!dateRange || dateRange.length < 2"
                >
                    <el-icon>
                        <RefreshRight />
                    </el-icon>
                    重置
                </el-button>
                
                <!-- 业绩统计按钮 -->
                <el-button 
                    type="primary"
                    @click="openStatisticsDialog"
                    :disabled="!selectedDepartmentIds || selectedDepartmentIds.length === 0"
                >
                    <el-icon>
                        <PieChart />
                    </el-icon>
                    利润统计
                </el-button>
            </div>
        </div>

        <!-- 业绩表格 -->
        <el-table
            :data="performanceList"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :row-key="row => row.employeeId + '_' + row.date"
            :max-height="'calc(100vh - 280px)'"
            class="custom-table"
        >
            <el-table-column
                label="年月"
                prop="date"
                width="120"
                align="center"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <span v-if="row.date" class="date-format">{{ formatDate(row.date) }}</span>
                    <el-tag v-else type="info" size="small">暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="员工姓名"
                prop="employeeName"
                min-width="120"
                align="center"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ row.employeeName || '未知' }}
                </template>
            </el-table-column>

            <el-table-column
                label="部门"
                prop="departmentName"
                min-width="150"
                align="center"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ row.departmentName || row.department || '总计' }}
                </template>
            </el-table-column>

            <el-table-column
                label="预估业绩"
                prop="estimatedPerformance"
                min-width="150"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.estimatedPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实际业绩"
                prop="actualPerformance"
                min-width="150"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月备用金"
                prop="totalPettyCash"
                min-width="150"
                align="right"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <span
                        v-if="row.totalPettyCash !== null && row.totalPettyCash !== undefined"
                    >
                        {{ formatCurrency(row.totalPettyCash) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >无记录</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月发布工资"
                min-width="150"
                align="right"
                prop="totalSalary" 
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <span
                        v-if="row.totalSalary !== null && row.totalSalary !== undefined"
                    >
                        {{ formatCurrency(row.totalSalary) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >本月暂无数据</el-tag>
                </template>
            </el-table-column>

            <!-- 新增列 -->
            <el-table-column
                label="本月平均部门开销"
                prop="averageDepartmentExpense"
                min-width="170" 
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                    {{ formatCurrency(row.averageDepartmentExpense) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月员工费用"
                prop="totalEmployeeOtherExpenses"
                min-width="160"
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                    {{ formatCurrency(row.totalEmployeeOtherExpenses) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月预计盈亏"
                prop="estimatedMonthlyProfitLoss"
                min-width="160"
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                    <span :class="getProfitLossClass(row.estimatedMonthlyProfitLoss)" class="font-bold">
                        {{ formatCurrency(row.estimatedMonthlyProfitLoss) }}
                    </span>
                </template>
            </el-table-column>

            <el-table-column
                label="本月实际盈亏"
                prop="actualMonthlyProfitLoss"
                min-width="160"
                align="right"
                show-overflow-tooltip>
                <template #default="{ row }">
                     <span :class="getProfitLossClass(row.actualMonthlyProfitLoss)" class="font-bold">
                        {{ formatCurrency(row.actualMonthlyProfitLoss) }}
                    </span>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 无数据提示 -->
        <div v-if="!loading && performanceList.length === 0" class="empty-data">
            <el-empty description="暂无业绩数据"></el-empty>
        </div>
        
        <!-- 业绩统计弹窗组件 -->
        <PerformanceStatistics
            v-model:visible="statisticsDialogVisible"
            :statistics-data="statisticsData"
            :performance-list="performanceList"
            :selected-departments="selectedDepartments"
            :department-options="cascaderOptions"
            :date-range="dateRange"
            :format-currency="formatCurrency"
            :loading="statisticsLoading"
        />
    </div>
</template>

<style scoped>
.department-performance-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.custom-table {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.empty-data {
    margin: 40px 0;
    text-align: center;
}

.date-format {
    font-weight: bold;
    color: #333;
}

.total-amount {
    font-weight: bold;
    color: #333;
}

.font-bold {
    font-weight: bold;
}

.operation-column :deep(.cell) {
    padding: 0 10px;
}

:deep(.el-table__header-wrapper th) {
    background-color: #f5f7fa;
    font-weight: 600;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}


</style> 
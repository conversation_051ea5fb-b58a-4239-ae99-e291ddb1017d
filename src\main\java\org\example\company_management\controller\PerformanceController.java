package org.example.company_management.controller;

import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.entity.Performance;
import org.example.company_management.service.PerformanceService;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.example.company_management.dto.DepartmentPerformanceStatsDTO;
import org.example.company_management.dto.PerformanceImportDTO;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.utils.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业绩记录控制器
 */
@Slf4j
@RestController
@RequestMapping("/performance")
public class PerformanceController {

    @Autowired
    private PerformanceService performanceService;

    /**
     * 分页查询业绩记录
     *
     * @param page         页码
     * @param size         每页大小
     * @param employeeId   员工ID，可为null
     * @param employeeName 员工姓名，可为null，用于模糊查询
     * @param departmentId 部门ID，可为null
     * @param yearMonth    年月，可为null，格式为yyyy-MM
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<Map<String, Object>> getPerformancePage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer employeeId,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) String yearMonth) {

        try {
            // 转换空字符串为null
            employeeName = (employeeName != null && employeeName.trim().isEmpty()) ? null : employeeName;
            yearMonth = (yearMonth != null && yearMonth.trim().isEmpty()) ? null : yearMonth;

            Page<Performance> performancePage = performanceService.getPerformancePage(
                    page, size, employeeId, employeeName, departmentId, yearMonth);

            // 简化返回数据结构，只包含必要字段
            Map<String, Object> result = new HashMap<>();
            result.put("records", performancePage.getResult()); // 保持records字段以避免前端需要修改
            result.put("total", performancePage.getTotal());

            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取业绩记录失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询业绩记录
     *
     * @param id 业绩记录ID
     * @return 业绩记录
     */
    @GetMapping("/{id}")
    public Result<Performance> getPerformanceById(@PathVariable Integer id) {
        try {
            Performance performance = performanceService.getById(id);
            if (performance == null) {
                return Result.error("业绩记录不存在");
            }
            return Result.success(performance);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取业绩记录详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工的业绩记录列表
     *
     * @param page 页码，默认为1
     * @param size 每页大小，默认为10
     * @param date 可选的日期筛选参数（年月格式：YYYY-MM）
     * @return 业绩记录分页列表
     */
    @GetMapping("/employee/list")
    public Result<Map<String, Object>> getCurrentEmployeePerformances(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String date) {
        try {

            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            System.out.println("ThreadLocal中的员工信息: " + (employeeInfo != null ? employeeInfo.toString() : "null"));

            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            System.out.println("当前登录员工ID: " + employeeId);

            // 获取该员工的业绩记录（分页查询）
            Page<Performance> performancePage = performanceService.getPerformancePage(
                    page, size, employeeId, null, null, date);

            // 构建返回结果，与前端分页格式一致
            Map<String, Object> result = new HashMap<>();
            result.put("records", performancePage.getResult()); // 当前页数据
            result.put("total", performancePage.getTotal()); // 总记录数

            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取业绩记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工特定月份的业绩详情
     *
     * @param date 年月（格式：YYYY-MM）
     * @return 业绩详情
     */
    @GetMapping("/employee/detail/{date}")
    public Result<Performance> getCurrentEmployeePerformanceDetail(@PathVariable String date) {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 查询该员工特定月份的业绩记录
            Page<Performance> performancePage = performanceService.getPerformancePage(
                    1, 1, employeeId, null, null, date);

            if (performancePage.getResult().isEmpty()) {
                return Result.error("未找到该月份的业绩记录");
            }

            Performance performance = performancePage.getResult().get(0);
            return Result.success(performance);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取业绩详情失败：" + e.getMessage());
        }
    }

    /**
     * 添加业绩记录
     *
     * @param performance 业绩记录
     * @return 操作结果
     */
    @PostMapping
    public Result<String> addPerformance(@RequestBody Performance performance) {
        try {
            boolean success = performanceService.addPerformance(performance);
            if (!success) {
                return Result.error("添加失败，可能存在重复记录");
            }
            return Result.success("添加成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("添加业绩记录失败：" + e.getMessage());
        }
    }

    /**
     * 更新业绩记录
     *
     * @param performance 业绩记录
     * @return 操作结果
     */
    @PutMapping
    public Result<String> updatePerformance(@RequestBody Performance performance) {
        try {
            if (performance.getId() == null) {
                return Result.error("ID不能为空");
            }

            boolean success = performanceService.updatePerformance(performance);
            if (!success) {
                return Result.error("更新失败，请检查数据");
            }
            return Result.success("更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("更新业绩记录失败：" + e.getMessage());
        }
    }

    /**
     * 删除业绩记录
     *
     * @param id 业绩记录ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<String> deletePerformance(@PathVariable Integer id) {
        try {
            boolean success = performanceService.deletePerformance(id);
            if (!success) {
                return Result.error("删除失败");
            }
            return Result.success("删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("删除业绩记录失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除业绩记录
     *
     * @param ids 业绩记录ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public Result<String> batchDeletePerformance(@RequestBody List<Integer> ids) {
        try {
            boolean success = performanceService.batchDeletePerformance(ids);
            if (!success) {
                return Result.error("批量删除失败");
            }
            return Result.success("批量删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("批量删除业绩记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取多个部门的业绩列表
     * <p>
     * 获取多个指定部门的业绩数据列表合集。
     * 支持分页和按日期范围筛选。
     * </p>
     *
     * @param requestBody 包含以下字段的请求体:
     *                    - departmentIds: 部门ID数组
     *                    - page: 页码，默认为1
     *                    - size: 每页大小，默认为10
     *                    - startDate: 开始年月，格式YYYY-MM，可选
     *                    - endDate: 结束年月，格式YYYY-MM，可选
     *                    - employeeName: 员工姓名，用于模糊查询
     * @return 业绩数据列表
     */
    @PostMapping("/departments/list")
    public Result<Map<String, Object>> getMultiDepartmentPerformances(
            @RequestBody Map<String, Object> requestBody) {
        try {
            // 从请求体中获取参数
            List<Integer> departmentIds = (List<Integer>) requestBody.get("departmentIds");
            Integer page = requestBody.get("page") != null ? Integer.valueOf(requestBody.get("page").toString()) : 1;
            Integer size = requestBody.get("size") != null ? Integer.valueOf(requestBody.get("size").toString()) : 10;
            String startDate = (String) requestBody.get("startDate");
            String endDate = (String) requestBody.get("endDate");
            String employeeName = (String) requestBody.get("employeeName");

            if (departmentIds == null || departmentIds.isEmpty()) {
                return Result.error("部门ID列表不能为空");
            }

            // 调用Service层方法获取多个部门业绩数据
            Map<String, Object> performanceData = performanceService.getMultiDepartmentPerformances(departmentIds, page, size, startDate, endDate, employeeName);

            return Result.success(performanceData);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取多部门业绩列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取部门业绩统计数据
     * @param requestBody 包含部门ID列表和日期范围的请求体
     * @return 部门业绩统计数据
     */
    @PostMapping("/departments/stats")
    public Result<List<DepartmentPerformanceStatsDTO>> getDepartmentPerformanceStats(
            @RequestBody Map<String, Object> requestBody) {
        try {
            List<Integer> departmentIds = (List<Integer>) requestBody.get("departmentIds");
            String startDate = (String) requestBody.get("startDate");
            String endDate = (String) requestBody.get("endDate");
            
            if (departmentIds == null || departmentIds.isEmpty()) {
                return Result.error("部门ID列表不能为空");
            }
            
            List<DepartmentPerformanceStatsDTO> statsData = 
                    performanceService.getPerformanceStatsByDepartments(departmentIds, startDate, endDate);
            
            return Result.success(statsData);
        } catch (Exception e) {
            log.error("获取部门业绩统计数据失败", e);
            return Result.error("获取部门业绩统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 导入业绩Excel文件
     * @param file 上传的Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    public Result<ExcelUtil.ImportResult<PerformanceImportDTO>> importPerformances(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("上传的文件不能为空");
        }

        // 可选：服务器端文件类型和大小校验 (虽然前端也做了)
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || 
            !(originalFilename.toLowerCase().endsWith(".xls") || originalFilename.toLowerCase().endsWith(".xlsx"))) {
            return Result.error("文件类型不支持，请上传 .xls 或 .xlsx 文件");
        }
        // 文件大小校验可以在service层或者全局配置中处理，这里仅作示例
        // long maxSize = 500 * 1024 * 1024; // 500MB
        // if (file.getSize() > maxSize) {
        //     return Result.error("文件大小超过限制 (500MB)");
        // }

        try {
            ExcelUtil.ImportResult<PerformanceImportDTO> importResult = performanceService.importPerformancesFromExcel(file);
            
            if (importResult.getFailureCount() > 0 || !importResult.getGeneralErrors().isEmpty()) {
                // 如果有失败的行或通用错误，返回成功但数据中包含详细错误信息
                String message = String.format("文件导入处理完成。共解析 %d 行，成功 %d 行，失败 %d 行。",
                                             importResult.getProcessedRows(),
                                             importResult.getSuccessCount(),
                                             importResult.getFailureCount());
                if (!importResult.getGeneralErrors().isEmpty()) {
                    message += " 通用错误: " + String.join("; ", importResult.getGeneralErrors());
                }
                //  返回200，但message提示有部分失败，前端可以根据data中的failureCount和failedRows展示详情
                return Result.success(message, importResult); 
            }

            String successMessage = String.format("文件导入成功。共解析 %d 行，成功导入 %d 行。",
                                                importResult.getProcessedRows(),
                                                importResult.getSuccessCount());
            return Result.success(successMessage, importResult);

        } catch (BusinessException be) {
            log.error("导入业绩业务异常: {}", be.getMessage());
            return Result.error(be.getCode() != null ? be.getCode() : 500, be.getMessage());
        } catch (Exception e) {
            log.error("导入业绩文件发生意外错误", e);
            return Result.error("导入失败，发生意外错误：" + e.getMessage());
        }
    }
}

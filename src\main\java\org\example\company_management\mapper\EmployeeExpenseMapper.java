package org.example.company_management.mapper;

import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.EmployeeExpense;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface EmployeeExpenseMapper {
    /**
     * 新增员工费用记录
     *
     * @param employeeExpense 员工费用实体
     */
    void insert(EmployeeExpense employeeExpense);

    /**
     * 分页查询员工费用记录
     *
     * @param params 查询条件 (Map)
     * @return 分页结果
     */
    Page<EmployeeExpense> selectPage(Map<String, Object> params);

    /**
     * 根据ID查询员工费用记录
     *
     * @param id 费用记录ID
     * @return 员工费用实体
     */
    EmployeeExpense selectById(Long id);

    /**
     * 更新员工费用记录
     *
     * @param employeeExpense 员工费用实体
     * @return 影响行数
     */
    int update(EmployeeExpense employeeExpense);

    /**
     * 根据ID删除员工费用记录
     *
     * @param id 费用记录ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据ID列表批量删除员工费用记录
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(List<Long> ids);

    /**
     * 根据员工ID、费用日期和项目名称查询已存在的费用记录数量
     *
     * @param employeeId 员工ID
     * @param expenseDate 费用日期 (YYYY-MM-DD, 通常为月份第一天)
     * @param itemName 项目名称
     * @return 存在的记录数量
     */
    int countByEmployeeIdAndExpenseDateAndItemName(@Param("employeeId") Long employeeId, 
                                                   @Param("expenseDate") LocalDate expenseDate, 
                                                   @Param("itemName") String itemName);

    /**
     * 分页查询员工费用记录 (用户视图，支持多部门ID)
     *
     * @param params 查询条件 (Map, 包含 departmentIds, employeeName, startDate, endDate, itemName)
     * @return 分页结果
     */
    Page<EmployeeExpense> selectPageForUserView(Map<String, Object> params);
} 
import request from '@/utils/request'

const api_name = '/employee-expense'

/**
 * 分页查询员工费用列表
 * @param params 查询参数, 例如: { pageNum: 1, pageSize: 10, employeeId: 1, employeeName: '张三', startDate: '2023-01-01', endDate: '2023-01-31', itemName: '交通费' }
 * @returns Promise
 */
export function getEmployeeExpensePage(params) {
    return request({
        url: `${api_name}/page`,
        method: 'get',
        params
    })
}

/**
 * 新增员工费用 (单条或根据DTO内容批量)
 * @param data 员工费用数据
 * @returns Promise
 */
export function addEmployeeExpense(data) {
    return request({
        url: api_name,
        method: 'post',
        data
    })
}

/**
 * 根据ID获取员工费用详情
 * @param id 员工费用ID
 * @returns Promise
 */
export function getEmployeeExpenseById(id) {
    return request({
        url: `${api_name}/${id}`,
        method: 'get'
    })
}

/**
 * 修改员工费用
 * @param data 员工费用数据
 * @returns Promise
 */
export function updateEmployeeExpense(data) {
    return request({
        url: api_name,
        method: 'put',
        data
    })
}

/**
 * 根据ID删除员工费用
 * @param id 员工费用ID
 * @returns Promise
 */
export function deleteEmployeeExpense(id) {
    return request({
        url: `${api_name}/${id}`,
        method: 'delete'
    })
}

/**
 * 批量删除员工费用
 * @param ids ID列表
 * @returns Promise
 */
export function batchDeleteEmployeeExpense(ids) {
    return request({
        url: `${api_name}/batch-delete`,
        method: 'post',
        data: ids
    })
}

/**
 * 批量延用员工费用 (基于选择的现有费用复制到新的月份)
 * @param data 请求体, e.g., { items: [ { employeeId, expenseDate, itemName, amount, remark }, ... ] }
 * @returns Promise
 */
export function extendBatchEmployeeExpense(data) {
    return request({
        url: `${api_name}/extend-batch`,
        method: 'post',
        data
    });
} 
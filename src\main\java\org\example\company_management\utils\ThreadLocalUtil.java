package org.example.company_management.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * ThreadLocal工具类
 * 用于在线程中存储数据，比如用户信息、token等
 */
public class ThreadLocalUtil {
    /**
     * 创建ThreadLocal对象
     */
    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL = ThreadLocal.withInitial(HashMap::new);
    
    /**
     * 设置值
     *
     * @param key 键
     * @param value 值
     */
    public static void set(String key, Object value) {
        Map<String, Object> map = THREAD_LOCAL.get();
        map.put(key, value);
    }
    
    /**
     * 获取值
     *
     * @param key 键
     * @param <T> 值类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        Map<String, Object> map = THREAD_LOCAL.get();
        return (T) map.get(key);
    }
    
    /**
     * 获取当前线程中的所有值
     *
     * @return 当前线程中的Map对象
     */
    public static Map<String, Object> getAll() {
        return THREAD_LOCAL.get();
    }
    
    /**
     * 移除某个值
     *
     * @param key 键
     */
    public static void remove(String key) {
        Map<String, Object> map = THREAD_LOCAL.get();
        map.remove(key);
    }
    
    /**
     * 清除ThreadLocal中的所有值
     * 防止内存泄漏，在请求结束时必须调用
     */
    public static void clear() {
        THREAD_LOCAL.remove();
    }
} 
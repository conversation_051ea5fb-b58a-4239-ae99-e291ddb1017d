import request from './request'

/**
 * 分页查询部门开销 (用户视图)
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码 (将被转换为 page)
 * @param {number} params.pageSize 每页大小 (将被转换为 size)
 * @param {Array<number>} params.departmentIds 部门ID列表
 * @param {string} [params.itemName] 项目名称 (可选)
 * @param {string} [params.month] 年月 (可选, YYYY-MM格式, 将被转换为 date)
 * @returns {Promise<any>}
 */
export function fetchDepartmentExpenses(params) {
  return request({
    url: '/department-expense/user-view/page',
    method: 'post',
    data: {
      page: params.pageNum,
      size: params.pageSize,
      departmentIds: params.departmentIds,
      itemName: params.itemName,
      date: params.month
    }
  });
}

// // 保留旧的 getDepartmentExpenseById 函数，如果只读页面仍可能需要它查看单条详情
// export function getDepartmentExpenseById(id) {
//   return request({
//     url: `/department-expense/${id}`,
//     method: 'get'
//   });
// }

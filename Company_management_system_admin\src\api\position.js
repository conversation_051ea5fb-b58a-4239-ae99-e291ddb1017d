import request from '@/utils/request'

/**
 * 获取所有职位
 * @returns {Promise<any>}
 */
export function getPositionList() {
	return request({
		url: '/position/list',
		method: 'get',
	})
}

/**
 * 分页查询职位
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.positionName] 职位名称(可选)
 * @param {number} [params.departmentId] 部门ID(可选)
 * @returns {Promise<any>}
 */
export function getPositionPage(params) {
	return request({
		url: '/position/page',
		method: 'get',
		params,
	})
}

/**
 * 根据ID查询职位
 * @param {number} id 职位ID
 * @returns {Promise<any>}
 */
export function getPositionById(id) {
	return request({
		url: `/position/${id}`,
		method: 'get',
	})
}

/**
 * 根据部门ID查询职位
 * @param {number} departmentId 部门ID
 * @returns {Promise<any>}
 */
export function getPositionsByDepartmentId(departmentId) {
	return request({
		url: `/position/department/${departmentId}`,
		method: 'get',
	})
}

/**
 * 新增职位
 * @param {Object} data 职位信息
 * @returns {Promise<any>}
 */
export function addPosition(data) {
	return request({
		url: '/position/add',
		method: 'post',
		data,
	})
}

/**
 * 更新职位
 * @param {Object} data 职位信息
 * @returns {Promise<any>}
 */
export function updatePosition(data) {
	return request({
		url: '/position/update',
		method: 'put',
		data,
	})
}

/**
 * 删除职位
 * @param {number} id 职位ID
 * @returns {Promise<any>}
 */
export function deletePosition(id) {
	return request({
		url: `/position/${id}`,
		method: 'delete',
	})
}

/**
 * 更新职位状态
 * @param {number} id 职位ID
 * @param {string} status 状态
 * @returns {Promise<any>}
 */
export function updatePositionStatus(id, status) {
	return request({
		url: '/position/status',
		method: 'put',
		params: { id, status },
	})
}

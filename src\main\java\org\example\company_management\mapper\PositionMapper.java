package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Position;
import org.example.company_management.entity.PositionDepartment;
import java.util.List;
import java.util.Map;

/**
 * 职位Mapper接口
 */
@Mapper
public interface PositionMapper {
    /**
     * 查询所有职位
     * @return 职位列表
     */
    List<Position> selectAll();
    
    /**
     * 根据ID查询职位
     * @param positionId 职位ID
     * @return 职位信息
     */
    Position selectById(Integer positionId);
    
    /**
     * 分页查询职位
     * @param params 查询参数，包含offset、limit、positionName等
     * @return 职位列表
     */
    List<Position> selectByPage(Map<String, Object> params);
    
    /**
     * 查询符合条件的职位总数
     * @param params 查询参数
     * @return 职位总数
     */
    int countPositions(Map<String, Object> params);
    
    /**
     * 根据部门ID查询职位
     * @param departmentId 部门ID
     * @return 职位列表
     */
    List<Position> selectByDepartmentId(Integer departmentId);
    
    /**
     * 新增职位
     * @param position 职位信息
     * @return 影响行数
     */
    int insert(Position position);
    
    /**
     * 修改职位
     * @param position 职位信息
     * @return 影响行数
     */
    int update(Position position);
    
    /**
     * 删除职位
     * @param positionId 职位ID
     * @return 影响行数
     */
    int deleteById(Integer positionId);
    
    /**
     * 批量更新职位所属部门
     * @param oldDepartmentId 原部门ID
     * @param newDepartmentId 新部门ID
     * @return 影响行数
     */
    int updatePositionDepartment(Integer oldDepartmentId, Integer newDepartmentId);
    
    /**
     * 批量插入职位-部门关联记录
     * @param positionDepartments 职位-部门关联记录列表
     * @return 影响行数
     */
    int batchInsertPositionDepartment(@Param("list") List<PositionDepartment> positionDepartments);
    
    /**
     * 根据职位ID删除所有职位-部门关联记录
     * @param positionId 职位ID
     * @return 影响行数
     */
    int deletePositionDepartmentByPositionId(Integer positionId);
    
    /**
     * 根据职位ID查询关联的部门ID列表
     * @param positionId 职位ID
     * @return 部门ID列表
     */
    List<Integer> selectDepartmentIdsByPositionId(Integer positionId);
    
    /**
     * 根据职位ID查询关联的部门名称列表
     * @param positionId 职位ID
     * @return 部门名称列表
     */
    List<String> selectDepartmentNamesByPositionId(Integer positionId);
} 
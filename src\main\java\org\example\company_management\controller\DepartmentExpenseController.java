package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.DepartmentExpenseDTO;
import org.example.company_management.service.DepartmentExpenseService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.example.company_management.dto.DepartmentBatchExtendExpenseRequestDTO;
import org.example.company_management.dto.DepartmentExtendBatchResultDTO;

/**
 * 部门日常开销管理 Controller
 */
@RestController
@RequestMapping("/department-expense")
@Slf4j
public class DepartmentExpenseController {

    @Autowired
    private DepartmentExpenseService departmentExpenseService;

    /**
     * 批量新增部门开销
     *
     * @param departmentExpenseDTO 批量部门开销数据
     * @return Result 通用返回结果
     */
    @PostMapping("/batch")
    public Result addBatchDepartmentExpenses(@Validated(ValidationGroups.CreateBatch.class) @RequestBody DepartmentExpenseDTO departmentExpenseDTO) {
        log.info("批量新增部门开销请求: {}", departmentExpenseDTO);
        departmentExpenseService.addBatch(departmentExpenseDTO);
        return Result.success();
    }

    /**
     * 分页查询部门开销
     *
     * @param pageNum      分页页码
     * @param pageSize     每页数量
     * @param departmentId 部门ID (可选)
     * @param startDate    开销日期开始 (可选, YYYY-MM-DD)
     * @param endDate      开销日期结束 (可选, YYYY-MM-DD)
     * @param description  查询描述 (用于模糊匹配项目名称, 可选)
     * @return Result<PageResult> 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult> getDepartmentExpensePage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String description) {

        log.info("分页查询部门开销, pageNum: {}, pageSize: {}, departmentId: {}, startDate: {}, endDate: {}, description: {}",
                pageNum, pageSize, departmentId, startDate, endDate, description);

        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        if (departmentId != null) {
            params.put("departmentId", departmentId);
        }
        if (startDate != null) {
            params.put("startDate", startDate);
        }
        if (endDate != null) {
            params.put("endDate", endDate);
        }
        if (description != null && !description.isEmpty()) {
            params.put("description", description);
        }

        PageResult pageResult = departmentExpenseService.getPage(params);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询部门开销
     *
     * @param id 部门开销ID
     * @return Result<DepartmentExpenseDTO> 部门开销数据
     */
    @GetMapping("/{id}")
    public Result<DepartmentExpenseDTO> getDepartmentExpenseById(@PathVariable Long id) {
        log.info("根据ID查询部门开销, ID: {}", id);
        DepartmentExpenseDTO departmentExpenseDTO = departmentExpenseService.getById(id);
        return Result.success(departmentExpenseDTO);
    }

    /**
     * 修改部门开销 (单条)
     *
     * @param departmentExpenseDTO 部门开销数据
     * @return Result 通用返回结果
     */
    @PutMapping
    public Result updateDepartmentExpense(@Validated(ValidationGroups.Update.class) @RequestBody DepartmentExpenseDTO departmentExpenseDTO) {
        log.info("修改部门开销 (单条): {}", departmentExpenseDTO);
        departmentExpenseService.update(departmentExpenseDTO);
        return Result.success();
    }

    /**
     * 根据ID删除部门开销
     *
     * @param id 部门开销ID
     * @return Result 通用返回结果
     */
    @DeleteMapping("/{id}")
    public Result deleteDepartmentExpenseById(@PathVariable Long id) {
        log.info("根据ID删除部门开销, ID: {}", id);
        departmentExpenseService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除部门开销
     *
     * @param ids ID列表
     * @return Result 通用返回结果
     */
    @PostMapping("/batch-delete")
    public Result batchDeleteDepartmentExpense(@RequestBody List<Long> ids) {
        log.info("批量删除部门开销, IDs: {}", ids);
        departmentExpenseService.batchDeleteByIds(ids);
        return Result.success();
    }

    @PostMapping("/extend-batch")
    public Result<DepartmentExtendBatchResultDTO> extendBatchDepartmentExpenses(
            @Validated @RequestBody DepartmentBatchExtendExpenseRequestDTO requestDTO) {
        log.info("批量延用部门开销接口请求，共 {} 项", requestDTO.getItems() != null ? requestDTO.getItems().size() : 0);
        DepartmentExtendBatchResultDTO resultDetails = departmentExpenseService.extendBatch(requestDTO);
        return Result.success(resultDetails);
    }

    /**
     * 分页查询部门开销 (用户视图)
     *
     * @param requestBody 包含查询参数的请求体
     * @return Result<PageResult> 分页结果
     */
    @PostMapping("/user-view/page")
    public Result<PageResult> getUserViewDepartmentExpensePage(
            @RequestBody Map<String, Object> requestBody) {

        log.info("用户视图分页查询部门开销, 请求参数: {}", requestBody);

        Map<String, Object> params = new HashMap<>();
        
        // 将前端参数映射到后端参数
        params.put("pageNum", requestBody.getOrDefault("page", 1));
        params.put("pageSize", requestBody.getOrDefault("size", 10));
        
        if (requestBody.containsKey("departmentIds") && requestBody.get("departmentIds") != null) {
            params.put("departmentIds", requestBody.get("departmentIds"));
        }
        
        if (requestBody.containsKey("itemName") && requestBody.get("itemName") != null) {
            String itemName = requestBody.get("itemName").toString();
            if (!itemName.isEmpty()) {
                params.put("itemName", itemName);
            }
        }
        
        if (requestBody.containsKey("date") && requestBody.get("date") != null) {
            String month = requestBody.get("date").toString();
            if (!month.isEmpty()) {
                params.put("month", month);
            }
        }

        PageResult pageResult = departmentExpenseService.getPageForUserView(params);
        return Result.success(pageResult);
    }
} 
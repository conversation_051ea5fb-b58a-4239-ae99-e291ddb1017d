package org.example.company_management.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ExcelUtil {

    /**
     * 读取Excel文件内容
     *
     * @param inputStream  Excel文件输入流
     * @param head         Excel表头对应的Java类 (DTO)，字段需用 @ExcelProperty 注解
     * @param rowProcessor 处理每一行解析成功的数据的逻辑
     * @param <T>          Excel行数据映射的Java对象类型
     * @return 导入结果，包含成功、失败条数及详细信息
     */
    public static <T> ImportResult<T> readExcel(InputStream inputStream, Class<T> head, RowProcessor<T> rowProcessor) {
        ImportResult<T> importResult = new ImportResult<>();
        GenericExcelListener<T> listener = new GenericExcelListener<>(rowProcessor, importResult);

        try {
            EasyExcel.read(inputStream, head, listener)
                    .sheet() // 读取第一个sheet
                    .doRead();
        } catch (Exception e) {
            log.error("ExcelUtil: 读取Excel文件失败", e);
            importResult.getGeneralErrors().add("读取Excel文件失败：" + e.getMessage());
        }
        return importResult;
    }

    /**
     * 函数式接口，用于处理单行数据
     *
     * @param <T> 行数据类型
     */
    @FunctionalInterface
    public interface RowProcessor<T> {
        /**
         * 处理单行数据
         *
         * @param rowIndex 当前行号 (0-based in listener, 1-based for user feedback)
         * @param data     解析到的行数据对象
         * @return 处理结果，例如一个包含错误信息的字符串，如果处理成功则返回null或空字符串
         */
        String process(int rowIndex, T data);
    }

    /**
     * 通用Excel导入结果封装类
     *
     * @param <T> 行数据类型
     */
    @Data
    @NoArgsConstructor
    public static class ImportResult<T> {
        private int totalRowsInSheet; // Excel中实际的总行数（由监听器估算）
        private int processedRows;    // 实际处理的行数
        private int successCount;
        private int failureCount;
        private List<FailedRowInfo<T>> failedRows = new ArrayList<>();
        private List<String> generalErrors = new ArrayList<>();

        public ImportResult(int totalRowsInSheet, int processedRows, int successCount, int failureCount, List<FailedRowInfo<T>> failedRows, List<String> generalErrors) {
            this.totalRowsInSheet = totalRowsInSheet;
            this.processedRows = processedRows;
            this.successCount = successCount;
            this.failureCount = failureCount;
            if (failedRows != null) {
                this.failedRows = failedRows;
            }
            if (generalErrors != null) {
                this.generalErrors = generalErrors;
            }
        }

        public void addGeneralError(String s) {

        }
    }

    /**
     * 导入失败的行信息
     *
     * @param <T> 行数据类型
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FailedRowInfo<T> {
        private int rowIndex; // Excel中的原始行号 (1-based for user feedback)
        private T rowData;    // 可选，原始行数据DTO
        private String errorMessage;
    }

    /**
     * 通用EasyExcel监听器
     *
     * @param <T>
     */
    private static class GenericExcelListener<T> extends AnalysisEventListener<T> {
        private final RowProcessor<T> rowProcessor;
        private final ImportResult<T> importResult;
        private int currentRow = 0; // EasyExcel的rowIndex是0-based

        public GenericExcelListener(RowProcessor<T> rowProcessor, ImportResult<T> importResult) {
            this.rowProcessor = rowProcessor;
            this.importResult = importResult;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            // 获取预估总行数 (不一定非常精确，特别是对于空行较多的情况)
            // Integer approximateTotalRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
            // importResult.setTotalRowsInSheet(approximateTotalRowNumber != null ? approximateTotalRowNumber : 0);
            // log.info("解析到表头: {}", headMap);
            // 如果需要更精确的总行数，可能需要在doAfterAllAnalysed中设置基于processedRows，或者接受EasyExcel的估算值。
            // 目前totalRowsInSheet的准确性依赖EasyExcel的实现，processedRows更可靠地反映实际处理的非空数据行。
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            currentRow = context.readRowHolder().getRowIndex(); // 0-based
            importResult.setProcessedRows(importResult.getProcessedRows() + 1);

            String errorMsg = null;
            try {
                errorMsg = rowProcessor.process(currentRow + 1, data); // 给业务逻辑传递1-based行号
            } catch (Exception e) {
                log.error("ExcelUtil: 处理第 {} 行数据时发生业务异常: {}", currentRow + 1, data, e);
                errorMsg = "处理数据时发生内部错误: " + e.getMessage();
            }

            if (errorMsg == null || errorMsg.isEmpty()) {
                importResult.setSuccessCount(importResult.getSuccessCount() + 1);
            } else {
                importResult.setFailureCount(importResult.getFailureCount() + 1);
                importResult.getFailedRows().add(new FailedRowInfo<>(currentRow + 1, data, errorMsg));
            }
        }

        @Override
        public void onException(Exception exception, AnalysisContext context) throws Exception {
            log.error("ExcelUtil: 解析Excel数据时发生异常，行号 {}: ", context.readRowHolder().getRowIndex() + 1, exception);
            // ExcelDataConvertException 是EasyExcel在数据转换时的特定异常，可以获取更详细的信息
            if (exception instanceof ExcelDataConvertException) {
                ExcelDataConvertException edce = (ExcelDataConvertException) exception;
                String error = String.format("第%d行，第%d列数据格式错误: %s",
                        edce.getRowIndex() + 1, edce.getColumnIndex() + 1, edce.getMessage());
                // 将此错误添加到特定行的失败信息中，或者作为通用错误
                // 为了简化，我们暂且添加到generalErrors，但更好的做法是关联到具体行
                importResult.getGeneralErrors().add(error);
            } else {
                importResult.getGeneralErrors().add(String.format("解析第%d行时发生错误: %s", context.readRowHolder().getRowIndex() + 1, exception.getMessage()));
            }
            // 如果希望异常中断整个读取过程，则向上抛出
            // super.onException(exception, context);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 所有数据解析完成后调用
            // 如果在invokeHeadMap中获取的totalRowsInSheet不准确，可以在这里基于processedRows修正
            // 但processedRows只代表非空且被处理的行。
            // EasyExcel 的 approximateTotalRowNumber 是对Sheet中总物理行数的估算。
            // 如果需要更准确的 sheet 内非空行数，可能需要在业务层面进一步判断。
            if (context.readSheetHolder().getApproximateTotalRowNumber() != null) {
                importResult.setTotalRowsInSheet(context.readSheetHolder().getApproximateTotalRowNumber());
            } else {
                // Fallback if approximateTotalRowNumber is null
                importResult.setTotalRowsInSheet(importResult.getProcessedRows());
            }
            log.info("ExcelUtil: Excel文件解析完成. 共处理 {} 行，成功 {} 行，失败 {} 行.",
                    importResult.getProcessedRows(), importResult.getSuccessCount(), importResult.getFailureCount());
        }
    }
} 
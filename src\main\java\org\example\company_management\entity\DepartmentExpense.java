package org.example.company_management.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 部门日常开销实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentExpense {
    /**
     * 开销记录ID (主键,自增)
     */
    private Long id;

    /**
     * 部门ID (外键,关联 department 表)
     */
    private Long departmentId;

    /**
     * 部门名称 (通过JOIN查询得到，非表字段)
     */
    private String departmentName;

    /**
     * 开销日期 (YYYY-MM-DD)
     */
    private LocalDate expenseDate;

    /**
     * 开销金额
     */
    private BigDecimal amount;

    /**
     * 开销项目名称 (对应数据库 item_name)
     */
    private String itemName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;
} 
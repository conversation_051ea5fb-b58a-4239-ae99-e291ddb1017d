import request from '@/utils/request'

// 登录
export function login(data) {
	return request({
		url: '/auth/login',
		method: 'post',
		data: {
			phone: data.phone,
			password: data.password,
		},
	})
}

// 登出
export function logout() {
	return request({
		url: '/auth/logout',
		method: 'post',
	})
}

// 获取用户信息
export function getUserInfo() {
	return request({
		url: '/auth/info',
		method: 'get',
	})
}

// 更新个人信息
export function updateProfile(data) {
	return request({
		url: '/auth/profile',
		method: 'put',
		data: {
			name: data.name,
			phone: data.phone,
			email: data.email,
			password: data.password,
			oldPassword: data.oldPassword,
		},
	})
}

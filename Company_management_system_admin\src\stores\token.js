import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
	state: () => ({
		token: localStorage.getItem('token') || null,
		user: JSON.parse(localStorage.getItem('user')) || null,
		loading: false,
		error: null,
		accessibleMenuItems: JSON.parse(localStorage.getItem('accessibleMenuItems')) || [],
	}),

	getters: {
		isAuthenticated: (state) => !!state.token,
		getToken: (state) => state.token,
		getUser: (state) => state.user,
		isLoading: (state) => state.loading,
		getError: (state) => state.error,
	},

	actions: {
		setToken(token) {
			this.token = token
			localStorage.setItem('token', token)
		},

		setUser(userData) {
			this.user = userData
			localStorage.setItem('user', JSON.stringify(userData))

			if (userData && userData.accessibleMenuIdsJson) {
				try {
					const parsedMenuIds = JSON.parse(userData.accessibleMenuIdsJson)
					if (Array.isArray(parsedMenuIds)) {
						this.accessibleMenuItems = parsedMenuIds
					} else {
						console.error('accessibleMenuIdsJson from userData is not an array after parsing:', parsedMenuIds)
						this.accessibleMenuItems = []
					}
				} catch (e) {
					console.error('Failed to parse accessibleMenuIdsJson from userData:', e, userData.accessibleMenuIdsJson)
					this.accessibleMenuItems = []
				}
			} else {
				console.log('No accessibleMenuIdsJson in userData or userData is null, clearing accessibleMenuItems.')
				this.accessibleMenuItems = []
			}
			localStorage.setItem('accessibleMenuItems', JSON.stringify(this.accessibleMenuItems))
		},

		setLoading(status) {
			this.loading = status
		},

		setError(error) {
			this.error = error
		},

		async loginSuccess(loginData) {
			this.token = loginData.token
			this.setUser(loginData.user)
			localStorage.setItem('token', loginData.token)
		},

		async fetchUserInfoAndSet() {
			if (this.isAuthenticated && (!this.user || this.accessibleMenuItems.length === 0)) {
				try {
					const res = await getUserInfo()
					if (res.code === 200 && res.data) {
						this.setUser(res.data)
					} else {
						this.logout()
						throw new Error(res.message || '获取用户信息失败')
					}
				} catch (error) {
					this.logout()
					throw error
				}
			} else if (this.user && this.user.accessibleMenuIdsJson && this.accessibleMenuItems.length === 0) {
				this.setUser(this.user)
			}
		},

		logout() {
			this.token = null
			this.user = null
			this.accessibleMenuItems = []
			localStorage.removeItem('token')
			localStorage.removeItem('user')
			localStorage.removeItem('accessibleMenuItems')
		},
	},
})

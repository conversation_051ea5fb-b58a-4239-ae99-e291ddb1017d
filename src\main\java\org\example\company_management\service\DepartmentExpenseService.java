package org.example.company_management.service;

import org.example.company_management.dto.DepartmentExpenseDTO;
import org.example.company_management.utils.PageResult;
import org.example.company_management.dto.DepartmentBatchExtendExpenseRequestDTO;
import org.example.company_management.dto.DepartmentExtendBatchResultDTO;

import java.util.List;
import java.util.Map;

/**
 * 部门日常开销 Service 接口
 */
public interface DepartmentExpenseService {

    /**
     * 分页查询部门开销
     *
     * @param params 查询条件 (Map 包含 pageNum, pageSize, departmentId, startDate, endDate, description)
     * @return PageResult 分页结果
     */
    PageResult getPage(Map<String, Object> params);

    /**
     * 根据ID查询部门开销
     *
     * @param id 部门开销ID
     * @return DepartmentExpenseDTO 部门开销数据
     */
    DepartmentExpenseDTO getById(Long id);

    /**
     * 修改部门开销
     *
     * @param departmentExpenseDTO 部门开销数据
     */
    void update(DepartmentExpenseDTO departmentExpenseDTO);

    /**
     * 根据ID删除部门开销
     *
     * @param id 部门开销ID
     */
    void deleteById(Long id);

    /**
     * 批量删除部门开销
     *
     * @param ids ID列表
     */
    void batchDeleteByIds(List<Long> ids);

    /**
     * 批量新增部门开销
     * @param departmentExpenseDTO 包含批量部门开销数据的DTO
     */
    void addBatch(DepartmentExpenseDTO departmentExpenseDTO);

    DepartmentExtendBatchResultDTO extendBatch(DepartmentBatchExtendExpenseRequestDTO requestDTO);

    /**
     * 分页查询部门开销 (用户视图)
     *
     * @param params 查询条件 (Map 包含 pageNum, pageSize, departmentIds, itemName, month)
     * @return PageResult 分页结果
     */
    PageResult getPageForUserView(Map<String, Object> params);
} 
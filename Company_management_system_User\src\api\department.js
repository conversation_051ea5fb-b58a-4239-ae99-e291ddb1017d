import request from './request'

/**
 * 获取当前用户的部门信息
 * @returns {Promise}
 */
export function getCurrentDepartment() {
	return request({
		url: '/department/current',
		method: 'get',
	})
}

/**
 * 获取部门子部门列表
 * @param {Number} departmentId - 部门ID
 * @returns {Promise}
 */
export function getSubDepartments(departmentId) {
	return request({
		url: `/department/${departmentId}/sub`,
		method: 'get',
	})
}

/**
 * 根据部门ID获取员工列表
 * @param {Number} departmentId - 部门ID
 * @returns {Promise}
 */
export function getDepartmentEmployees(departmentId) {
	return request({
		url: `/employee/department/${departmentId}`,
		method: 'get',
	})
}

/**
 * 根据部门ID分页获取员工列表
 * @param {Number} departmentId - 部门ID 
 * @param {Object} params - 分页参数
 * @param {Number} params.pageNum - 页码
 * @param {Number} params.pageSize - 每页数量
 * @param {String} params.name - 员工姓名(可选)
 * @returns {Promise}
 */
export function getDepartmentEmployeesPage(departmentId, params) {
	return request({
		url: `/employee/department/${departmentId}/page`,
		method: 'get',
		params,
	})
}

/**
 * 获取部门详情
 * @param {Number} departmentId - 部门ID
 * @returns {Promise}
 */
export function getDepartmentById(departmentId) {
	return request({
		url: `/department/${departmentId}`,
		method: 'get',
	})
}

/**
 * 获取当前用户负责的部门树
 * @returns {Promise}
 */
export function getResponsibleDepartmentTree() {
	return request({
		url: '/department/responsible-tree',
		method: 'get'
	});
}
package org.example.company_management.service.impl;

import org.example.company_management.entity.Position;
import org.example.company_management.entity.PositionDepartment;
import org.example.company_management.mapper.EmployeeMapper;
import org.example.company_management.mapper.PositionMapper;
import org.example.company_management.service.PositionService;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职位服务实现类
 */
@Service
public class PositionServiceImpl implements PositionService {

    @Autowired
    private PositionMapper positionMapper;
    
    @Autowired
    private EmployeeMapper employeeMapper;

    @Override
    public List<Position> list() {
        List<Position> positions = positionMapper.selectAll();
        
        // 填充每个职位的部门列表
        for (Position position : positions) {
            if (position.getPositionId() != null) {
                // 获取部门名称列表
                List<String> departmentNames = positionMapper.selectDepartmentNamesByPositionId(position.getPositionId());
                position.setDepartmentNames(departmentNames);
                
                // 获取部门ID列表
                List<Integer> departmentIds = positionMapper.selectDepartmentIdsByPositionId(position.getPositionId());
                position.setDepartmentIds(departmentIds);
            }
        }
        
        return positions;
    }

    @Override
    public PageResult<Position> pageList(int pageNum, int pageSize, String positionName, Integer departmentId) {
        // 计算分页参数
        int offset = (pageNum - 1) * pageSize;
        
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("limit", pageSize);
        
        // 添加查询条件
        if (positionName != null && !positionName.trim().isEmpty()) {
            params.put("positionName", "%" + positionName + "%");
        }
        
        // 添加部门ID筛选条件
        if (departmentId != null) {
            params.put("departmentId", departmentId);
        }
        
        // 查询总数和分页数据
        int total = positionMapper.countPositions(params);
        List<Position> positions = positionMapper.selectByPage(params);
        
        // 填充每个职位的部门列表
        for (Position position : positions) {
            if (position.getPositionId() != null) {
                // 获取部门名称列表
                List<String> departmentNames = positionMapper.selectDepartmentNamesByPositionId(position.getPositionId());
                position.setDepartmentNames(departmentNames);
                
                // 获取部门ID列表
                List<Integer> departmentIds = positionMapper.selectDepartmentIdsByPositionId(position.getPositionId());
                position.setDepartmentIds(departmentIds);
            }
        }
        
        // 创建分页结果 - 使用带参数的构造函数
        return new PageResult<>(pageNum, pageSize, total, positions);
    }

    @Override
    public Position getById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        
        Position position = positionMapper.selectById(id);
        
        if (position != null) {
            // 获取部门名称列表
            List<String> departmentNames = positionMapper.selectDepartmentNamesByPositionId(id);
            position.setDepartmentNames(departmentNames);
            
            // 获取部门ID列表
            List<Integer> departmentIds = positionMapper.selectDepartmentIdsByPositionId(id);
            position.setDepartmentIds(departmentIds);
        }
        
        return position;
    }

    @Override
    public List<Position> getByDepartmentId(Integer departmentId) {
        if (departmentId == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }
        return positionMapper.selectByDepartmentId(departmentId);
    }

    @Override
    @Transactional
    public void add(Position position) {
        if (position == null) {
            throw new IllegalArgumentException("职位信息不能为空");
        }
        if (position.getPositionName() == null || position.getPositionName().trim().isEmpty()) {
            throw new IllegalArgumentException("职位名称不能为空");
        }
        
        // 设置创建和更新时间
        Date now = new Date();
        position.setCreateTime(now);
        position.setUpdateTime(now);
        
        // 设置默认状态
        if (position.getStatus() == null) {
            position.setStatus("Active");
        }
        
        // 如果前端传来多个部门ID但没有设置主要部门ID，则使用第一个作为主要部门ID
        if (position.getDepartmentId() == null && position.getDepartmentIds() != null && !position.getDepartmentIds().isEmpty()) {
            position.setDepartmentId(position.getDepartmentIds().get(0));
        }
        
        // 插入职位基本信息
        positionMapper.insert(position);
        
        // 处理职位-部门关联
        if (position.getDepartmentIds() != null && !position.getDepartmentIds().isEmpty()) {
            setPositionDepartments(position.getPositionId(), position.getDepartmentIds());
        } else if (position.getDepartmentId() != null) {
            // 如果没有提供departmentIds但有departmentId，则使用departmentId
            List<Integer> departmentIds = new ArrayList<>();
            departmentIds.add(position.getDepartmentId());
            setPositionDepartments(position.getPositionId(), departmentIds);
        }
    }

    @Override
    @Transactional
    public void update(Position position) {
        if (position == null) {
            throw new IllegalArgumentException("职位信息不能为空");
        }
        if (position.getPositionId() == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        if (position.getPositionName() == null || position.getPositionName().trim().isEmpty()) {
            throw new IllegalArgumentException("职位名称不能为空");
        }
        
        // 检查职位是否存在
        Position existingPosition = positionMapper.selectById(position.getPositionId());
        if (existingPosition == null) {
            throw new IllegalArgumentException("职位不存在");
        }
        
        // 更新修改时间
        position.setUpdateTime(new Date());
        
        // 保留创建时间
        position.setCreateTime(existingPosition.getCreateTime());
        
        // 如果前端传来多个部门ID但没有设置主要部门ID，则使用第一个作为主要部门ID
        if (position.getDepartmentId() == null && position.getDepartmentIds() != null && !position.getDepartmentIds().isEmpty()) {
            position.setDepartmentId(position.getDepartmentIds().get(0));
        }
        
        // 更新职位基本信息
        positionMapper.update(position);
        
        // 处理职位-部门关联
        if (position.getDepartmentIds() != null) {
            setPositionDepartments(position.getPositionId(), position.getDepartmentIds());
        } else if (position.getDepartmentId() != null) {
            // 如果没有提供departmentIds但有departmentId，则使用departmentId
            List<Integer> departmentIds = new ArrayList<>();
            departmentIds.add(position.getDepartmentId());
            setPositionDepartments(position.getPositionId(), departmentIds);
        }
    }

    @Override
    @Transactional
    public void delete(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        
        // 检查职位是否存在
        Position position = positionMapper.selectById(id);
        if (position == null) {
            throw new IllegalArgumentException("职位不存在");
        }
        
        // 检查职位下是否有员工
        int employeeCount = checkPositionHasEmployees(id);
        if (employeeCount > 0) {
            throw new IllegalArgumentException("该职位下有" + employeeCount + "名员工，请先调整这些员工的职位后再删除");
        }
        
        // 删除职位-部门关联
        positionMapper.deletePositionDepartmentByPositionId(id);
        
        // 删除职位
        positionMapper.deleteById(id);
    }
    
    @Override
    public int checkPositionHasEmployees(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        return employeeMapper.countByPosition(id);
    }

    @Override
    @Transactional
    public void updateStatus(Integer id, String status) {
        if (id == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("状态不能为空");
        }
        
        // 检查职位是否存在
        Position position = positionMapper.selectById(id);
        if (position == null) {
            throw new IllegalArgumentException("职位不存在");
        }
        
        // 设置新状态和更新时间
        position.setStatus(status);
        position.setUpdateTime(new Date());
        
        positionMapper.update(position);
    }

    @Override
    @Transactional
    public int updatePositionDepartment(Integer oldDepartmentId, Integer newDepartmentId) {
        if (oldDepartmentId == null) {
            throw new IllegalArgumentException("原部门ID不能为空");
        }
        
        if (newDepartmentId == null) {
            throw new IllegalArgumentException("新部门ID不能为空");
        }
        
        // 更新职位所属部门
        return positionMapper.updatePositionDepartment(oldDepartmentId, newDepartmentId);
    }
    
    @Override
    public List<Integer> getPositionDepartmentIds(Integer positionId) {
        if (positionId == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        return positionMapper.selectDepartmentIdsByPositionId(positionId);
    }

    @Override
    @Transactional
    public void setPositionDepartments(Integer positionId, List<Integer> departmentIds) {
        if (positionId == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        
        // 先删除已有的职位-部门关联
        positionMapper.deletePositionDepartmentByPositionId(positionId);
        
        // 如果部门ID列表为空，则直接返回
        if (departmentIds == null || departmentIds.isEmpty()) {
            return;
        }
        
        // 批量插入新的职位-部门关联
        List<PositionDepartment> positionDepartments = new ArrayList<>();
        Date now = new Date();
        
        for (Integer departmentId : departmentIds) {
            if (departmentId != null) {
                PositionDepartment pd = new PositionDepartment();
                pd.setPositionId(positionId);
                pd.setDepartmentId(departmentId);
                pd.setCreateTime(now);
                positionDepartments.add(pd);
            }
        }
        
        if (!positionDepartments.isEmpty()) {
            positionMapper.batchInsertPositionDepartment(positionDepartments);
        }
    }

    @Override
    public List<String> getPositionDepartmentNames(Integer positionId) {
        if (positionId == null) {
            throw new IllegalArgumentException("职位ID不能为空");
        }
        return positionMapper.selectDepartmentNamesByPositionId(positionId);
    }
}

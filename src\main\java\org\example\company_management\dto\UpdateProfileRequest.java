package org.example.company_management.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
/**
 * 更新个人信息请求DTO
 * <p>
 * 允许更新姓名、手机号、邮箱（可选）和密码。
 * </p>
 */
public class UpdateProfileRequest {

    /**
     * 姓名
     */
    @Size(min = 2, max = 50, message = "姓名长度必须在2-50个字符之间")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 邮箱 (可选)
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 新密码（可选）密码必须至少8位，包含大小写字母和数字
     */
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$", message = "密码必须至少8位，包含大小写字母和数字")
    private String password;

    /**
     * 旧密码（更改密码时必填）密码必须至少8位，包含大小写字母和数字
     */
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$", message = "密码必须至少8位，包含大小写字母和数字")
    private String oldPassword;


} 
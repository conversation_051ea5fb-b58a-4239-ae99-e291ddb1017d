package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Employee;
import java.util.List;
import java.util.Map;

/**
 * 员工Mapper接口
 */
@Mapper
public interface EmployeeMapper {
    /**
     * 根据手机号查询员工 (不包含密码)
     * @param phone 手机号
     * @return 员工信息
     */
    Employee selectByPhone(String phone);

    /**
     * 根据手机号查询员工（包含密码，仅用于认证）
     * @param phone 手机号
     * @return 员工信息
     */
    Employee selectByPhoneForAuth(String phone);
    
    /**
     * 根据邮箱查询员工 (主要用于邮箱唯一性校验)
     * @param email 邮箱
     * @return 员工信息 (可能只包含部分关键字段如id和email)
     */
    Employee selectByEmail(String email);
    
    /**
     * 根据ID查询员工
     * @param employeeId 员工ID
     * @return 员工信息
     */
    Employee selectById(Integer employeeId);
    
    /**
     * 查询所有员工
     * @return 员工列表
     */
    List<Employee> selectAll();
    
    /**
     * 分页查询员工
     * @param params 查询参数，包含offset, pageSize, name和departmentId
     * @return 员工列表
     */
    List<Employee> selectByPage(Map<String, Object> params);
    
    /**
     * 获取员工总数
     * @param params 查询参数，包含name和departmentId
     * @return 员工总数
     */
    int countTotal(Map<String, Object> params);
    
    /**
     * 统计指定职位的员工数量
     * @param positionId 职位ID
     * @return 该职位的员工数量
     */
    int countByPosition(Integer positionId);
    
    /**
     * 新增员工
     * @param employee 员工信息
     * @return 影响行数
     */
    int insert(Employee employee);
    
    /**
     * 修改员工
     * @param employee 员工信息
     * @return 影响行数
     */
    int update(Employee employee);
    
    /**
     * 删除员工
     * @param employeeId 员工ID
     * @return 影响行数
     */
    int deleteById(Integer employeeId);
    
    /**
     * 根据职位查询员工
     * @param positionId 职位ID
     * @return 员工列表
     */
    List<Employee> selectByPosition(Integer positionId);
    
    /**
     * 根据部门查询员工
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<Employee> selectByDepartment(Integer departmentId);
    
    /**
     * 批量更新员工部门
     * @param departmentId 原部门ID
     * @param newDepartmentId 新部门ID，如果为null则设置为未分配状态
     * @return 更新的员工数量
     */
    int updateEmployeeDepartment(@Param("departmentId") Integer departmentId, @Param("newDepartmentId") Integer newDepartmentId);
    
    /**
     * 根据姓名查询员工
     * @param name 员工姓名
     * @return 员工信息列表
     */
    List<Employee> selectByName(String name);
    
    /**
     * 根据ID查询员工（包含密码，仅用于认证）
     * @param employeeId 员工ID
     * @return 员工信息
     */
    Employee selectByIdForAuth(Integer employeeId);
    
    /**
     * 分页查询部门下的员工
     * @param params 查询参数，包含offset, pageSize, departmentId和name
     * @return 员工列表
     */
    List<Employee> selectByPageAndDepartment(Map<String, Object> params);
    
    /**
     * 获取部门下员工总数
     * @param params 查询参数，包含departmentId和name
     * @return 员工总数
     */
    int countTotalByDepartment(Map<String, Object> params);

    /**
     * 根据身份证号查询员工
     * @param idCard 身份证号
     * @return 员工信息，如果身份证号唯一，则返回单个员工；否则可能需要调整返回类型或处理逻辑
     */
    Employee selectByIdCard(@Param("idCard") String idCard);

    /**
     * 根据部门ID列表查询员工及其详细信息
     * @param departmentIds 部门ID列表
     * @return 员工列表
     */
    List<Employee> selectEmployeesWithDetailsByDepartmentIds(@Param("departmentIds") List<Integer> departmentIds);

    Integer countActiveEmployeesByDepartmentId(@Param("departmentId") Integer departmentId);

    /**
     * 根据员工ID查找员工及其职位名称。
     * @param employeeId 员工ID
     * @return 员工对象，包含职位名称，可能为空
     */
    Employee findByIdWithPositionName(@Param("employeeId") Integer employeeId);

    /**
     * 获取所有指定职位名称的员工ID列表 (用于后台任务批量生成通知)
     * @param positionName 职位名称
     * @return 员工ID列表
     */
    List<Integer> findEmployeeIdsByPositionName(@Param("positionName") String positionName);
} 
package org.example.company_management.service.impl;

import org.example.company_management.entity.Employee;
import org.example.company_management.mapper.EmployeeMapper;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.utils.Md5Util;
import org.example.company_management.utils.PageResult;
import org.example.company_management.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 员工服务实现类
 */
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Override
    public List<Employee> list() {
        return employeeMapper.selectAll();
    }

    @Override
    public PageResult<Employee> pageList(int pageNum, int pageSize, String name, Integer departmentId) {
        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 添加筛选条件
        if (name != null && !name.trim().isEmpty()) {
            params.put("name", name);
        }

        if (departmentId != null) {
            params.put("departmentId", departmentId);
        }

        // 查询总数
        int total = employeeMapper.countTotal(params);

        // 查询当前页数据
        List<Employee> pageEmployees = employeeMapper.selectByPage(params);

        // 创建分页结果
        return new PageResult<>(pageNum, pageSize, total, pageEmployees);
    }

    @Override
    public Employee getById(Integer id) {
        return employeeMapper.selectById(id);
    }

    @Override
    public List<Employee> getByDepartmentId(Integer departmentId) {
        return employeeMapper.selectByDepartment(departmentId);
    }

    @Override
    public void add(Employee employee) {
        // 检查手机号是否已存在
        if (StringUtils.hasText(employee.getPhone())) {
            Employee existingByPhone = employeeMapper.selectByPhone(employee.getPhone());
            if (existingByPhone != null) {
                throw new BusinessException("手机号 '" + employee.getPhone() + "' 已被其他员工使用");
            }
        } else {
            // 前端DTO已有@NotBlank校验，理论上这里不应该为空，但作为服务层可以再加一道防线
            throw new BusinessException("手机号不能为空");
        }

        // 如果邮箱是空字符串，则视为空（NULL），以避免数据库唯一约束问题
        if (employee.getEmail() != null && employee.getEmail().isEmpty()) {
            employee.setEmail(null);
        }

        // 检查邮箱是否已存在 (如果邮箱字段存在且需要唯一性检查)
        if (StringUtils.hasText(employee.getEmail())) {
            Employee existingByEmail = employeeMapper.selectByEmail(employee.getEmail());
            if (existingByEmail != null) {
                throw new BusinessException("邮箱 '" + employee.getEmail() + "' 已被其他员工使用");
            }
        }

        employee.setPassword(Md5Util.encrypt(employee.getPassword()));
        employee.setStatus("Active");
        employee.setCreateTime(new Date());
        employee.setUpdateTime(new Date());
        employeeMapper.insert(employee);
    }

    @Override
    public void update(Employee employee) {
        // 查询当前员工信息
        Employee currentEmployee = employeeMapper.selectByIdForAuth(employee.getEmployeeId());
        if (currentEmployee == null) {
            throw new BusinessException("员工不存在");
        }

        // 检查手机号是否更改以及是否已存在
        if (StringUtils.hasText(employee.getPhone()) && !employee.getPhone().equals(currentEmployee.getPhone())) {
            Employee existingByPhone = employeeMapper.selectByPhone(employee.getPhone());
            if (existingByPhone != null) { // 不需要检查 existingByPhone.getEmployeeId().equals(employee.getEmployeeId())
                throw new BusinessException("手机号 '" + employee.getPhone() + "' 已被其他员工使用");
            }
        } else if (!StringUtils.hasText(employee.getPhone())) {
            // 前端DTO已有@NotBlank校验
            throw new BusinessException("手机号不能为空");
        }

        // 检查邮箱是否更改以及是否已存在 (如果邮箱字段存在且需要唯一性检查)
        if (StringUtils.hasText(employee.getEmail()) && !employee.getEmail().equals(currentEmployee.getEmail())) {
            Employee existingByEmail = employeeMapper.selectByEmail(employee.getEmail());
            if (existingByEmail != null && !existingByEmail.getEmployeeId().equals(employee.getEmployeeId())) {
                throw new BusinessException("邮箱 '" + employee.getEmail() + "' 已被其他员工使用");
            }
        } else if (employee.getEmail() == null && currentEmployee.getEmail() != null) {
            // 如果传入的email是null (表示要清空邮箱)，而当前邮箱不是null，则允许 (取决于业务是否允许清空邮箱)
            // 如果业务不允许清空，则这里可能需要抛出异常或保留原邮箱
        } else if (!StringUtils.hasText(employee.getEmail()) && StringUtils.hasText(currentEmployee.getEmail())){
            // 如果传入的email是空字符串，而当前邮箱有值，也视为清空
            employee.setEmail(null); // 明确设置为null，以便MyBatis更新
        }

        //修改员工加密密码
        if (employee.getPassword() != null && !employee.getPassword().isEmpty()) {
            employee.setPassword(Md5Util.encrypt(employee.getPassword()));
        }

        // 如果员工不是 'Deleted' 状态，则根据 exitDate 调整 status
        // 并且只有在传入的 employee 对象中 status 为空时（意味着前端没有尝试直接修改 status），才根据 exitDate 调整
        // 如果 employee.getStatus() 有值，则可能是前端通过其他方式（非标准 status 更新接口）传递了状态，此时应谨慎处理
        // 但基于当前 DTO，status 不会由前端直接在 update 时传入，而是由 exitDate 决定或由 updateStatus 接口处理
        if (!"Deleted".equals(currentEmployee.getStatus())) {
            if (employee.getExitDate() != null) {
                employee.setStatus("Inactive");
            } else {
                employee.setStatus("Active"); // 如果没有离职日期，则为 Active
            }
        } else {
            // 如果是 'Deleted' 状态，常规更新不应改变 status，除非有特定逻辑允许恢复
            // 将 employee 对象中的 status 设置回 currentEmployee 的状态，以防止意外更改
            employee.setStatus(currentEmployee.getStatus());
        }

        // 设置更新时间
        employee.setUpdateTime(new Date());

        employeeMapper.update(employee);
    }

    @Override
    public void delete(Integer id) {
        // 在调用 deleteById 之前，可以先检查员工是否存在，以提供更友好的错误信息
        Employee employee = employeeMapper.selectByIdForAuth(id); // 检查是否存在，即使是Deleted状态
        if (employee == null) {
            throw new BusinessException("尝试删除的员工不存在。");
        }
        // 如果员工已经是 Deleted 状态，可以选择不执行任何操作或抛出特定异常
        if ("Deleted".equals(employee.getStatus())) {
            // ElMessage.warning("员工已经是删除状态"); // 如果在service层处理，可以考虑日志或特定返回
            return; // 或者抛出异常，取决于业务需求
        }
        employeeMapper.deleteById(id);
    }

    @Override
    public void updateStatus(Integer id, String newStatus) {
        Employee employee = employeeMapper.selectByIdForAuth(id); // 使用 ForAuth 获取员工，即使是 Deleted
        if (employee == null) {
            throw new BusinessException("员工不存在");
        }

        // 防止通过此接口操作已删除的员工或将员工状态设置为Deleted
        if ("Deleted".equals(employee.getStatus())) {
            throw new BusinessException("不能修改已删除员工的状态。");
        }
        if ("Deleted".equals(newStatus)) {
            throw new BusinessException("请使用删除接口来标记员工为已删除。");
        }

        employee.setStatus(newStatus);
        if ("Active".equals(newStatus)) {
            employee.setExitDate(null);
        } else if ("Inactive".equals(newStatus)) {
            if (employee.getExitDate() == null) { // 仅当之前没有离职日期时，才设置为当前时间
                employee.setExitDate(new Date());
            }
        }
        employee.setUpdateTime(new Date());
        // 使用 employeeMapper.update 来更新包括 exitDate 在内的所有相关字段
        employeeMapper.update(employee);
    }

    @Override
    public int updateEmployeeDepartment(Integer departmentId, Integer newDepartmentId) {
        if (departmentId == null) {
            throw new IllegalArgumentException("原部门ID不能为空");
        }

        if (newDepartmentId == null) {
            throw new IllegalArgumentException("新部门ID不能为空");
        }

        return employeeMapper.updateEmployeeDepartment(departmentId, newDepartmentId);
    }

    @Override
    public List<Employee> findByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return employeeMapper.selectByName(name);
    }

    @Override
    public PageResult<Employee> pageListByDepartmentId(int pageNum, int pageSize, Integer departmentId, String name) {
        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("departmentId", departmentId);

        // 添加筛选条件
        if (name != null && !name.trim().isEmpty()) {
            params.put("name", name);
        }

        // 查询总数
        int total = employeeMapper.countTotalByDepartment(params);

        // 查询当前页数据
        List<Employee> pageEmployees = employeeMapper.selectByPageAndDepartment(params);

        // 创建分页结果
        return new PageResult<>(pageNum, pageSize, total, pageEmployees);
    }
} 
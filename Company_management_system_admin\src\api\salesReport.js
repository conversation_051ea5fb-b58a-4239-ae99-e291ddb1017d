import request from '@/utils/request'

/**
 * {{CHENGQI: 销售日报管理端API接口}}
 * {{CHENGQI: 任务ID: P3-LD-010}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报管理端API调用封装}}
 */

// 分页查询销售日报列表（管理端）
export function getSalesReportPage(params) {
    return request({
        url: '/sales-report/page',
        method: 'get',
        params
    })
}

// 获取日报详情
export function getSalesReportById(id) {
    return request({
        url: `/sales-report/detail/${id}`,
        method: 'get'
    })
}

// 删除销售日报（管理员权限）
export function deleteSalesReport(id) {
    return request({
        url: `/sales-report/${id}`,
        method: 'delete'
    })
}

// 获取统计数据
export function getSalesReportStatistics(params) {
    return request({
        url: '/sales-report/statistics',
        method: 'get',
        params
    })
}

// 获取责任心评级分布
export function getResponsibilityDistribution(params) {
    return request({
        url: '/sales-report/responsibility-distribution',
        method: 'get',
        params
    })
}

// 获取检查清单完成度统计
export function getChecklistStats(params) {
    return request({
        url: '/sales-report/checklist-stats',
        method: 'get',
        params
    })
}

// 批量删除日报（管理员权限）
export function batchDeleteSalesReports(ids) {
    return request({
        url: '/sales-report/batch-delete',
        method: 'delete',
        data: { ids }
    })
}

// 导出日报数据
export function exportSalesReports(params) {
    return request({
        url: '/sales-report/export',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

// 为了兼容组件中的导入名称，添加别名导出
export { batchDeleteSalesReports as batchDeleteSalesReport }
export { getSalesReportPage as getAllSalesReportPage }

// {{CHENGQI: 管理端API接口封装完成}}

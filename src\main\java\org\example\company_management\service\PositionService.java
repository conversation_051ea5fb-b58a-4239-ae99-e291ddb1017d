package org.example.company_management.service;

import org.example.company_management.entity.Position;
import org.example.company_management.utils.PageResult;

import java.util.List;

/**
 * 职位服务接口
 */
public interface PositionService {
    /**
     * 获取所有职位列表
     * @return 职位列表
     */
    List<Position> list();
    
    /**
     * 分页获取职位列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param positionName 职位名称（可选）
     * @param departmentId 部门ID（可选）
     * @return 分页结果
     */
    PageResult<Position> pageList(int pageNum, int pageSize, String positionName, Integer departmentId);
    
    /**
     * 根据ID获取职位
     * @param id 职位ID
     * @return 职位信息
     */
    Position getById(Integer id);
    
    /**
     * 根据部门ID获取职位列表
     * @param departmentId 部门ID
     * @return 职位列表
     */
    List<Position> getByDepartmentId(Integer departmentId);
    
    /**
     * 添加职位
     * @param position 职位信息
     */
    void add(Position position);
    
    /**
     * 更新职位
     * @param position 职位信息
     */
    void update(Position position);
    
    /**
     * 删除职位
     * @param id 职位ID
     */
    void delete(Integer id);
    
    /**
     * 检查职位下是否有员工
     * @param id 职位ID
     * @return 员工数量
     */
    int checkPositionHasEmployees(Integer id);
    
    /**
     * 更新职位状态
     * @param id 职位ID
     * @param status 状态
     */
    void updateStatus(Integer id, String status);
    
    /**
     * 更新职位的部门ID
     * @param oldDepartmentId 原部门ID
     * @param newDepartmentId 新部门ID
     * @return 更新的职位数量
     */
    int updatePositionDepartment(Integer oldDepartmentId, Integer newDepartmentId);
    
    /**
     * 获取职位关联的部门ID列表
     * @param positionId 职位ID
     * @return 部门ID列表
     */
    List<Integer> getPositionDepartmentIds(Integer positionId);
    
    /**
     * 设置职位的部门关联关系
     * @param positionId 职位ID
     * @param departmentIds 部门ID列表
     */
    void setPositionDepartments(Integer positionId, List<Integer> departmentIds);
    
    /**
     * 获取职位关联的部门名称列表
     * @param positionId 职位ID
     * @return 部门名称列表
     */
    List<String> getPositionDepartmentNames(Integer positionId);
}

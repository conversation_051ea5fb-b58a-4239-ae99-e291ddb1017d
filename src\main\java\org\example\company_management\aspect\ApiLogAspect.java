package org.example.company_management.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.example.company_management.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * API接口日志切面
 */
@Aspect
@Component
public class ApiLogAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiLogAspect.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 定义切点，拦截所有controller包下的所有方法
     */
    @Pointcut("execution(* org.example.company_management.controller..*.*(..))")
    public void logPointCut() {
    }
    
    /**
     * 环绕通知，记录接口访问日志
     */
    @Around("logPointCut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        
        // 获取请求方法
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 记录请求信息
        Map<String, Object> logMap = new HashMap<>();
        logMap.put("uri", request.getRequestURI()); // 请求URI
        logMap.put("method", request.getMethod()); // 请求方法
        logMap.put("ip", getIpAddress(request)); // 请求IP
        logMap.put("class", joinPoint.getTarget().getClass().getName()); // 请求类名
        logMap.put("function", method.getName()); // 请求方法名
        
        // 记录请求参数
        Object[] args = joinPoint.getArgs();
        try {
            Map<String, Object> params = new HashMap<>();
            String[] paramNames = signature.getParameterNames();
            for (int i = 0; i < paramNames.length; i++) {
                Object arg = args[i];
                if (arg instanceof MultipartFile) {
                    // 对于文件类型，只记录文件名和大小
                    MultipartFile file = (MultipartFile) arg;
                    params.put(paramNames[i], file.getOriginalFilename() + " (" + file.getSize() + " bytes)");
                } else if (!"password".equalsIgnoreCase(paramNames[i]) && !"file".equalsIgnoreCase(paramNames[i])) {
                    // 不记录密码和文件内容
                    params.put(paramNames[i], arg);
                }
            }
            logMap.put("params", params); // 请求参数
        } catch (Exception e) {
            logger.warn("记录接口参数异常", e);
            logMap.put("params", "无法获取请求参数"); // 请求参数异常
        }
        
        long startTime = System.currentTimeMillis();
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 记录返回结果
            long endTime = System.currentTimeMillis();
            logMap.put("time", endTime - startTime); // 执行时间
            
            if (result instanceof Result) {
                Result<?> apiResult = (Result<?>) result;
                logMap.put("code", apiResult.getCode()); // 状态码
                logMap.put("message", apiResult.getMessage()); // 返回消息
                
                // 对于返回数据较大的情况，不记录详细数据
                Object data = apiResult.getData();
                if (data != null) {
                    String dataStr = objectMapper.writeValueAsString(data);
                    if (dataStr.length() > 1000) {
                        logMap.put("data", "返回数据太大，不记录");
                    } else {
                        logMap.put("data", data);
                    }
                }
            } else {
                logMap.put("result", result);
            }
            
            // 记录为INFO级别
            logger.info("API访问: {}", objectMapper.writeValueAsString(logMap));
            
            return result;
        } catch (Throwable e) {
            // 记录异常信息
            long endTime = System.currentTimeMillis();
            logMap.put("time", endTime - startTime); // 执行时间
            logMap.put("status", "失败");
            logMap.put("error", e.getMessage());
            
            // 记录为ERROR级别
            logger.error("API异常: {}", objectMapper.writeValueAsString(logMap), e);
            throw e;
        }
    }
    
    /**
     * 获取IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }
} 
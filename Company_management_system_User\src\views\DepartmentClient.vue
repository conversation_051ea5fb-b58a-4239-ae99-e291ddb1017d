<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import {
    // Assuming these APIs will be created or already exist
    // getDepartmentClients // This will be from a new api/client.js file
    getDepartmentClients
} from '@/api/client'; // Placeholder for actual API import
import {
    getCurrentDepartment,
    getSubDepartments,
    getDepartmentById,
    getResponsibleDepartmentTree
} from '@/api/department'; // Corrected path
import { ElMessage, ElLoading } from 'element-plus';
import { Search, RefreshRight, Calendar, Filter } from '@element-plus/icons-vue';

// Client data
const clientList = ref([]);
const loading = ref(false);
// Filter states
const clientNameSearch = ref('');
const employeeNameSearch = ref(''); // 新增：负责员工姓名搜索
const categorySearch = ref(''); // For client category
const statusSearch = ref('');   // For approval status (审批状态)
const clientStatusSearch = ref(''); // For client status (客户状态)

// Department selection related (copied from DepartmentSalary.vue)
const loadingDepartments = ref(false);
const cascaderOptions = ref([]);
const selectedDepartments = ref([]); // For UI binding, stores array of selected department IDs for cascader
const selectedDepartmentIds = ref([]); // Actual IDs used for query, including children if a parent is selected
const cascaderProps = {
    checkStrictly: true,
    emitPath: false,
    expandTrigger: 'hover',
    multiple: true,
};
const leadingDepartmentIds = ref([]); // Stores IDs of departments user is directly responsible for

// Pagination settings (copied from DepartmentSalary.vue)
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// Add these functions for styling consistency with Client.vue
const getCategoryTagType = (category) => {
    switch (category) {
        case '海运':
            return 'primary';
        case '空运':
            return 'success';
        case '散货':
            return 'warning';
        case '快递':
            return 'info';
        default:
            return ''; // Or 'info'
    }
};

// 根据审批状态设置不同的Tag样式
const getStatusTagType = (status) => {
    switch (status) {
        case '未审核':
            return 'info';
        case '审核中':
            return 'warning';
        case '审核通过':
            return 'success';
        case '已拒绝':
            return 'danger';
        default:
            return '';
    }
};

// 根据客户状态设置不同的Tag样式
const getClientStatusTagType = (clientStatus) => {
    switch (clientStatus) {
        case '报价中':
            return 'warning';
        case '已合作':
            return 'success';
        default:
            return 'info';
    }
};

// 状态显示映射（将后端状态映射为前端显示文本）
const getDisplayStatus = (status) => {
    switch (status) {
        case '已合作':
            return '审核通过';
        default:
            return status || '----';
    }
};

// Load client list (adapted from DepartmentSalary.vue)
const loadDepartmentClientList = async () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.warning('请先选择要查看的部门');
        clientList.value = [];
        pagination.total = 0;
        return;
    }

    let hasInvalidDepartment = false;
    for (const deptId of selectedDepartmentIds.value) {
        if (!isLeadingDepartment(deptId)) {
            hasInvalidDepartment = true;
            break;
        }
    }

    if (hasInvalidDepartment) {
        ElMessage.warning('您只能查看自己负责的部门及其下级部门的客户数据');
        clientList.value = [];
        pagination.total = 0;
        return;
    }

    try {
        loading.value = true;
        
        // 构建请求参数
        let params = {
            page: pagination.page,
            size: pagination.size,
            departmentIds: selectedDepartmentIds.value,
        };

        // 添加可选过滤条件
        if (clientNameSearch.value && clientNameSearch.value.trim() !== '') {
            params.clientName = clientNameSearch.value.trim();
        }
        if (employeeNameSearch.value && employeeNameSearch.value.trim() !== '') {
            params.employeeName = employeeNameSearch.value.trim();
        }
        if (categorySearch.value) {
            params.category = categorySearch.value;
        }
        if (statusSearch.value) {
            params.status = statusSearch.value;
        }
        if (clientStatusSearch.value) {
            params.clientStatus = clientStatusSearch.value;
        }

        // 调用API获取数据
        const response = await getDepartmentClients(params);

        // 成功响应处理
        if (response.code === 200 && response.data) {
            // 提取客户列表数据 - 自适应不同的返回格式
            if (Array.isArray(response.data.list)) {
                clientList.value = response.data.list;
            } else if (Array.isArray(response.data.records)) {
                clientList.value = response.data.records;
            } else if (Array.isArray(response.data)) {
                clientList.value = response.data;
            } else {
                clientList.value = [];
            }
            
            // 处理分页数据
            handlePaginationData(response.data);
            
        } else {
            ElMessage.error(response.message || '获取部门客户数据失败');
            console.error('API错误响应:', response);
            clientList.value = [];
            pagination.total = 0;
        }
    } catch (error) {
        console.error('获取部门客户列表失败:', error);
        ElMessage.error('获取部门客户列表失败，请稍后再试');
        clientList.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

/**
 * 处理分页数据，支持不同的API返回格式
 * @param {Object} data API返回的数据对象
 */
const handlePaginationData = (data) => {
    // 提取总记录数
    pagination.total = data.total || 0;
    
    // 根据API返回同步页码信息
    // 部门客户API使用pageNum/pageSize，工资API可能使用page/size
    if (data.pageNum !== undefined) {
        pagination.page = data.pageNum;
    } else if (data.page !== undefined) {
        pagination.page = data.page;
    }
    
    if (data.pageSize !== undefined) {
        pagination.size = data.pageSize;
    } else if (data.size !== undefined) {
        pagination.size = data.size;
    }
};

// Department handling functions (copied & adapted from DepartmentSalary.vue)
const isLeadingDepartment = (departmentId) => {
    if (leadingDepartmentIds.value.includes(departmentId)) return true;
    for (const option of cascaderOptions.value) {
        if (leadingDepartmentIds.value.includes(option.value)) {
            const isChildOfLeadingDept = (children, targetId) => {
                if (!children || children.length === 0) return false;
                for (const child of children) {
                    if (child.value === targetId) return true;
                    if (child.children && isChildOfLeadingDept(child.children, targetId)) return true;
                }
                return false;
            };
            if (isChildOfLeadingDept(option.children, departmentId)) return true;
        }
    }
    return false;
};

const getAllChildDepartmentIds = (departmentId) => {
    const childIds = [];
    const findDepartmentNode = (nodes, targetId) => {
        if (!nodes || !nodes.length) return null;
        for (const node of nodes) {
            if (node.value === targetId) return node;
            if (node.children && node.children.length) {
                const foundNode = findDepartmentNode(node.children, targetId);
                if (foundNode) return foundNode;
            }
        }
        return null;
    };
    const collectChildIds = (node) => {
        if (!node.children || !node.children.length) return;
        for (const child of node.children) {
            childIds.push(child.value);
            collectChildIds(child);
        }
    };
    const departmentNode = findDepartmentNode(cascaderOptions.value, departmentId);
    if (departmentNode) collectChildIds(departmentNode);
    return childIds;
};

const convertTreeToOptions = (nodes) => {
    if (!nodes || !nodes.length) return [];
    return nodes.map(node => ({
        value: node.departmentId,
        label: node.departmentName,
        children: convertTreeToOptions(node.children || [])
    }));
};

const loadDepartments = async () => {
    try {
        loadingDepartments.value = true;
        const response = await getResponsibleDepartmentTree();
        if (response.code === 200) {
            cascaderOptions.value = (response.data || []).map(dept => ({
                value: dept.departmentId,
                label: dept.departmentName,
                children: convertTreeToOptions(dept.children || [])
            }));
            leadingDepartmentIds.value = (response.data || []).map(dept => dept.departmentId);
            if (leadingDepartmentIds.value.length > 0) {
                const defaultDepartmentId = leadingDepartmentIds.value[0];
                const childDepartmentIds = getAllChildDepartmentIds(defaultDepartmentId);
                selectedDepartments.value = [defaultDepartmentId, ...childDepartmentIds];
                selectedDepartmentIds.value = [defaultDepartmentId, ...childDepartmentIds];
                await loadDepartmentClientList();
            } else {
                ElMessage.warning('您没有任何可以查看客户的部门');
            }
        } else {
            ElMessage.error(response.message || '获取部门信息失败');
        }
    } catch (error) {
        console.error('加载部门信息失败:', error);
        ElMessage.error('加载部门信息失败，请稍后再试');
    } finally {
        loadingDepartments.value = false;
    }
};

const handleCascaderChange = (values) => {
    if (!values || values.length === 0) {
        selectedDepartmentIds.value = [];
        selectedDepartments.value = [];
        pagination.page = 1;
        loadDepartmentClientList();
        return;
    }
    const previousSelection = selectedDepartmentIds.value || [];
    const newlySelected = values.filter(id => !previousSelection.includes(id));
    const deselected = previousSelection.filter(id => !values.includes(id));
    let allDepartmentIds = [...values];
    let uiSelectedDepartments = [...values];

    for (const departmentId of newlySelected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            childIds.forEach(childId => {
                if (!allDepartmentIds.includes(childId)) allDepartmentIds.push(childId);
                if (!uiSelectedDepartments.includes(childId)) uiSelectedDepartments.push(childId);
            });
        }
    }
    for (const departmentId of deselected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            allDepartmentIds = allDepartmentIds.filter(id => !childIds.includes(id));
            uiSelectedDepartments = uiSelectedDepartments.filter(id => !childIds.includes(id));
        }
    }
    selectedDepartments.value = uiSelectedDepartments;
    selectedDepartmentIds.value = allDepartmentIds;
    pagination.page = 1;
    loadDepartmentClientList();
};

// Search or filter
const handleSearchOrFilter = async () => {
    pagination.page = 1;
    await loadDepartmentClientList();
};

// Reset filter
const resetFilter = async () => {
    clientNameSearch.value = '';
    employeeNameSearch.value = '';
    categorySearch.value = '';
    statusSearch.value = '';
    clientStatusSearch.value = '';
    pagination.page = 1;
    // Keep selected departments, just reload data for them
    if (selectedDepartmentIds.value && selectedDepartmentIds.value.length > 0) {
        await loadDepartmentClientList();
    }
};

// Pagination handlers
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadDepartmentClientList();
};

const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadDepartmentClientList();
};

// Format date (YYYY-MM-DD HH:mm:ss)
const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid
    return date.toLocaleString('zh-CN', {
        year: 'numeric', month: '2-digit', day: '2-digit',
        hour: '2-digit', minute: '2-digit', second: '2-digit',
        hour12: false
    }).replace(/\//g, '-');
};

// Client categories and statuses for dropdowns
const clientCategories = ref([
    { value: '海运', label: '海运' },
    { value: '散货', label: '散货' },
    { value: '空运', label: '空运' },
    { value: '快递', label: '快递' },
]);

// 审批状态选项
const approvalStatuses = ref([
    { value: '未审核', label: '未审核' },
    { value: '审核中', label: '审核中' },
    { value: '审核通过', label: '审核通过' },
    { value: '已拒绝', label: '已拒绝' },
]);

// 客户状态选项
const clientStatuses = ref([
    { value: '报价中', label: '报价中' },
    { value: '已合作', label: '已合作' },
]);

// Initial load
onMounted(async () => {
    await loadDepartments();
});

</script>

<template>
    <div class="department-content-container"> <!-- Changed class name for potential specific styling -->
        <!-- Toolbar -->
        <div class="toolbar">
            <div class="filter-actions">
                <el-cascader
                    v-model="selectedDepartments"
                    :options="cascaderOptions"
                    :props="cascaderProps"
                    placeholder="请选择您负责的部门"
                    clearable
                    :loading="loadingDepartments"
                    @change="handleCascaderChange"
                    style="width: 280px; margin-right: 10px;"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="2"
                />

                <el-input
                    v-model="clientNameSearch"
                    placeholder="客户名称"
                    clearable
                    @keyup.enter="handleSearchOrFilter"
                    @clear="handleSearchOrFilter"
                    style="width: 180px; margin-right: 10px;"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearchOrFilter" />
                    </template>
                </el-input>

                <el-input
                    v-model="employeeNameSearch"
                    placeholder="负责员工"
                    clearable
                    @keyup.enter="handleSearchOrFilter"
                    @clear="handleSearchOrFilter"
                    style="width: 180px; margin-right: 10px;"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearchOrFilter" />
                    </template>
                </el-input>

                <el-select v-model="categorySearch" placeholder="客户分类" clearable @change="handleSearchOrFilter" style="width: 150px; margin-right: 10px;">
                    <el-option
                        v-for="item in clientCategories"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>

                <el-select v-model="statusSearch" placeholder="审批状态" clearable @change="handleSearchOrFilter" style="width: 150px; margin-right: 10px;">
                    <el-option
                        v-for="item in approvalStatuses"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>

                <el-select v-model="clientStatusSearch" placeholder="客户状态" clearable @change="handleSearchOrFilter" style="width: 150px; margin-right: 10px;">
                    <el-option
                        v-for="item in clientStatuses"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
                
                <el-button @click="resetFilter">
                    <el-icon><RefreshRight /></el-icon>
                    重置
                </el-button>
            </div>
        </div>

        <!-- Client Table -->
        <el-table
            :data="clientList"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            row-key="clientId" 
            :max-height="'calc(100vh - 280px)'"
            class="custom-table"
        >
            <!-- 序号 -->
            <el-table-column label="序号" min-width="70" align="center">
                <template #default="scope">
                    {{ (pagination.page - 1) * pagination.size + scope.$index + 1 }}
                </template>
            </el-table-column>

            <!-- 客户名称 -->
            <el-table-column label="客户名称" prop="name" min-width="150" align="center" show-overflow-tooltip />

            <!-- 联系人 -->
            <el-table-column label="联系人" prop="contactPerson" min-width="120" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.contactPerson || '----' }}
                </template>
            </el-table-column>

            <!-- 邮箱 -->
            <el-table-column label="邮箱" prop="email" min-width="180" align="center" show-overflow-tooltip />

            <!-- 电话 -->
            <el-table-column label="电话" prop="phone" min-width="120" align="center" show-overflow-tooltip />

            <!-- 国籍/地区 -->
            <el-table-column label="国籍/地区" prop="nationality" min-width="100" align="center" show-overflow-tooltip />

            <!-- 所属部门 -->
            <el-table-column label="部门" prop="departmentName" min-width="120" align="center" show-overflow-tooltip />

            <!-- 负责员工 -->
            <el-table-column label="负责员工" prop="employeeName" min-width="100" align="center" show-overflow-tooltip />

            <!-- 客户分类 -->
            <el-table-column label="客户分类" prop="category" min-width="100" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    <el-tag :type="getCategoryTagType(row.category)" effect="plain">
                        {{ row.category || '待选择' }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 客户状态 -->
            <el-table-column label="客户状态" prop="clientStatus" min-width="100" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    <el-tag :type="getClientStatusTagType(row.clientStatus)" effect="plain">
                        {{ row.clientStatus || '报价中' }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 审批状态 -->
            <el-table-column label="审批状态" prop="status" min-width="100" align="center" show-overflow-tooltip>
                 <template #default="{ row }">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="row.rejectRemark"
                        placement="top"
                        :disabled="!(row.status === '已拒绝' && row.rejectRemark && row.rejectRemark.trim() !== '')"
                    >
                        <el-tag :type="getStatusTagType(row.status)" effect="light">
                            {{ getDisplayStatus(row.status) }}
                        </el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>

            <!-- 备注 -->
            <el-table-column label="备注" prop="remark" min-width="150" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.remark || '----' }}
                </template>
            </el-table-column>

            <!-- 操单时间 -->
            <el-table-column label="操单时间" prop="operationTime" min-width="160" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ formatDate(row.operationTime) }}
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column label="创建时间" prop="createTime" min-width="160" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ formatDate(row.createTime) }}
                </template>
            </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- No data tip -->
        <div v-if="!loading && clientList.length === 0" class="empty-data">
            <el-empty description="暂无客户数据"></el-empty>
        </div>
    </div>
</template>

<style scoped>
.department-content-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap; /* Allow wrapping for smaller screens */
}

.custom-table {
    margin-bottom: 60px; /* Ensure space for pagination */
    border-radius: 6px;
    overflow: hidden; /* For border-radius to take effect on table corners */
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; /* Ensure it's above other content */
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); /* Optional: match other styling */
    z-index: 1; /* Ensure it is above the table if table overflows */
}

.empty-data {
    margin: 40px 0;
    text-align: center;
}

/* Optional: Specific styling for table header or rows if needed */
:deep(.el-table__header-wrapper th) {
    background-color: #f5f7fa; 
    font-weight: 600;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important; 
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Ensure text in cells doesn't wrap and shows ellipsis for overflow */
:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /* text-align: center; */ /* Default from DepartmentSalary, can be overridden per column */
}
</style> 
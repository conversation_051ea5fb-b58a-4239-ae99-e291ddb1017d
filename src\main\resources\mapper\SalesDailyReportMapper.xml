<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
{{CHENGQI: 销售日报Mapper XML映射文件}}
{{CHENGQI: 任务ID: P1-LD-003}}
{{CHENGQI: 负责人: LD}}
{{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
{{CHENGQI: 描述: 销售日报数据访问层XML映射，处理JSON字段和复杂查询}}
-->

<mapper namespace="org.example.company_management.mapper.SalesDailyReportMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.SalesDailyReport">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="employee_id" property="employeeId" jdbcType="INTEGER"/>
        <result column="report_date" property="reportDate" jdbcType="DATE"/>
        <result column="yearly_new_clients" property="yearlyNewClients" jdbcType="INTEGER"/>
        <result column="monthly_new_clients" property="monthlyNewClients" jdbcType="INTEGER"/>
        <result column="days_since_last_new_client" property="daysSinceLastNewClient" jdbcType="INTEGER"/>
        <result column="inquiry_clients" property="inquiryClients" jdbcType="VARCHAR"/>
        <result column="shipping_clients" property="shippingClients" jdbcType="VARCHAR"/>
        <result column="key_development_clients" property="keyDevelopmentClients" jdbcType="VARCHAR"/>
        <result column="responsibility_level" property="responsibilityLevel" jdbcType="VARCHAR"/>
        <result column="end_of_day_checklist" property="endOfDayChecklist" jdbcType="VARCHAR"/>
        <result column="daily_results" property="dailyResults" jdbcType="LONGVARCHAR"/>
        <result column="meeting_report" property="meetingReport" jdbcType="LONGVARCHAR"/>
        <result column="work_diary" property="workDiary" jdbcType="LONGVARCHAR"/>
        <result column="manager_evaluation" property="managerEvaluation" jdbcType="LONGVARCHAR"/>
        <result column="evaluation_time" property="evaluationTime" jdbcType="TIMESTAMP"/>
        <result column="evaluator_id" property="evaluatorId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 带员工信息的结果映射 -->
    <resultMap id="WithEmployeeInfoResultMap" type="org.example.company_management.entity.SalesDailyReport" extends="BaseResultMap">
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="evaluator_name" property="evaluatorName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, employee_id, report_date, yearly_new_clients, monthly_new_clients,
        days_since_last_new_client, inquiry_clients, shipping_clients,
        key_development_clients, responsibility_level, end_of_day_checklist,
        daily_results, meeting_report, work_diary, manager_evaluation,
        evaluation_time, evaluator_id, create_time, update_time
    </sql>

    <!-- 带员工信息的字段 -->
    <sql id="With_Employee_Info_Column_List">
        sdr.id, sdr.employee_id, sdr.report_date, sdr.yearly_new_clients,
        sdr.monthly_new_clients, sdr.days_since_last_new_client,
        sdr.inquiry_clients, sdr.shipping_clients, sdr.key_development_clients,
        sdr.responsibility_level, sdr.end_of_day_checklist, sdr.daily_results,
        sdr.meeting_report, sdr.work_diary, sdr.manager_evaluation,
        sdr.evaluation_time, sdr.evaluator_id, sdr.create_time, sdr.update_time,
        e.name as employee_name, d.department_name, ev.name as evaluator_name
    </sql>

    <!-- 插入销售日报 -->
    <insert id="insert" parameterType="org.example.company_management.entity.SalesDailyReport" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sales_daily_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">employee_id,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="yearlyNewClients != null">yearly_new_clients,</if>
            <if test="monthlyNewClients != null">monthly_new_clients,</if>
            <if test="daysSinceLastNewClient != null">days_since_last_new_client,</if>
            <if test="inquiryClients != null">inquiry_clients,</if>
            <if test="shippingClients != null">shipping_clients,</if>
            <if test="keyDevelopmentClients != null">key_development_clients,</if>
            <if test="responsibilityLevel != null">responsibility_level,</if>
            <if test="endOfDayChecklist != null">end_of_day_checklist,</if>
            <if test="dailyResults != null">daily_results,</if>
            <if test="meetingReport != null">meeting_report,</if>
            <if test="workDiary != null">work_diary,</if>
            <if test="managerEvaluation != null">manager_evaluation,</if>
            <if test="evaluationTime != null">evaluation_time,</if>
            <if test="evaluatorId != null">evaluator_id,</if>
            create_time, update_time
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">#{employeeId},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="yearlyNewClients != null">#{yearlyNewClients},</if>
            <if test="monthlyNewClients != null">#{monthlyNewClients},</if>
            <if test="daysSinceLastNewClient != null">#{daysSinceLastNewClient},</if>
            <if test="inquiryClients != null">#{inquiryClients},</if>
            <if test="shippingClients != null">#{shippingClients},</if>
            <if test="keyDevelopmentClients != null">#{keyDevelopmentClients},</if>
            <if test="responsibilityLevel != null">#{responsibilityLevel},</if>
            <if test="endOfDayChecklist != null">#{endOfDayChecklist},</if>
            <if test="dailyResults != null">#{dailyResults},</if>
            <if test="meetingReport != null">#{meetingReport},</if>
            <if test="workDiary != null">#{workDiary},</if>
            <if test="managerEvaluation != null">#{managerEvaluation},</if>
            <if test="evaluationTime != null">#{evaluationTime},</if>
            <if test="evaluatorId != null">#{evaluatorId},</if>
            NOW(), NOW()
        </trim>
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sales_daily_report
        WHERE id = #{id}
    </select>

    <!-- 根据员工ID和日期查询 -->
    <select id="selectByEmployeeAndDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sales_daily_report
        WHERE employee_id = #{employeeId} AND report_date = #{reportDate}
    </select>

    <!-- 更新销售日报 -->
    <update id="update" parameterType="org.example.company_management.entity.SalesDailyReport">
        UPDATE sales_daily_report
        <set>
            <if test="yearlyNewClients != null">yearly_new_clients = #{yearlyNewClients},</if>
            <if test="monthlyNewClients != null">monthly_new_clients = #{monthlyNewClients},</if>
            <if test="daysSinceLastNewClient != null">days_since_last_new_client = #{daysSinceLastNewClient},</if>
            <if test="inquiryClients != null">inquiry_clients = #{inquiryClients},</if>
            <if test="shippingClients != null">shipping_clients = #{shippingClients},</if>
            <if test="keyDevelopmentClients != null">key_development_clients = #{keyDevelopmentClients},</if>
            <if test="responsibilityLevel != null">responsibility_level = #{responsibilityLevel},</if>
            <if test="endOfDayChecklist != null">end_of_day_checklist = #{endOfDayChecklist},</if>
            <if test="dailyResults != null">daily_results = #{dailyResults},</if>
            <if test="meetingReport != null">meeting_report = #{meetingReport},</if>
            <if test="workDiary != null">work_diary = #{workDiary},</if>
            <if test="managerEvaluation != null">manager_evaluation = #{managerEvaluation},</if>
            <if test="evaluationTime != null">evaluation_time = #{evaluationTime},</if>
            <if test="evaluatorId != null">evaluator_id = #{evaluatorId},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM sales_daily_report WHERE id = #{id}
    </delete>

    <!-- 分页查询（带员工和部门信息） -->
    <select id="selectPageWithEmployeeInfo" parameterType="java.util.Map" resultMap="WithEmployeeInfoResultMap">
        SELECT <include refid="With_Employee_Info_Column_List"/>
        FROM sales_daily_report sdr
        LEFT JOIN employee e ON sdr.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN employee ev ON sdr.evaluator_id = ev.employee_id
        <where>
            <if test="employeeId != null">
                AND sdr.employee_id = #{employeeId}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="startDate != null">
                AND sdr.report_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND sdr.report_date &lt;= #{endDate}
            </if>
            <if test="responsibilityLevel != null and responsibilityLevel != ''">
                AND sdr.responsibility_level = #{responsibilityLevel}
            </if>
            <if test="employeeIds != null and employeeIds.size() > 0">
                AND sdr.employee_id IN
                <foreach collection="employeeIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY sdr.report_date DESC, sdr.create_time DESC
    </select>

    <!-- 查询总数 -->
    <select id="selectCount" parameterType="java.util.Map" resultType="int">
        SELECT COUNT(1)
        FROM sales_daily_report sdr
        LEFT JOIN employee e ON sdr.employee_id = e.employee_id
        <where>
            <if test="employeeId != null">
                AND sdr.employee_id = #{employeeId}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="startDate != null">
                AND sdr.report_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND sdr.report_date &lt;= #{endDate}
            </if>
            <if test="responsibilityLevel != null and responsibilityLevel != ''">
                AND sdr.responsibility_level = #{responsibilityLevel}
            </if>
            <if test="employeeIds != null and employeeIds.size() > 0">
                AND sdr.employee_id IN
                <foreach collection="employeeIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 检查是否存在 -->
    <select id="existsByEmployeeAndDate" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM sales_daily_report
        WHERE employee_id = #{employeeId} AND report_date = #{reportDate}
    </select>

    <!-- 根据员工ID查询日报列表 -->
    <select id="selectByEmployeeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sales_daily_report
        WHERE employee_id = #{employeeId}
        <if test="startDate != null">
            AND report_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND report_date &lt;= #{endDate}
        </if>
        ORDER BY report_date DESC
    </select>

    <!-- 根据部门ID查询日报列表 -->
    <select id="selectByDepartmentId" resultMap="WithEmployeeInfoResultMap">
        SELECT <include refid="With_Employee_Info_Column_List"/>
        FROM sales_daily_report sdr
        LEFT JOIN employee e ON sdr.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN employee ev ON sdr.evaluator_id = ev.employee_id
        WHERE e.department_id = #{departmentId}
        <if test="startDate != null">
            AND sdr.report_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND sdr.report_date &lt;= #{endDate}
        </if>
        ORDER BY sdr.report_date DESC, e.name
    </select>

    <!-- 查询包含特定客户的日报 -->
    <select id="selectByClientId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sales_daily_report
        WHERE
        <choose>
            <when test="clientType == 'inquiry'">
                JSON_CONTAINS(inquiry_clients->'$.clientIds', #{clientId})
            </when>
            <when test="clientType == 'shipping'">
                JSON_CONTAINS(shipping_clients->'$.clientIds', #{clientId})
            </when>
            <when test="clientType == 'key_development'">
                JSON_CONTAINS(key_development_clients->'$.clientIds', #{clientId})
            </when>
            <otherwise>
                (JSON_CONTAINS(inquiry_clients->'$.clientIds', #{clientId}) OR
                 JSON_CONTAINS(shipping_clients->'$.clientIds', #{clientId}) OR
                 JSON_CONTAINS(key_development_clients->'$.clientIds', #{clientId}))
            </otherwise>
        </choose>
        <if test="startDate != null">
            AND report_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND report_date &lt;= #{endDate}
        </if>
        ORDER BY report_date DESC
    </select>

    <!-- 查询员工最近的日报 -->
    <select id="selectRecentByEmployeeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sales_daily_report
        WHERE employee_id = #{employeeId}
        ORDER BY report_date DESC
        LIMIT #{limit}
    </select>

    <!-- 统计查询已删除 -->
    <!-- 注：删除了未使用的统计查询：selectReportStatistics, selectResponsibilityDistribution, selectChecklistCompletionStats -->

    <!-- 批量更新统计字段 -->
    <update id="batchUpdateStatistics" parameterType="java.util.List">
        <foreach collection="reports" item="report" separator=";">
            UPDATE sales_daily_report
            SET yearly_new_clients = #{report.yearlyNewClients},
                monthly_new_clients = #{report.monthlyNewClients},
                days_since_last_new_client = #{report.daysSinceLastNewClient},
                update_time = NOW()
            WHERE id = #{report.id}
        </foreach>
    </update>

    <!-- 更新日报评价信息 -->
    <update id="updateEvaluation">
        UPDATE sales_daily_report
        SET manager_evaluation = #{managerEvaluation},
            evaluation_time = NOW(),
            evaluator_id = #{evaluatorId},
            update_time = NOW()
        WHERE id = #{reportId}
    </update>

</mapper>

<!-- {{CHENGQI: 任务P1-LD-003完成时间: 2025-06-04 10:52:42 +08:00}} -->
<!-- {{CHENGQI: 验收状态: 完整的Mapper接口和XML映射文件创建完成，包含所有CRUD、查询、统计功能}} -->
<!-- {{CHENGQI: 设计原则应用: DRY原则复用SQL片段，JSON字段查询优化，动态SQL提高灵活性}} -->
<!-- {{CHENGQI: 下一步: 执行任务P1-LD-004创建客户统计Service}} -->

<script setup>
import { ref, onMounted, reactive, provide, computed } from 'vue';
import {
    getDepartmentMultiSalaries
} from '../api/salary';
import {
    getCurrentDepartment,
    getSubDepartments,
    getDepartmentById,
    getResponsibleDepartmentTree
} from '../api/department';
import { ElMessage, ElLoading } from 'element-plus';
import { Search, RefreshRight, Calendar, ArrowDown } from '@element-plus/icons-vue';

// 工资数据
const salaryList = ref([]);
const loading = ref(false);
// 选中的日期范围
const dateRange = ref([]);
// 员工姓名搜索
const employeeNameSearch = ref('');

// 部门选择相关
const currentDepartment = ref(null);
const loadingDepartments = ref(false);

// 级联选择器相关
const cascaderOptions = ref([]);
const selectedDepartments = ref([]);
const selectedDepartmentIds = ref([]);
const cascaderProps = {
    checkStrictly: true, // 设置为true，父子节点选择状态独立
    emitPath: false, // 只返回选中的节点的值，而不是整个路径
    expandTrigger: 'hover', // 鼠标悬停时展开子节点
    multiple: true // 启用多选模式
};

// 存储用户负责的部门ID列表
const leadingDepartmentIds = ref([]);

// 判断是否是用户负责的部门
const isLeadingDepartment = (departmentId) => {
    // 直接匹配部门ID
    if (leadingDepartmentIds.value.includes(departmentId)) {
        return true;
    }
    
    // 检查是否是负责部门的子部门
    for (const option of cascaderOptions.value) {
        if (leadingDepartmentIds.value.includes(option.value)) {
            // 递归检查子部门
            const isChildOfLeadingDept = (children, targetId) => {
                if (!children || children.length === 0) {
                    return false;
                }
                
                for (const child of children) {
                    if (child.value === targetId) {
                        return true;
                    }
                    
                    if (child.children && isChildOfLeadingDept(child.children, targetId)) {
                        return true;
                    }
                }
                
                return false;
            };
            
            if (isChildOfLeadingDept(option.children, departmentId)) {
                return true;
            }
        }
    }
    
    return false;
};

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 加载部门工资列表
const loadDepartmentSalaryList = async () => {
    if (!selectedDepartmentIds.value || selectedDepartmentIds.value.length === 0) {
        ElMessage.warning('请先选择要查看的部门');
        return;
    }
    
    // 检查权限
    let hasInvalidDepartment = false;
    for (const deptId of selectedDepartmentIds.value) {
        if (!isLeadingDepartment(deptId)) {
            hasInvalidDepartment = true;
            break;
        }
    }
    
    if (hasInvalidDepartment) {
        ElMessage.warning('您只能查看自己负责的部门及其下级部门的工资数据');
        salaryList.value = []; // 清空工资列表
        pagination.total = 0;
        return;
    }
    
    try {
        loading.value = true;
        let params = {
            page: pagination.page,
            size: pagination.size,
            departmentIds: selectedDepartmentIds.value
        };

        if (dateRange.value && dateRange.value.length === 2) {
            params.startDate = dateRange.value[0].substring(0, 7);
            params.endDate = dateRange.value[1].substring(0, 7);
        }

        if (employeeNameSearch.value && employeeNameSearch.value.trim() !== '') {
            params.employeeName = employeeNameSearch.value.trim();
        }

        const response = await getDepartmentMultiSalaries(params);

        if (response.code === 200) {
            let records = [];
            if (response.data && typeof response.data === 'object') {
                if (Array.isArray(response.data.records)) {
                    records = response.data.records.filter(record => record !== null && record !== undefined);
                    records = records.map((record, index) => {
                        if (!record.id) {
                            record.id = `${record.date}_${record.departmentId || selectedDepartmentIds.value[0]}_${index}`;
                        }
                        if (!record.departmentName && record.department) {
                            record.departmentName = record.department;
                        } else if (!record.departmentName) {
                            record.departmentName = currentDepartment.value ? currentDepartment.value.name : '未知部门';
                        }
                        return record;
                    });
                    pagination.total = response.data.total || 0;
                } else if (Array.isArray(response.data)) {
                    records = response.data.filter(record => record !== null && record !== undefined);
                    records = records.map((record, index) => {
                        if (!record.id) {
                            record.id = `${record.date}_${record.departmentId || selectedDepartmentIds.value[0]}_${index}`;
                        }
                        if (!record.departmentName && record.department) {
                            record.departmentName = record.department;
                        } else if (!record.departmentName) {
                            record.departmentName = currentDepartment.value ? currentDepartment.value.name : '未知部门';
                        }
                        return record;
                    });
                    pagination.total = records.length || 0;
                } else {
                    records = [];
                    pagination.total = 0;
                }
            }
            salaryList.value = records;
            if (records.length === 0) {
                console.log('未查询到工资记录');
            }
        } else {
            ElMessage.error(response.message || '获取部门工资数据失败');
        }
    } catch (error) {
        console.error('获取部门工资列表失败:', error);
        ElMessage.error('获取部门工资列表失败，请稍后再试');
    } finally {
        loading.value = false;
    }
};

// 加载部门及子部门数据
const loadDepartments = async () => {
    try {
        loadingDepartments.value = true;
        
        // 获取当前用户负责的部门树
        const response = await getResponsibleDepartmentTree();
        
        if (response.code === 200) {
            // 直接使用返回的部门树数据
            const departmentTree = response.data;
            
            // 转换为级联选择器所需的格式
            cascaderOptions.value = departmentTree.map(dept => ({
                value: dept.departmentId,
                label: dept.departmentName,
                children: convertTreeToOptions(dept.children || [])
            }));
            
            // 保存用户负责的顶级部门ID列表，用于权限验证
            leadingDepartmentIds.value = departmentTree.map(dept => dept.departmentId);
            
            // 设置默认选中的部门
            if (leadingDepartmentIds.value.length > 0) {
                // 选择用户负责的第一个部门作为默认值
                const defaultDepartmentId = leadingDepartmentIds.value[0];
                
                // 获取默认部门的所有子部门ID
                const childDepartmentIds = getAllChildDepartmentIds(defaultDepartmentId);
                
                // 更新UI显示，同时勾选父部门和所有子部门
                selectedDepartments.value = [defaultDepartmentId, ...childDepartmentIds];
                
                // 更新实际用于查询的部门ID列表
                selectedDepartmentIds.value = [defaultDepartmentId, ...childDepartmentIds];
                
                // 加载工资数据
                await loadDepartmentSalaryList();
            } else {
                ElMessage.warning('您没有任何可以查看工资的部门');
            }
        } else {
            ElMessage.error(response.message || '获取部门信息失败');
        }
    } catch (error) {
        console.error('加载部门信息失败:', error);
        ElMessage.error('加载部门信息失败，请稍后再试');
    } finally {
        loadingDepartments.value = false;
    }
};

// 将后端返回的树结构转换为级联选择器所需的格式
const convertTreeToOptions = (nodes) => {
    if (!nodes || !nodes.length) return [];
    
    return nodes.map(node => ({
        value: node.departmentId,
        label: node.departmentName,
        children: convertTreeToOptions(node.children || [])
    }));
};

// 处理级联选择器变化
const handleCascaderChange = (values) => {
    if (!values || values.length === 0) {
        selectedDepartmentIds.value = [];
        selectedDepartments.value = [];
        pagination.page = 1; // 重置页码
        loadDepartmentSalaryList();
        return;
    }
    
    // 获取当前选择和之前选择的差异
    const previousSelection = selectedDepartmentIds.value || [];
    
    // 找出新选中的部门（添加的部门）
    const newlySelected = values.filter(id => !previousSelection.includes(id));
    
    // 找出被取消选择的部门（移除的部门）
    const deselected = previousSelection.filter(id => !values.includes(id));
    
    // 初始化结果数组（从用户的当前选择开始）
    let allDepartmentIds = [...values];
    let uiSelectedDepartments = [...values];
    
    // 第一步：处理新选中的父部门，自动添加其所有子部门
    for (const departmentId of newlySelected) {
        const childIds = getAllChildDepartmentIds(departmentId);
        if (childIds.length > 0) {
            // 添加子部门ID到选择列表，避免重复
            for (const childId of childIds) {
                if (!allDepartmentIds.includes(childId)) {
                    allDepartmentIds.push(childId);
                }
                // 同时添加到UI显示列表，使子部门在UI上也被选中
                if (!uiSelectedDepartments.includes(childId)) {
                    uiSelectedDepartments.push(childId);
                }
            }
        }
    }
    
    // 第二步：处理被取消选择的父部门，自动取消其所有子部门
    for (const departmentId of deselected) {
        // 获取该部门的所有子部门ID
        const childIds = getAllChildDepartmentIds(departmentId);
        
        // 从结果数组中移除被取消的子部门
        if (childIds.length > 0) {
            // 从allDepartmentIds中移除所有子部门
            allDepartmentIds = allDepartmentIds.filter(id => !childIds.includes(id));
            
            // 从UI显示列表中移除所有子部门
            uiSelectedDepartments = uiSelectedDepartments.filter(id => !childIds.includes(id));
        }
    }
    
    // 更新级联选择器显示的值（包含父部门和所有子部门）
    selectedDepartments.value = uiSelectedDepartments;
    // 更新实际用于查询的部门ID列表
    selectedDepartmentIds.value = allDepartmentIds;
    
    pagination.page = 1; // 重置页码
    loadDepartmentSalaryList();
};

// 搜索或按月过滤工资
const handleSearchOrFilter = async () => {
    pagination.page = 1;
    await loadDepartmentSalaryList();
};

// 重置过滤条件
const resetFilter = async () => {
    dateRange.value = [];
    employeeNameSearch.value = ''; // 清空员工姓名搜索
    pagination.page = 1;
    await loadDepartmentSalaryList();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadDepartmentSalaryList();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadDepartmentSalaryList();
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    // 检查日期是否为YYYY-MM格式
    const datePattern = /^\d{4}-\d{2}$/;
    if (datePattern.test(dateString)) {
        const [year, month] = dateString.split('-');
        return `${year}年${month}月`;
    }
    return dateString;
};

// 递归获取部门的所有子部门ID
const getAllChildDepartmentIds = (departmentId) => {
    const childIds = [];
    
    // 找到对应的部门节点
    const findDepartmentNode = (nodes, targetId) => {
        if (!nodes || !nodes.length) return null;
        
        for (const node of nodes) {
            if (node.value === targetId) {
                return node;
            }
            
            if (node.children && node.children.length) {
                const foundNode = findDepartmentNode(node.children, targetId);
                if (foundNode) return foundNode;
            }
        }
        
        return null;
    };
    
    // 递归收集子部门ID
    const collectChildIds = (node) => {
        if (!node.children || !node.children.length) return;
        
        for (const child of node.children) {
            childIds.push(child.value);
            collectChildIds(child);
        }
    };
    
    const departmentNode = findDepartmentNode(cascaderOptions.value, departmentId);
    if (departmentNode) {
        collectChildIds(departmentNode);
    }
    
    return childIds;
};

// 初始加载
onMounted(async () => {
    await loadDepartments();
});
</script>

<template>
    <div class="department-salary-container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="filter-actions">
                <!-- 部门级联选择器 -->
                <el-cascader
                    v-model="selectedDepartments"
                    :options="cascaderOptions"
                    :props="cascaderProps"
                    placeholder="请选择您负责的部门"
                    clearable
                    :loading="loadingDepartments"
                    @change="handleCascaderChange"
                    style="width: 280px; margin-right: 10px;"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="3"
                />
                
                <!-- 日期选择器 -->
                <el-date-picker
                    v-model="dateRange"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM-DD"
                    :prefix-icon="Calendar"
                    @change="handleSearchOrFilter"
                    clearable
                    style="margin-right: 10px;"
                />

                <!-- 员工姓名搜索框 -->
                <el-input
                    v-model="employeeNameSearch"
                    placeholder="请输入员工姓名"
                    clearable
                    @keyup.enter="handleSearchOrFilter"
                    @clear="handleSearchOrFilter"
                    style="width: 200px; margin-right: 10px;"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearchOrFilter" />
                    </template>
                </el-input>
                
                <el-button @click="resetFilter">
                    <el-icon><RefreshRight /></el-icon>
                    重置
                </el-button>
            </div>
        </div>

        <!-- 工资表格 -->
        <el-table
            :data="salaryList"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            row-key="id"
            :max-height="'calc(100vh - 280px)'"
            class="custom-table"
        >
            <el-table-column
                label="年月"
                prop="date"
                width="120"
                align="center"
            >
                <template #default="{ row }">
                    <span v-if="row.date" class="date-format">{{ formatDate(row.date) }}</span>
                    <el-tag v-else type="info" size="small">暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="部门"
                prop="departmentName"
                min-width="150"
                align="center"
            >
                <template #default="{ row }">
                    {{ row.departmentName || row.department || '总计' }}
                </template>
            </el-table-column>
            
            <!-- 员工姓名列 -->
            <el-table-column
                label="员工姓名"
                prop="employeeName"
                min-width="120"
                align="center"
            >
                <template #default="{ row }">
                    <template v-if="row.employeeName">
                        {{ row.employeeName }}
                    </template>
                    <el-tag v-else type="info" size="small">暂无数据</el-tag>
                </template>
            </el-table-column>
            
            <!-- 职位列 -->
            <el-table-column
                label="职位"
                prop="position"
                min-width="120"
                align="center"
            >
                <template #default="{ row }">
                    <template v-if="row.position">
                        {{ row.position }}
                    </template>
                    <el-tag v-else type="info" size="small">暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="基本工资"
                prop="basicSalary"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.basicSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="奖金"
                prop="performanceBonus"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.performanceBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="全勤奖"
                prop="fullAttendanceBonus"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.fullAttendanceBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="业务与操作奖金"
                prop="businessOperationBonus"
                min-width="160" 
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.businessOperationBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实得金额"
                prop="sumSalary"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.sumSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="请假"
                prop="leaveDeduction"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.leaveDeduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="扣款"
                prop="deduction"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.deduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="迟到与缺卡"
                prop="lateDeduction"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.lateDeduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="社会保险费个人部分"
                prop="socialSecurityPersonal"
                min-width="180" 
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.socialSecurityPersonal) }}
                </template>
            </el-table-column>

            <el-table-column
                label="公积金"
                prop="providentFund"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.providentFund) }}
                </template>
            </el-table-column>

            <el-table-column
                label="代扣代缴个税"
                prop="tax"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.tax) }}
                </template>
            </el-table-column>

            <el-table-column
                label="水电费"
                prop="waterElectricityFee"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.waterElectricityFee) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实发工资"
                prop="actualSalary"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="报销"
                prop="reimbursement"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.reimbursement) }}
                </template>
            </el-table-column>

            <el-table-column
                label="私帐"
                prop="privateAccount"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.privateAccount) }}
                </template>
            </el-table-column>

            <el-table-column
                label="合计"
                prop="totalSalary"
                min-width="180"
                align="right"
            >
                <template #default="{ row }">
                    <span class="total-salary">{{ formatCurrency(row.totalSalary) }}</span>
                </template>
            </el-table-column>

            <el-table-column
                label="备注"
                prop="remark"
                min-width="180" 
                align="left" 
                show-overflow-tooltip 
            >
                <template #default="{ row }">
                    {{ row.remark || '-' }}
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 无数据提示 -->
        <div v-if="!loading && salaryList.length === 0" class="empty-data">
            <el-empty description="暂无工资数据"></el-empty>
        </div>
    </div>
</template>

<style scoped>
.department-salary-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.custom-table {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.empty-data {
    margin: 40px 0;
    text-align: center;
}

.date-format {
    font-weight: bold;
    color: #333;
}

.total-salary {
    font-weight: bold;
    color: #333;
}

.operation-column :deep(.cell) {
    padding: 0 10px;
}

:deep(.el-table__header-wrapper th) {
    background-color: #f5f7fa;
    font-weight: 600;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}
</style> 
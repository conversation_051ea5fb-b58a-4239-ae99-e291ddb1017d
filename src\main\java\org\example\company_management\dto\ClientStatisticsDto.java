package org.example.company_management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {{CHENGQI: 客户统计数据传输对象}}
 * {{CHENGQI: 任务ID: P1-LD-004}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 客户统计数据DTO，遵循数据传输对象设计模式}}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClientStatisticsDto {
    
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 年度新客户总数
     */
    private Integer yearlyNewClients;
    
    /**
     * 当月新客户总数
     */
    private Integer monthlyNewClients;
    
    /**
     * 距离上次出新客户天数
     */
    private Integer daysSinceLastNewClient;
    
    /**
     * 统计计算时间
     */
    private String calculatedAt;
}

// {{CHENGQI: DTO类创建完成}}

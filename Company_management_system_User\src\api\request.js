import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 创建axios实例
const request = axios.create({
	baseURL: '/api', // URL前缀
	timeout: 30000, // 请求超时时间
})

// 请求拦截器
request.interceptors.request.use(
	(config) => {
		const token = localStorage.getItem('token')
		if (token) {
			config.headers['Authorization'] = `Bearer ${token}`
		}
		return config
	},
	(error) => {
		console.log(error)
		return Promise.reject(error)
	}
)

// 响应拦截器
request.interceptors.response.use(
	(response) => {
		const { code, message, data } = response.data

		// 如果不是成功状态码
		if (code !== 200) {
			// 对于验证错误(code 400)，不显示全局错误消息，让调用方处理
			if (code === 400) {
				return response.data
			}
			
			ElMessage.error(message || '操作失败')

			// 如果是未授权，清除token并跳转到登录页
			if (code === 401) {
				localStorage.removeItem('token')
				router.push('/login')
			}

			return Promise.reject(new Error(message || '操作失败'))
		}

		return response.data
	},
	(error) => {
		let message = error.message || '请求失败'

		if (error.response) {
			// 如果是未授权，清除token并跳转到登录页
			if (error.response.status === 401) {
				localStorage.removeItem('token')
				router.push('/login')
				message = '登录已过期，请重新登录'
			} else if (error.response.status === 404) {
				message = '请求的资源不存在'
			} else if (error.response.status === 500) {
				message = '服务器内部错误'
			}
		}

		ElMessage.error(message)
		return Promise.reject(error)
	}
)

export default request

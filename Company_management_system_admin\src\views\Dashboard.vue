<script setup>
import { ref, reactive, onMounted, computed, markRaw, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/token';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Menu as IconMenu,
    User,
    Briefcase,
    ArrowDown,
    Management,
    DataAnalysis,
    Monitor,
    OfficeBuilding,
    Position,
    Money,
    HomeFilled,
    Edit,
    Document,
    Notebook,
} from '@element-plus/icons-vue';
import { login, logout, getUserInfo, updateProfile } from '../api/auth';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const user = ref(null);
const isCollapse = ref(false);
const activeMenu = ref('');
const profileFormRef = ref(null);

// 面包屑导航相关
const breadcrumbs = ref([]);

// 个人信息弹窗相关
const profileDialogVisible = ref(false);
const isEditing = ref(false);
const profileForm = reactive({
    name: '',
    email: '',
    phone: '',
    idCard: '',
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    departmentName: '',
    positionName: '',
    entryDate: '',
    status: '',
    logisticsRoute: '',
});
const profileRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] },
    ],
    oldPassword: [
        {
            validator: (rule, value, callback) => {
                // 如果新密码有值，原密码必须填写
                if (profileForm.newPassword && !value) {
                    callback(new Error('修改密码时需要输入原密码'));
                }
                // 如果原密码有值，新密码必须填写
                else if (value && !profileForm.newPassword) {
                    callback(new Error('请输入新密码'));
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
    newPassword: [
        {
            validator: (rule, value, callback) => {
                // 如果原密码有值，新密码必须填写
                if (profileForm.oldPassword && !value) {
                    callback(new Error('请输入新密码'));
                }
                // 如果新密码有值，验证密码规则
                else if (value) {
                    // 密码长度至少8位
                    if (value.length < 8) {
                        callback(new Error('密码长度至少为8位'));
                        return;
                    }
                    // 必须包含大小写字母和数字
                    const hasUpperCase = /[A-Z]/.test(value);
                    const hasLowerCase = /[a-z]/.test(value);
                    const hasNumber = /\d/.test(value);

                    if (!hasUpperCase || !hasLowerCase || !hasNumber) {
                        callback(new Error('密码必须包含大小写字母和数字'));
                        return;
                    }

                    // 如果原密码没有值，不允许修改密码
                    if (!profileForm.oldPassword) {
                        callback(new Error('请先输入原密码'));
                        return;
                    }
                }
                // 当新密码变化时，验证确认密码
                if (profileForm.confirmPassword) {
                    profileFormRef.value?.validateField('confirmPassword');
                }
                callback();
            },
            trigger: 'blur',
        },
    ],
    confirmPassword: [
        {
            validator: (rule, value, callback) => {
                if (profileForm.newPassword && !value) {
                    callback(new Error('请确认新密码'));
                } else if (value !== profileForm.newPassword) {
                    callback(new Error('两次输入的密码不一致'));
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
};

// 主菜单定义 (所有可能的菜单项)
// 这个列表可以从 src/config/adminMenuConfig.js 导入，如果已在那里定义了更完整的结构
// 或者直接在此处定义以确保包含所有必要的属性如 path, isMenuItem, children
const masterMenuItems = [
    { id: 'dashboard', title: '首页', path: '/', icon: markRaw(HomeFilled), isMenuItem: true },
    {
        id: 'organization', title: '组织管理', icon: markRaw(OfficeBuilding),
        // isMenuItem: false (implied if children exist and not a direct link)
        children: [
            { id: 'department', title: '部门管理', path: '/department', isMenuItem: true },
            { id: 'position', title: '职位管理', path: '/position', isMenuItem: true },
        ],
    },
    {
        id: 'personnel', title: '人员管理', icon: markRaw(User),
        children: [
            { id: 'employee', title: '员工管理', path: '/employee', isMenuItem: true },
            { id: 'client', title: '客户管理', path: '/client', isMenuItem: true },
        ],
    },
    {
        id: 'performance', title: '业绩管理', icon: markRaw(DataAnalysis),
        children: [
            { id: 'performance-analysis', title: '业绩分析', path: '/performance-analysis', isMenuItem: true },
            { id: 'salary', title: '工资管理', path: '/salary', isMenuItem: true },
            { id: 'department-expense', title: '部门开销', path: '/department-expense', icon: markRaw(Money), isMenuItem: true },
            { id: 'employee-expense', title: '员工费用', path: '/employee-expense', icon: markRaw(Money), isMenuItem: true },
        ],
    },
    {
        id: 'sales-report', title: '销售日报', icon: markRaw(Document),
        children: [
            { id: 'sales-report-management', title: '日报管理', path: '/sales-report-management', isMenuItem: true },
        ],
    },
    { id: 'petty-cash', title: '备用金管理', path: '/petty-cash', icon: markRaw(Money), isMenuItem: true },
];

// 计算属性：根据权限动态生成菜单
const menuItems = computed(() => {
    const accessibleIds = new Set(authStore.accessibleMenuItems || []);

    // Helper function for deep cloning and re-applying markRaw (important!)
    function cloneAndProcessMenuItems(items) {
        return items.map(item => {
            const newItem = { ...item }; // Shallow clone first
            if (item.icon && typeof item.icon === 'object') { // Check if icon is a Vue component object
                 // If icon object might lose reactivity or markRaw status, re-apply markRaw
                 // This assumes item.icon is the component itself or an object that markRaw understands
                newItem.icon = markRaw(item.icon.__v_raw || item.icon); 
            }
            if (item.children) {
                newItem.children = cloneAndProcessMenuItems(item.children);
            }
            return newItem;
        });
    }

    function filterRecursive(itemsToFilter) {
        return itemsToFilter.reduce((acc, item) => {
            const newItem = { ...item }; // Work with a clone

            if (item.children) {
                // Recursively filter children first.
                // The result will be an array of children that the user has access to.
                newItem.children = filterRecursive(item.children);
            }

            // 条件1: 该菜单项本身被显式授权
            const isDirectlyAccessible = accessibleIds.has(item.id);
            // 条件2: 该菜单项有子菜单，并且至少一个子菜单是可访问的
            const hasAccessibleChildren = newItem.children && newItem.children.length > 0;

            if (isDirectlyAccessible || hasAccessibleChildren) {
                acc.push(newItem);
            }
            return acc;
        }, []);
    }
    
    const clonedMasterItems = cloneAndProcessMenuItems(masterMenuItems);
    return filterRecursive(clonedMasterItems);
});

// 扁平化菜单，方便根据路径查找菜单项
const flattenedMenuItems = computed(() => {
    const flattened = [];
    // This function now operates on the already filtered `menuItems` computed property
    function flatten(items, parent = null) {
        items.forEach(item => {
            const flatItem = {
                id: item.id,
                title: item.title,
                path: item.path,
                // isMenuItem: item.isMenuItem, // Optional: if needed by breadcrumb logic
            };
            if (parent) {
                flatItem.parentId = parent.id;
                flatItem.parentTitle = parent.title;
            }
            flattened.push(flatItem);
            if (item.children) {
                flatten(item.children, item);
            }
        });
    }
    flatten(menuItems.value); // Use the filtered menuItems.value here
    return flattened;
});

// 根据当前路由更新面包屑和激活的菜单
const updateBreadcrumbsAndActiveMenu = () => {
    const currentPath = route.path;
    const currentMenuItem = flattenedMenuItems.value.find(
        (item) => item.path === currentPath
    );

    if (currentMenuItem) {
        activeMenu.value = currentMenuItem.id; 
        breadcrumbs.value = [];
        if (currentMenuItem.parentTitle) {
            breadcrumbs.value.push({
                title: currentMenuItem.parentTitle,
                path: null, 
            });
        }
        breadcrumbs.value.push({
            title: currentMenuItem.title,
            path: currentMenuItem.path,
        });
    } else {
        // Fallback for unmatched paths, e.g., /dashboard root might not have a direct menu item if Welcome is default
        // Or if the path doesn't map to any menu item (e.g. /login)
        const dashboardHome = menuItems.value.find(item => item.path === '/' || item.id === 'dashboard');
        if (dashboardHome) {
             activeMenu.value = dashboardHome.id;
             breadcrumbs.value = [{ title: dashboardHome.title, path: dashboardHome.path }];
        } else {
            activeMenu.value = ''; // Or a default active menu
            breadcrumbs.value = [{ title: '首页', path: '/'}]; // Default breadcrumb
        }
    }
};

onMounted(async () => {
    // Ensure user info and permissions are loaded, preferably in a route guard or App.vue
    // For robustness, can check here too.
    if (!authStore.user || authStore.accessibleMenuItems.length === 0) {
        if (authStore.isAuthenticated) { // Only fetch if authenticated but data is missing
            try {
                await authStore.fetchUserInfoAndSet();
            } catch (error) {
                console.error("Dashboard onMounted: Failed to fetch user info/permissions", error);
                 if (error.message !== '获取用户信息失败' && !error.message.includes('登录已过期')) {
                    ElMessage.error('加载用户权限失败，请稍后重试。');
                 }
                // If fetchUserInfoAndSet handles logout on critical failure, no need to push to login here
            }
        }
    }
    user.value = authStore.user; // Update local ref if used in template
    updateBreadcrumbsAndActiveMenu(); // Call after potential data fetch
});

// 监听路由变化以更新面包屑和激活菜单
watch(
    () => route.path,
    () => {
        updateBreadcrumbsAndActiveMenu();
    },
    { immediate: true }
);

// 获取用户角色文本
const userRoleText = computed(() => {
    if (!user.value) return '';
    return user.value.role === 'admin' ? '管理员' : '普通用户';
});

// 处理菜单点击
const handleMenuClick = (path) => {
    router.push(path);
};

// 登出方法
const handleLogout = async () => {
    try {
        ElMessageBox.confirm('确定要退出登录吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(async () => {
                try {
                    await logout();
                    authStore.logout();
                    router.push('/login');
                    ElMessage.success('已成功退出登录');
                } catch (error) {
                    ElMessage.error('登出时发生错误');
                }
            })
            .catch(() => {
                // 取消操作
            });
    } catch (error) {
        // 处理错误
    }
};

// 跳转到个人信息页面
const goToProfile = () => {
    router.push('/profile');
};

// 控制侧边栏折叠
const toggleSidebar = () => {
    isCollapse.value = !isCollapse.value;
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
};

// 获取用户详细信息
const fetchUserInfo = async () => {
    try {
        const response = await getUserInfo();
        if (response.code === 200) {
            user.value = response.data;
            // 初始化表单数据
            Object.assign(profileForm, {
                name: user.value.name,
                email: user.value.email,
                phone: user.value.phone,
                idCard: user.value.idCard,
                oldPassword: '',
                newPassword: '',
                confirmPassword: '',
                departmentName: user.value.departmentName,
                positionName: user.value.positionName,
                entryDate: formatDate(user.value.entryDate),
                status: user.value.status === 'Active' ? '在职' : '离职',
                logisticsRoute: user.value.logisticsRoute,
            });
        }
    } catch (error) {
        ElMessage.error('获取用户信息失败');
    }
};

// 显示个人信息弹窗
const showProfileDialog = async () => {
    await fetchUserInfo(); // 获取最新的用户信息
    profileDialogVisible.value = true;
    isEditing.value = false;
};

// 切换编辑模式
const toggleEdit = () => {
    isEditing.value = !isEditing.value;
    if (!isEditing.value) {
        // 取消编辑时重置表单
        Object.assign(profileForm, {
            name: user.value.name,
            email: user.value.email,
            phone: user.value.phone,
            idCard: user.value.idCard,
            oldPassword: '',
            newPassword: '',
            confirmPassword: '',
            departmentName: user.value.departmentName,
            positionName: user.value.positionName,
            entryDate: formatDate(user.value.entryDate),
            status: user.value.status === 'Active' ? '在职' : '离职',
            logisticsRoute: user.value.logisticsRoute,
        });
    }
};

// 提交个人信息更新
const submitProfileUpdate = async (formEl) => {
    if (!formEl) return;

    try {
        // 先进行表单验证
        const valid = await formEl.validate();

        if (valid) {
            // 验证密码修改相关的逻辑
            if (profileForm.newPassword) {
                if (!profileForm.oldPassword) {
                    ElMessage.error('修改密码时需要输入原密码');
                    return;
                }
                if (profileForm.newPassword !== profileForm.confirmPassword) {
                    ElMessage.error('两次输入的新密码不一致');
                    return;
                }
            }

            // 构造请求数据，只包含后端需要的字段
            const updateData = {
                name: profileForm.name,
                phone: profileForm.phone,
                email: profileForm.email,
                password: profileForm.newPassword || undefined,
                oldPassword: profileForm.oldPassword || undefined,
            };

            const response = await updateProfile(updateData);

            if (response.code === 200) {
                ElMessage.success('个人信息更新成功');
                isEditing.value = false;
                await fetchUserInfo(); // 重新获取用户信息
                profileDialogVisible.value = false; // 关闭弹窗
            } else {
                ElMessage.error(response.message || '更新失败');
            }
        }
    } catch (error) {
        if (error.response) {
            ElMessage.error(error.response.data.message || '更新失败');
        } else {
            ElMessage.error(error.message || '更新失败');
        }
    }
};
</script>

<template>
    <div class="app-container">
        <el-container class="layout-container">
            <!-- 侧边栏 -->
            <el-aside
                :width="isCollapse ? '64px' : '200px'"
                class="sidebar"
            >
                <!-- Logo 区域 -->
                <div class="logo-container">
                    <div
                        class="logo-content"
                        :class="{ 'collapsed': isCollapse }"
                    >
                        <img
                            src="../assets/img/logo.jpg"
                            class="logo-img"
                            alt="Logo"
                            v-if="!isCollapse"
                        >
                        <span
                            class="logo-text-collapsed"
                            v-if="isCollapse"
                        >中航</span>
                    </div>
                </div>

                <!-- 侧边菜单 -->
                <el-scrollbar class="sidebar-menu-container">
                    <el-menu
                        :default-active="activeMenu"
                        class="sidebar-menu"
                        :collapse="isCollapse"
                        :collapse-transition="false"
                        background-color="#001529"
                        text-color="#fff"
                        active-text-color="#409EFF"
                    >
                        <template v-for="item in menuItems" :key="item.id">
                            <!-- 如果项目没有子项，或者明确标记为 isMenuItem，则渲染为 el-menu-item -->
                            <el-menu-item
                                v-if="!item.children || item.isMenuItem"
                                :index="item.id"
                                @click="handleMenuClick(item.path)"
                                class="menu-item-with-transition"
                            >
                                <el-icon v-if="item.icon">
                                    <component :is="item.icon" />
                                </el-icon>
                                <template #title>
                                    <span>{{ item.title }}</span>
                                </template>
                            </el-menu-item>

                            <!-- 如果项目有子项，则渲染为 el-sub-menu -->
                            <el-sub-menu
                                v-else-if="item.children && item.children.length > 0"
                                :index="item.id"
                            >
                                <template #title>
                                    <el-icon v-if="item.icon">
                                        <component :is="item.icon" />
                                    </el-icon>
                                    <span>{{ item.title }}</span>
                                </template>
                                <el-menu-item
                                    v-for="child in item.children"
                                    :key="child.id"
                                    :index="child.id"
                                    @click="handleMenuClick(child.path)"
                                    class="menu-item-with-transition"
                                >
                                    <span>{{ child.title }}</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </template>
                    </el-menu>
                </el-scrollbar>

                <!-- 伸缩按钮放在左侧下方 -->
                <div class="sidebar-footer">
                    <el-icon
                        class="toggle-button"
                        @click="toggleSidebar"
                    >
                        <IconMenu />
                    </el-icon>
                </div>
            </el-aside>

            <!-- 主内容区域 -->
            <el-container class="main-container">
                <!-- 顶部导航栏 -->
                <el-header class="main-header">
                    <div class="header-left">
                        <!-- 替换原来的标题，添加面包屑导航 -->
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item
                                v-for="(item, index) in breadcrumbs"
                                :key="index"
                                :to="item.path ? { path: item.path } : null"
                            >
                                {{ item.title }}
                            </el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    <div class="header-right">
                        <el-dropdown
                            trigger="hover"
                            @command="(command) => command === 'logout' ? handleLogout() : showProfileDialog()"
                        >
                            <div class="user-info">
                                <el-avatar
                                    :size="32"
                                    :src="user?.avatar || 'https://ui-avatars.com/api/?name=' + user?.name + '&background=random'"
                                    class="user-avatar"
                                ></el-avatar>
                                <span class="user-name">{{ user?.name }}</span>
                                <el-icon class="dropdown-icon">
                                    <ArrowDown />
                                </el-icon>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="profile">
                                        <el-icon>
                                            <User />
                                        </el-icon> 个人信息
                                    </el-dropdown-item>
                                    <el-dropdown-item command="logout">
                                        <el-icon>
                                            <Money />
                                        </el-icon> 退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 内容区域 -->
                <el-main class="main-content">
                    <router-view></router-view>
                </el-main>
            </el-container>
        </el-container>

        <!-- 个人信息弹窗 -->
        <el-dialog
            v-model="profileDialogVisible"
            :title="isEditing ? '编辑个人信息' : '个人信息'"
            width="500px"
            :close-on-click-modal="false"
            @closed="isEditing = false"
        >
            <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
                :disabled="!isEditing"
                status-icon
            >
                <!-- 基本信息部分 -->
                <el-form-item
                    label="姓名"
                    prop="name"
                >
                    <el-input v-model="profileForm.name" />
                </el-form-item>
                <el-form-item
                    label="手机号"
                    :prop="isEditing ? 'phone' : ''"
                >
                    <el-input v-model="profileForm.phone" :disabled="!isEditing" />
                </el-form-item>
                <el-form-item
                    label="邮箱"
                    prop="email"
                >
                    <el-input v-model="profileForm.email" />
                </el-form-item>
                <el-form-item 
                    v-if="!isEditing" 
                    label="身份证号"
                >
                    <el-input v-model="profileForm.idCard" disabled />
                </el-form-item>

                <!-- 只在查看模式下显示的信息 -->
                <template v-if="!isEditing">
                    <el-form-item label="所属部门">
                        <el-input
                            v-model="profileForm.departmentName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="职位">
                        <el-input
                            v-model="profileForm.positionName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="入职日期">
                        <el-input
                            v-model="profileForm.entryDate"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="在职状态">
                        <el-input
                            v-model="profileForm.status"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="物流航线">
                        <el-input
                            v-model="profileForm.logisticsRoute"
                            disabled
                        />
                    </el-form-item>
                </template>

                <!-- 只在编辑模式下显示的密码修改部分 -->
                <template v-if="isEditing">
                    <el-divider content-position="center">修改密码（选填）</el-divider>
                    <el-form-item
                        label="原密码"
                        prop="oldPassword"
                    >
                        <el-input
                            v-model="profileForm.oldPassword"
                            type="password"
                            show-password
                            placeholder="修改密码时必填"
                        />
                    </el-form-item>
                    <el-form-item
                        label="新密码"
                        prop="newPassword"
                    >
                        <el-input
                            v-model="profileForm.newPassword"
                            type="password"
                            show-password
                            placeholder="至少8位，必须包含大小写字母和数字"
                        />
                    </el-form-item>
                    <el-form-item
                        label="确认密码"
                        prop="confirmPassword"
                    >
                        <el-input
                            v-model="profileForm.confirmPassword"
                            type="password"
                            show-password
                            placeholder="请再次输入新密码"
                        />
                    </el-form-item>
                </template>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="profileDialogVisible = false">关闭</el-button>
                    <template v-if="!isEditing">
                        <el-button
                            type="primary"
                            @click="toggleEdit"
                        >
                            编辑信息
                        </el-button>
                    </template>
                    <template v-else>
                        <el-button @click="toggleEdit">取消</el-button>
                        <el-button
                            type="primary"
                            @click="submitProfileUpdate(profileFormRef)"
                        >
                            保存
                        </el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style scoped>
.app-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

.layout-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #001529;
    color: white;
    height: 100vh;
    transition: width 0.3s;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.logo-container {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #ffffff;
}

.logo-content {
    display: flex;
    align-items: center;
    overflow: hidden;
}

.logo-content.collapsed {
    justify-content: center;
}

.logo-img {
    height: 40px;
    object-fit: contain;
}

.logo-text-collapsed {
    color: #001529;
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
}

/* 侧边栏底部放置伸缩按钮 */
.sidebar-footer {
    padding: 16px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.toggle-button {
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 18px;
    transition: color 0.3s;
}

.toggle-button:hover {
    color: white;
}

/* 菜单样式 */
.sidebar-menu-container {
    flex: 1;
    overflow-y: auto;
}

.sidebar-menu {
    border-right: none;
}

.sidebar-menu :deep(.el-submenu__title):hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

.sidebar-menu :deep(.el-menu-item):hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
    background-color: #2c3e50 !important;
    transition: background-color 0.3s;
}

/* 增加点击菜单项的过渡效果 */
.menu-item-with-transition {
    position: relative;
    transition: all 0.3s;
}

.menu-item-with-transition:active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(44, 62, 80, 0.7);
    animation: fadeOut 0.5s forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 主内容区域样式 */
.main-container {
    height: 100vh;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
}

.main-header {
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left :deep(.el-breadcrumb) {
    font-size: 16px;
    font-weight: 500;
}

.header-left :deep(.el-breadcrumb__item) {
    color: #606266;
}

.header-left :deep(.el-breadcrumb__inner.is-link) {
    color: #409eff;
    font-weight: 500;
}

.header-left :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
    color: #303133;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.user-info:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.user-avatar {
    margin-right: 8px;
    background-color: #1890ff;
}

.user-name {
    font-size: 14px;
    margin-right: 8px;
    color: #333;
}

.dropdown-icon {
    font-size: 12px;
    color: #666;
    margin-left: 4px;
}

.main-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    height: calc(100vh - 60px);
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
    .sidebar {
        position: fixed;
        z-index: 1000;
        transform: translateX(0);
        transition: transform 0.3s;
    }

    .sidebar.collapsed {
        transform: translateX(-100%);
    }

    .main-container {
        margin-left: 0;
    }

    .user-name {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* 添加新的样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

:deep(.el-form-item__label) {
    font-weight: 500;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: #f5f7fa;
}
</style> 
package org.example.company_management.exception;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.example.company_management.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        logger.error("业务异常：{}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理参数校验异常 (@Valid注解触发)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationExceptions(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        
        String errorMessage = fieldErrors.stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining("; "));
        
        logger.error("参数校验异常: {}", errorMessage);
        
        return Result.error(errorMessage);
    }
    
    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public Result<Void> handleBindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            sb.append(fieldError.getField()).append(": ").append(fieldError.getDefaultMessage()).append(", ");
        }
        String msg = sb.toString();
        if (msg.length() > 2) {
            msg = msg.substring(0, msg.length() - 2);
        }
        logger.error("参数绑定异常：{}", msg);
        return Result.error(400, msg);
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException ex) {
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        
        String errorMessage = violations.stream()
            .map(ConstraintViolation::getMessage)
            .collect(Collectors.joining("; "));
        
        logger.error("约束违反异常: {}", errorMessage);
        
        return Result.error(errorMessage);
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        logger.error("缺少请求参数：{}", e.getMessage());
        return Result.error(400, "缺少请求参数：" + e.getParameterName());
    }
    
    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Result<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        logger.error("JSON解析异常：{}", e.getMessage());
        return Result.error(400, "JSON解析异常，请检查请求参数格式");
    }
    
    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        logger.error("请求方法不支持：{}", e.getMessage());
        return Result.error(405, "请求方法不支持：" + e.getMethod());
    }
    
    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Result<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        logger.error("参数类型不匹配：{}", e.getMessage());
        return Result.error(400, "参数类型不匹配：" + e.getName());
    }
    
    /**
     * 处理数据完整性约束违反异常
     */
    @ExceptionHandler(org.springframework.dao.DataIntegrityViolationException.class)
    public Result<Void> handleDataIntegrityViolationException(org.springframework.dao.DataIntegrityViolationException e) {
        logger.error("数据完整性约束违反：{}", e.getMessage());
        
        String message = e.getMessage();
        // 检查具体的错误类型并提供友好的错误消息
        if (message != null) {
            if (message.contains("Data too long for column 'id_card'")) {
                return Result.error(400, "身份证号码长度超出限制，请检查格式是否正确");
            } else if (message.contains("Data too long")) {
                // 提取字段名
                String columnName = "数据";
                int startIndex = message.indexOf("column '") + 8;
                int endIndex = message.indexOf("'", startIndex);
                if (startIndex > 8 && endIndex > startIndex) {
                    columnName = message.substring(startIndex, endIndex);
                    // 将数据库字段名转换为友好的中文名称
                    switch (columnName) {
                        case "name": columnName = "姓名"; break;
                        case "email": columnName = "邮箱"; break;
                        case "id_card": columnName = "身份证号码"; break;
                        case "department_name": columnName = "部门名称"; break;
                        case "position_name": columnName = "职位名称"; break;
                        case "department_leader": columnName = "部门负责人"; break;
                        case "position_description": columnName = "职位描述"; break;
                        case "department_description": columnName = "部门描述"; break;
                        case "logistics_route": columnName = "物流线路"; break;
                        // 添加其他可能的字段映射
                    }
                }
                return Result.error(400, columnName + "长度超出限制，请减少输入内容");
            } else if (message.contains("Duplicate entry") && message.contains("for key")) {
                if (message.contains("email")) {
                    return Result.error(400, "该邮箱已被注册，请使用其他邮箱");
                } else if (message.contains("id_card")) {
                    return Result.error(400, "该身份证号码已被使用，请检查输入是否正确");
                } else {
                    return Result.error(400, "数据已存在，请勿重复添加");
                }
            } else if (message.contains("foreign key constraint fails")) {
                return Result.error(400, "关联数据不存在，请先添加相关数据");
            }
        }
        
        // 默认错误消息
        return Result.error(400, "数据操作失败，请检查输入内容是否符合要求");
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<Void> handleRuntimeException(RuntimeException e) {
        logger.error("运行时异常", e);
        return Result.error(500, "系统内部错误");
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        logger.error("系统异常", e);
        return Result.error(500, "系统内部错误");
    }
} 
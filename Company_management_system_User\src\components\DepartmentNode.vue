<template>
  <div class="department-node" :style="{ marginLeft: depth * 20 + 'px' }" :class="{ 'virtual-node': department.isVirtual }">
    <div class="node-header">
      <el-icon @click="toggleExpand" class="expand-icon" :class="{ 'is-expanded': isExpanded }">
        <ArrowRightBold v-if="!isExpanded" />
        <ArrowDownBold v-else />
      </el-icon>
      <span class="department-name">{{ department.name }}</span>
      <el-icon v-if="isLoadingChildren" class="is-loading" style="margin-left: 5px;"><Loading /></el-icon>
    </div>

    <el-collapse-transition>
      <div v-show="isExpanded" class="node-content">
        <!-- 仅当不是虚拟节点时显示员工表格 -->
        <div class="employee-section" v-if="!department.isVirtual" v-loading="employeeData?.loading">
          <div class="section-header">
            <h4>员工列表</h4>
            <!-- 员工搜索框 -->
            <div class="search-box">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索员工姓名"
                size="small"
                clearable
                @clear="handleClearSearch"
              >
                <template #append>
                  <el-button @click="handleSearch" :icon="Search"></el-button>
                </template>
              </el-input>
            </div>
          </div>
          
          <!-- 搜索结果提示 -->
          <div v-if="employeeData?.searchKeyword && !employeeData?.loading" class="search-result-info">
            搜索"{{ employeeData.searchKeyword }}"：找到 {{ employeeData.total }} 条结果
            <el-button type="text" @click="handleClearSearch">清除搜索</el-button>
          </div>

          <el-table v-if="employeeData?.employees?.length > 0" :data="employeeData.employees" style="width: 100%; margin-bottom: 10px;" border fit size="small">
            <el-table-column prop="employeeId" label="员工ID" width="80" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" min-width="100" align="center"></el-table-column>
            <el-table-column prop="email" label="邮箱" min-width="180" align="center"></el-table-column>
            <el-table-column label="部门" min-width="120" align="center">
               <template #default="scope">
                 <span>{{ scope.row.departmentName || '未分配' }}</span>
               </template>
            </el-table-column>
            <el-table-column label="职位" min-width="120" align="center">
              <template #default="scope">
                <span>{{ scope.row.positionName || '未分配' }}</span>
              </template>
            </el-table-column>
             <el-table-column label="入职日期" min-width="120" align="center">
              <template #default="scope">
                 <span>{{ formatDate(scope.row.entryDate) }}</span>
               </template>
             </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
               <template #default="scope">
                 <el-tooltip
                   v-if="scope.row.status === 'Inactive'"
                   :content="scope.row.exitDate ? `离职日期: ${formatDate(scope.row.exitDate)}` : '无离职日期记录'"
                   placement="top"
                 >
                   <el-tag
                     :type="scope.row.status === 'Active' ? 'success' : scope.row.status === 'Inactive' ? 'danger' : 'info'"
                     size="small"
                   >
                     {{ scope.row.status === 'Active' ? '在职' : scope.row.status === 'Inactive' ? '离职' : scope.row.status || '-' }}
                   </el-tag>
                 </el-tooltip>
                 <el-tag
                   v-else
                   :type="scope.row.status === 'Active' ? 'success' : scope.row.status === 'Inactive' ? 'danger' : 'info'"
                   size="small"
                 >
                   {{ scope.row.status === 'Active' ? '在职' : scope.row.status === 'Inactive' ? '离职' : scope.row.status || '-' }}
                 </el-tag>
               </template>
             </el-table-column>
             <el-table-column label="物流线路" min-width="120" align="center">
              <template #default="scope">
                 <span>{{ scope.row.logisticsRoute || '-' }}</span>
               </template>
             </el-table-column>
          </el-table>
          <p v-else-if="!employeeData?.loading && employeeData?.employees?.length === 0" style="text-align: center; color: #999; margin: 10px 0;">
            {{ employeeData?.searchKeyword ? '没有找到符合条件的员工' : '暂无员工数据' }}
          </p>

          <div class="pagination-container" v-if="employeeData?.total > 0">
            <el-pagination
              background
              size="small"
              layout="total, sizes, prev, pager, next"
              :page-sizes="[5, 10, 20]"
              :total="employeeData.total"
              :page-size="employeeData.pageSize"
              :current-page="employeeData.currentPage"
              @current-change="handlePageChange(department.id, $event)"
              @size-change="handleSizeChange(department.id, $event)"
            >
            </el-pagination>
          </div>
        </div>

        <!-- 处理虚拟节点中的预加载子部门 -->
        <div v-if="department.subDepartments && department.subDepartments.length > 0" class="sub-departments-section">
          <DepartmentNode
            v-for="subDept in department.subDepartments"
            :key="subDept.id"
            :department="subDept"
            :depth="depth + 1"
          />
        </div>

        <!-- 正常子部门加载逻辑 -->
        <div v-else class="sub-departments-section">
          <h4 v-if="localSubDepartments.length > 0 || isLoadingChildren">子部门</h4>
           <DepartmentNode
             v-for="subDept in localSubDepartments"
             :key="subDept.id"
             :department="subDept"
             :depth="depth + 1"
           />
           <p v-if="!isLoadingChildren && isExpanded && wasSubDeptLoadAttempted && localSubDepartments.length === 0" style="text-align: center; color: #999; margin: 10px 0;">无子部门</p>
        </div>
        
        <!-- 显示兄弟部门（同级部门） -->
        <div v-if="department.siblings && department.siblings.length > 0" class="siblings-section">
          <DepartmentNode
            v-for="siblingDept in department.siblings"
            :key="siblingDept.id"
            :department="siblingDept"
            :depth="depth"
          />
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
import { ref, inject, computed, watch, defineProps, defineAsyncComponent } from 'vue';
import {
  ElTable, ElTableColumn, ElPagination, ElTag, ElIcon, ElCollapseTransition, ElTooltip, 
  ElInput, ElButton // 新增导入的组件
} from 'element-plus';
import { ArrowRightBold, ArrowDownBold, Loading, Search } from '@element-plus/icons-vue'; // 新增 Search 图标

// Need to handle recursive component import
const DepartmentNode = defineAsyncComponent(() => import('./DepartmentNode.vue'));

const props = defineProps({
  department: {
    type: Object,
    required: true
  },
  depth: { // 用于控制缩进
    type: Number,
    default: 0
  }
});

const isExpanded = ref(props.depth === 0); // 根节点默认展开
const localSubDepartments = ref([]);
const isLoadingChildren = ref(false);
const wasSubDeptLoadAttempted = ref(false); // 标记是否尝试加载过子部门
const searchKeyword = ref(''); // 新增：搜索关键词

// Inject shared state and methods from parent
const displayData = inject('displayData');
const fetchEmployees = inject('fetchEmployees');
const searchEmployees = inject('searchEmployees'); // 新增：注入搜索方法
const loadSubDepartmentsForNode = inject('loadSubDepartmentsForNode');
const handlePageChange = inject('handlePageChange');
const handleSizeChange = inject('handleSizeChange');
const formatDate = inject('formatDate'); // 注入日期格式化函数

// Computed property to get employee data for this specific department
const employeeData = computed(() => displayData.value[props.department.id]);

// 搜索员工方法 - 新增
const handleSearch = () => {
  if (!searchEmployees) {
    console.error("searchEmployees function is not available via inject");
    return;
  }
  
  // 调用父组件提供的搜索方法
  searchEmployees(props.department.id, searchKeyword.value.trim());
};

// 清除搜索 - 新增
const handleClearSearch = () => {
  // 重置搜索关键词
  searchKeyword.value = '';
  // 使用空字符串作为关键词调用搜索方法，相当于重置搜索
  searchEmployees(props.department.id, '');
};

// Method to toggle expansion and load data on first expand
const toggleExpand = async () => {
  isExpanded.value = !isExpanded.value;
  if (isExpanded.value) {
    // 仅当不是虚拟节点时加载员工数据
    if (!props.department.isVirtual) {
      // Load employees if not already loaded (or force reload if needed)
      if (!employeeData.value || employeeData.value.employees.length === 0) {
         // Check if fetchEmployees exists before calling
         if (typeof fetchEmployees === 'function') {
             await fetchEmployees(props.department.id, 1); // Load first page
         } else {
             console.error("fetchEmployees function is not available via inject");
         }
      }
    }
    
    // 如果不是预加载的子部门，加载子部门数据
    if (!props.department.subDepartments && localSubDepartments.value.length === 0 && !wasSubDeptLoadAttempted.value) {
        isLoadingChildren.value = true;
        wasSubDeptLoadAttempted.value = true; // Mark attempt
         // Check if loadSubDepartmentsForNode exists before calling
        if (typeof loadSubDepartmentsForNode === 'function') {
            try {
                const children = await loadSubDepartmentsForNode(props.department.id);
                localSubDepartments.value = children || [];
            } catch (error) {
                 console.error(`Failed to load sub-departments for ${props.department.id}:`, error);
                 localSubDepartments.value = []; // Ensure it's an empty array on error
            } finally {
                 isLoadingChildren.value = false;
            }
        } else {
             console.error("loadSubDepartmentsForNode function is not available via inject");
             isLoadingChildren.value = false;
        }
    }
  }
};

// Watch for external changes to employee data if needed, e.g., after edits
// watch(() => displayData.value[props.department.id], (newData) => {
//   // Optional: Handle updates if necessary
// }, { deep: true });

// Ensure employee data is loaded when the component mounts if it's the root node (already expanded)
if (isExpanded.value) {
   // 仅当不是虚拟节点时加载员工数据
   if (!props.department.isVirtual) {
     if (!employeeData.value || employeeData.value.employees.length === 0) {
          if (typeof fetchEmployees === 'function') {
              fetchEmployees(props.department.id, 1);
          } else {
              console.error("fetchEmployees function is not available via inject on mount");
          }
     }
   }
   
   // 如果不是预加载的子部门，加载子部门数据
   if (!props.department.subDepartments && localSubDepartments.value.length === 0 && !wasSubDeptLoadAttempted.value) {
       isLoadingChildren.value = true;
       wasSubDeptLoadAttempted.value = true;
       if (typeof loadSubDepartmentsForNode === 'function') {
            loadSubDepartmentsForNode(props.department.id)
              .then(children => { localSubDepartments.value = children || []; })
              .catch(error => { console.error(`Failed initial load sub-departments for ${props.department.id}:`, error); localSubDepartments.value = [];})
              .finally(() => { isLoadingChildren.value = false; });
       } else {
          console.error("loadSubDepartmentsForNode function is not available via inject on mount");
          isLoadingChildren.value = false;
       }
   }
}

</script>

<style scoped>
.department-node {
  margin-top: 10px;
  border-left: 1px solid #eee;
  padding-left: 10px;
}

.node-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 0;
  transition: background-color 0.2s ease;
}
.node-header:hover {
    background-color: #f5f7fa;
}

.expand-icon {
  margin-right: 8px;
  color: #409EFF;
  transition: transform 0.2s ease-out;
}
.expand-icon.is-expanded {
  transform: rotate(90deg);
}

.department-name {
  font-weight: 500;
  color: #303133;
}

.node-content {
  margin-top: 5px;
  padding-left: 15px; /* Indent content further */
  border-left: 1px dashed #dcdfe6;
}

.employee-section, .sub-departments-section, .siblings-section {
  margin-top: 10px;
  background-color: #fcfcfc; /* Slightly different background for content blocks */
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* 为同级部门添加特殊样式 */
.siblings-section {
  margin-top: 20px;
  border-top: 1px dashed #dcdfe6;
  padding-top: 20px;
}

/* 为虚拟节点(如"我负责的部门")添加特殊样式 */
.virtual-node {
  background-color: #f0f7ff;
  border-radius: 4px;
  padding: 5px;
  border-left-color: #409EFF;
}

h4 {
  margin-bottom: 10px;
  color: #303133;
}

/* 新增：section-header样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
}

/* 新增：搜索框样式 */
.search-box {
  width: 250px;
}

/* 新增：搜索结果信息样式 */
.search-result-info {
  margin-bottom: 10px;
  font-size: 13px;
  color: #606266;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #f4f8ff;
  border-radius: 4px;
}

/* 添加加载图标的旋转动画 */
.is-loading {
  animation: loading-rotate 1s linear infinite;
  color: #909399;
}

@keyframes loading-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style> 
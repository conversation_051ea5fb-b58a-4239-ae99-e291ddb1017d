package org.example.company_management.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.example.company_management.dto.SalaryImportDTO;
import org.example.company_management.entity.Employee;
import org.example.company_management.entity.Salary;
import org.example.company_management.mapper.EmployeeMapper;
import org.example.company_management.mapper.SalaryMapper;
import org.example.company_management.service.SalaryService;
import org.example.company_management.utils.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Pattern;
import java.util.Set;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;

/**
 * 工资服务实现类
 */
@Service
public class SalaryServiceImpl implements SalaryService {

    private static final Pattern YEAR_MONTH_PATTERN = Pattern.compile("^\\d{4}-(0[1-9]|1[0-2])$");
    private static final Logger log = LoggerFactory.getLogger(SalaryServiceImpl.class);

    @Autowired
    private SalaryMapper salaryMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private ExcelUtil excelUtil;
    @Autowired
    private Validator validator;

    @Override
    public List<Salary> getAllSalaries() {
        return salaryMapper.selectAll();
    }

    @Override
    public List<Salary> getSalariesByEmployeeId(Integer employeeId) {
        return salaryMapper.selectByEmployeeId(employeeId);
    }

    @Override
    public Salary getSalaryByEmployeeIdAndDate(Integer employeeId, String date) {
        return salaryMapper.selectByEmployeeIdAndDate(employeeId, date);
    }

    @Override
    public PageInfo<Salary> getSalaryPage(int pageNum, int pageSize, String employeeName, Integer departmentId, String yearMonth) {
        // 设置分页参数
        PageHelper.startPage(pageNum, pageSize);

        // 执行查询
        List<Salary> salaries = salaryMapper.selectPage(employeeName, departmentId, yearMonth);

        // 使用PageInfo的简易构造方法
        PageInfo<Salary> pageInfo = new PageInfo<>(salaries, 0); // 第二个参数0表示不计算导航页码

        return pageInfo;
    }

    @Override
    public PageInfo<Salary> getSalaryPageByEmployeeId(Integer pageNum, Integer pageSize, Integer employeeId, String date) {
        // 设置分页参数
        PageHelper.startPage(pageNum, pageSize);

        // 准备查询条件 - 如果有日期参数，根据日期筛选
        List<Salary> salaries;
        if (date != null && !date.isEmpty()) {
            // 如果有日期参数，直接查询特定年月的记录
            Salary specificSalary = salaryMapper.selectByEmployeeIdAndDate(employeeId, date);
            if (specificSalary != null) {
                salaries = List.of(specificSalary);
            } else {
                salaries = List.of();
            }
        } else {
            // 没有日期参数，查询所有记录
            salaries = salaryMapper.selectByEmployeeId(employeeId);
        }

        // 使用PageInfo的简易构造方法
        PageInfo<Salary> pageInfo = new PageInfo<>(salaries, 0); // 第二个参数0表示不计算导航页码

        return pageInfo;
    }

    @Override
    public Salary getSalaryById(Integer id) {
        return salaryMapper.selectById(id);
    }

    @Override
    @Transactional
    public Salary addSalary(Salary salary) {
        salaryMapper.insert(salary);
        return salary;
    }

    @Override
    @Transactional
    public Salary updateSalary(Salary salary) {
        salaryMapper.update(salary);
        return salary;
    }

    @Override
    @Transactional
    public boolean deleteSalary(Integer id) {
        return salaryMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean batchDeleteSalary(List<Integer> ids) {
        return salaryMapper.batchDelete(ids) > 0;
    }

    @Override
    public PageInfo<Salary> getDepartmentMultiSalaryPage(
            List<Integer> departmentIds,
            Integer pageNum,
            Integer pageSize,
            String startDate,
            String endDate,
            String employeeName
    ) {
        // 检查部门ID列表是否为空
        if (departmentIds == null || departmentIds.isEmpty()) {
            return new PageInfo<>(List.of());
        }

        // 设置分页参数
        PageHelper.startPage(pageNum, pageSize);

        // 处理日期参数
        String dateParam = null;
        if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
            dateParam = "range_" + startDate + "_" + endDate;
        } else if (startDate != null && !startDate.isEmpty()) {
            dateParam = startDate;
        } else if (endDate != null && !endDate.isEmpty()) {
            dateParam = endDate;
        }

        // 执行多部门查询，获取员工工资详情数据，并传递employeeName
        List<Salary> salaries = salaryMapper.selectMultiDepartmentSalary(departmentIds, dateParam, employeeName);

        // 构建分页结果（第二个参数0表示不计算导航页码，提高性能）
        PageInfo<Salary> pageInfo = new PageInfo<>(salaries, 0);

        return pageInfo;
    }

    @Override
    @Transactional
    public ExcelUtil.ImportResult<SalaryImportDTO> importSalariesFromExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            ExcelUtil.ImportResult<SalaryImportDTO> emptyFileResult = new ExcelUtil.ImportResult<>();
            emptyFileResult.addGeneralError("上传的文件不能为空。");
            return emptyFileResult;
        }

        ExcelUtil.RowProcessor<SalaryImportDTO> salaryRowProcessor = (rowIndex, dto) -> {
            // 1. JSR 303/380 Bean Validation
            Set<ConstraintViolation<SalaryImportDTO>> violations = validator.validate(dto);
            if (!violations.isEmpty()) {
                StringBuilder errorMessages = new StringBuilder();
                for (ConstraintViolation<SalaryImportDTO> violation : violations) {
                    errorMessages.append(violation.getMessage()).append("; ");
                }
                return "行 " + rowIndex + ": " + errorMessages.toString().trim();
            }

            Employee employee = employeeMapper.selectByIdCard(dto.getIdCard());

            if (employee == null) {
                return "行 " + rowIndex + ": 根据身份证号码 '" + dto.getIdCard() + "' 未找到对应员工";
            }

            Salary existingSalary = salaryMapper.selectByEmployeeIdAndDate(employee.getEmployeeId(), dto.getYearMonth());
            if (existingSalary != null) {
                return "行 " + rowIndex + ": 员工 '" + employee.getName() + "' 在 " + dto.getYearMonth() + " 的工资记录已存在，已跳过";
            }

            Salary newSalary = new Salary();
            newSalary.setEmployeeId(employee.getEmployeeId());
            newSalary.setDate(dto.getYearMonth());
            newSalary.setBasicSalary(dto.getBasicSalary() != null ? dto.getBasicSalary() : BigDecimal.ZERO);
            newSalary.setPerformanceBonus(dto.getPerformanceBonus() != null ? dto.getPerformanceBonus() : BigDecimal.ZERO);
            newSalary.setFullAttendanceBonus(dto.getFullAttendanceBonus() != null ? dto.getFullAttendanceBonus() : BigDecimal.ZERO);
            newSalary.setBusinessOperationBonus(dto.getBusinessOperationBonus() != null ? dto.getBusinessOperationBonus() : BigDecimal.ZERO);
            newSalary.setReimbursement(dto.getReimbursement() != null ? dto.getReimbursement() : BigDecimal.ZERO);
            newSalary.setPrivateAccount(dto.getPrivateAccount() != null ? dto.getPrivateAccount() : BigDecimal.ZERO);
            newSalary.setLeaveDeduction(dto.getLeaveDeduction() != null ? dto.getLeaveDeduction() : BigDecimal.ZERO);
            newSalary.setDeduction(dto.getDeduction() != null ? dto.getDeduction() : BigDecimal.ZERO);
            newSalary.setLateDeduction(dto.getLateDeduction() != null ? dto.getLateDeduction() : BigDecimal.ZERO);
            newSalary.setSocialSecurityPersonal(dto.getSocialSecurityPersonal() != null ? dto.getSocialSecurityPersonal() : BigDecimal.ZERO);
            newSalary.setProvidentFund(dto.getProvidentFund() != null ? dto.getProvidentFund() : BigDecimal.ZERO);
            newSalary.setTax(dto.getTax() != null ? dto.getTax() : BigDecimal.ZERO);
            newSalary.setWaterElectricityFee(dto.getWaterElectricityFee() != null ? dto.getWaterElectricityFee() : BigDecimal.ZERO);
            
            // Directly set total/sum fields from DTO as per new requirement
            newSalary.setSumSalary(dto.getSumSalary() != null ? dto.getSumSalary() : BigDecimal.ZERO);
            newSalary.setActualSalary(dto.getActualSalary() != null ? dto.getActualSalary() : BigDecimal.ZERO);
            newSalary.setTotalSalary(dto.getTotalSalary() != null ? dto.getTotalSalary() : BigDecimal.ZERO);
            
            newSalary.setRemark(StringUtils.isNotBlank(dto.getRemark()) ? dto.getRemark() : "");
            
            try {
                int inserted = salaryMapper.insert(newSalary);
                if (inserted > 0) {
                    return null; // 成功导入此行
                } else {
                    log.warn("行 {}: 数据保存失败，数据库未插入记录，员工ID: {}, 年月: {}", rowIndex, employee.getEmployeeId(), dto.getYearMonth());
                    return "行 " + rowIndex + ": 数据保存失败，数据库未插入记录";
                }
            } catch (Exception e) {
                log.error("行 {}: 保存到数据库时发生错误, 员工ID: {}, 年月: {}", rowIndex, employee.getEmployeeId(), dto.getYearMonth(), e);
                return "行 " + rowIndex + ": 保存到数据库时发生错误: " + e.getMessage();
            }
        };

        try {
            // Assuming 3-parameter version of readExcel with RowProcessor
            return excelUtil.readExcel(file.getInputStream(), SalaryImportDTO.class, salaryRowProcessor);
        } catch (Exception e) {
            log.error("导入工资Excel时发生严重错误", e);
            ExcelUtil.ImportResult<SalaryImportDTO> exceptionResult = new ExcelUtil.ImportResult<>();
            exceptionResult.addGeneralError("处理Excel文件过程中发生意外错误: " + e.getMessage());
            return exceptionResult;
        }
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.PettyCashMapper">
    
    <resultMap id="PettyCashMap" type="org.example.company_management.entity.PettyCash">
        <id property="id" column="id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="purpose" column="purpose"/>
        <result property="amount" column="amount"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 非数据库字段 -->
        <result property="employeeName" column="employee_name"/>
        <result property="department" column="department_name"/>
        <result property="position" column="position_name"/>
        <result property="departmentId" column="department_id"/>
        <result property="date" column="date"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        pc.id, pc.employee_id, pc.date, pc.purpose, pc.amount, pc.status, pc.create_time, pc.update_time
    </sql>
    
    <sql id="Join_Column_List">
        pc.id, pc.employee_id, pc.date, pc.purpose, pc.amount, pc.status, pc.create_time, pc.update_time,
        e.name as employee_name, d.department_name, p.position_name, e.department_id
    </sql>
    
    <!-- 分页查询备用金记录 -->
    <select id="selectByPage" parameterType="java.util.Map" resultMap="PettyCashMap">
        SELECT
        <include refid="Join_Column_List"/>
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        <where>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="status != null and status != ''">
                AND pc.status = #{status}
            </if>
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
        ORDER BY pc.create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    
    <!-- 查询备用金记录总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        <where>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="status != null and status != ''">
                AND pc.status = #{status}
            </if>
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
    </select>
    
    <!-- 根据ID查询备用金记录 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="PettyCashMap">
        SELECT
        <include refid="Join_Column_List"/>
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE pc.id = #{id}
    </select>
    
    <!-- 添加备用金记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.PettyCash" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO petty_cash (employee_id, date, purpose, amount, status, create_time, update_time)
        VALUES (#{employeeId}, #{date}, #{purpose}, #{amount}, #{status}, NOW(), NOW())
    </insert>
    
    <!-- 更新备用金记录 -->
    <update id="update" parameterType="org.example.company_management.entity.PettyCash">
        UPDATE petty_cash
        <set>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="date != null and date != ''">date = #{date},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 删除备用金记录 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        UPDATE petty_cash 
        SET status = '已删除',
            update_time = NOW()
        WHERE id = #{id}
    </delete>
    
    <!-- 批量删除备用金记录 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        UPDATE petty_cash
        SET status = '已删除',
            update_time = NOW()
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 审核备用金申请 -->
    <update id="approve" parameterType="java.util.Map">
        UPDATE petty_cash
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <!-- 获取待审批的备用金列表 -->
    <select id="selectPending" resultMap="PettyCashMap">
        SELECT
        <include refid="Join_Column_List"/>
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        <where>
            pc.status = '审核中'
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
        ORDER BY pc.create_time DESC
    </select>
    
    <!-- 获取待审批的备用金数量 -->
    <select id="countPending" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM petty_cash pc
        <where>
            pc.status = '审核中'
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
    </select>
    
    <!-- 根据员工ID分页查询备用金记录 -->
    <select id="selectByEmployeeId" parameterType="java.util.Map" resultMap="PettyCashMap">
        SELECT
        <include refid="Join_Column_List"/>
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        <where>
            pc.employee_id = #{employeeId}
            <if test="purpose != null and purpose != ''">
                AND pc.purpose LIKE CONCAT('%', #{purpose}, '%')
            </if>
            <if test="status != null and status != ''">
                AND pc.status = #{status}
            </if>
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
        ORDER BY pc.create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    
    <!-- 查询指定员工的备用金记录总数 -->
    <select id="countTotalByEmployeeId" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM petty_cash pc
        <where>
            pc.employee_id = #{employeeId}
            <if test="purpose != null and purpose != ''">
                AND pc.purpose LIKE CONCAT('%', #{purpose}, '%')
            </if>
            <if test="status != null and status != ''">
                AND pc.status = #{status}
            </if>
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
    </select>
    
    <!-- 提交备用金申请到审核状态 -->
    <update id="submitToApproval" parameterType="java.lang.Integer">
        UPDATE petty_cash
        SET status = '审核中',
            update_time = NOW()
        WHERE id = #{id}
        AND (status = '待审核' OR status = '已取消')
    </update>
    
    <!-- 取消备用金申请 -->
    <update id="cancelPettyCash" parameterType="java.lang.Integer">
        UPDATE petty_cash
        SET status = '已取消',
            update_time = NOW()
        WHERE id = #{id}
        AND (status = '待审核' OR status = '审核中')
    </update>
    
    <!-- 根据部门ID列表分页查询备用金记录 -->
    <select id="selectByDepartmentIds" parameterType="java.util.Map" resultMap="PettyCashMap">
        SELECT
        <include refid="Join_Column_List"/>
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        <where>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND e.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="purpose != null and purpose != ''">
                AND pc.purpose LIKE CONCAT('%', #{purpose}, '%')
            </if>
            <if test="status != null and status != ''">
                AND pc.status = #{status}
            </if>
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
        ORDER BY pc.create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    
    <!-- 根据部门ID列表查询备用金记录总数 -->
    <select id="countTotalByDepartmentIds" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT pc.id) <!-- Use DISTINCT pc.id to avoid issues with JOINs if one employee has multiple entries in other joined tables not directly related to filtering criteria -->
        FROM petty_cash pc
        LEFT JOIN employee e ON pc.employee_id = e.employee_id
        <where>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND e.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="purpose != null and purpose != ''">
                AND pc.purpose LIKE CONCAT('%', #{purpose}, '%')
            </if>
            <if test="status != null and status != ''">
                AND pc.status = #{status}
            </if>
            <if test="date != null and date != ''">
                AND pc.date = #{date}
            </if>
            AND pc.status != '已删除'
        </where>
    </select>

    <select id="sumApprovedAmountByEmployeeIdAndDate" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM petty_cash
        WHERE employee_id = #{employeeId}
          AND date = #{date}
          AND status != '已拒绝'
    </select>

</mapper> 
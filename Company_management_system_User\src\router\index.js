import { createRouter, createWebHistory } from 'vue-router'

const routes = [
	{
		path: '/',
		redirect: '/login',
	},
	{
		path: '/login',
		name: 'Login',
		component: () => import('../views/Login.vue'),
		meta: { title: '员工登录' },
	},
	{
		path: '/dashboard',
		name: 'Dashboard',
		component: () => import('../views/Dashboard.vue'),
		redirect: '/dashboard/home',
		meta: { requiresAuth: true, title: '主页' },
		children: [
			{
				path: 'home',
				name: 'Home',
				component: () => import('../views/Home.vue'),
				meta: { requiresAuth: true, title: '首页' },
			},
			{
				path: 'client',
				name: 'Client',
				component: () => import('../views/Client.vue'),
				meta: { requiresAuth: true, title: '客户管理' },
			},
			{
				path: 'petty-cash',
				name: 'PettyCash',
				component: () => import('../views/PettyCash.vue'),
				meta: { requiresAuth: true, title: '备用金管理' },
			},
			{
				path: 'performance',
				name: 'Performance',
				component: () => import('../views/Performance.vue'),
				meta: { requiresAuth: true, title: '业绩查询' },
			},
			{
				path: 'salary',
				name: 'Salary',
				component: () => import('../views/Salary.vue'),
				meta: { requiresAuth: true, title: '工资查询' },
			},
			{
				path: 'department-employees',
				name: 'DepartmentEmployees',
				component: () => import('../views/DepartmentEmployeeList.vue'),
				meta: { requiresAuth: true, title: '部门员工列表' }
			},
			{
				path: 'department-performance',
				name: 'DepartmentPerformance',
				component: () => import('../views/DepartmentPerformance.vue'),
				meta: { requiresAuth: true, title: '部门业绩' }
			},
			{
				path: 'department-salary',
				name: 'DepartmentSalary',
				component: () => import('../views/DepartmentSalary.vue'),
				meta: { requiresAuth: true, title: '部门工资' }
			},
			{
				path: 'department-clients',
				name: 'DepartmentClients',
				component: () => import('../views/DepartmentClient.vue'),
				meta: { requiresAuth: true, title: '部门客户' }
			},
			{
				path: 'department-petty-cash',
				name: 'DepartmentPettyCash',
				component: () => import('../views/DepartmentPettyCash.vue'),
				meta: { requiresAuth: true, title: '部门备用金' }
			},
			{
				path: 'department-expense',
				name: 'DepartmentExpense',
				component: () => import('../views/DepartmentExpense.vue'),
				meta: { requiresAuth: true, title: '部门开销' }
			},
			{
				path: 'employee-expense',
				name: 'EmployeeExpense',
				component: () => import('../views/EmployeeExpense.vue'),
				meta: { requiresAuth: true, title: '员工费用' }
			},
			// {{CHENGQI: 销售日报功能路由}}
			// {{CHENGQI: 任务ID: P3-LD-011}}
			// {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
			{
				path: 'sales-report',
				name: 'SalesReport',
				component: () => import('../views/SalesReportBasic.vue'),
				meta: { requiresAuth: true, title: '销售日报' }
			},
			{
				path: 'department-reports',
				name: 'DepartmentReports',
				component: () => import('../views/DepartmentReportsBasic.vue'),
				meta: { requiresAuth: true, title: '部门日报' }
			}
		],
	},
	{
		path: '/:pathMatch(.*)*',
		redirect: '/dashboard',
	},
]

const router = createRouter({
	history: createWebHistory(),
	routes,
})

// 全局前置守卫，处理权限和标题
router.beforeEach((to, from, next) => {
	// 设置页面标题
	document.title = to.meta.title
		? `${to.meta.title} - 员工信息系统`
		: '员工信息系统'

	// 处理权限验证
	if (to.meta.requiresAuth) {
		const token = localStorage.getItem('token')

		if (!token) {
			// 未登录则跳转到登录页
			next({
				path: '/login',
				query: { redirect: to.fullPath },
			})
		} else {
			next()
		}
	} else {
		next()
	}
})

export default router

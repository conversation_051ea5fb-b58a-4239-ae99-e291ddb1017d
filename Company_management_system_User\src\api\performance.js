import request from './request'

/**
 * 获取当前员工业绩列表
 * @param {Object} params - 查询参数，可选的日期筛选参数（年月格式：YYYY-MM）
 * @returns {Promise}
 */
export function getEmployeePerformances(params) {
	return request({
		url: '/performance/employee/list',
		method: 'get',
		params,
	})
}

/**
 * 获取特定月份的业绩详情
 * @param {String} date - 年月（格式：YYYY-MM）
 * @returns {Promise}
 */
export function getPerformanceDetail(date) {
	return request({
		url: `/performance/employee/detail/${date}`,
		method: 'get',
	})
}


/**
 * 获取多个部门业绩列表
 * @param {Object} params - 查询参数
 * @param {Array} params.departmentIds - 部门ID数组
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页大小
 * @param {String} params.startDate - 开始年月，格式为yyyy-MM（可选）
 * @param {String} params.endDate - 结束年月，格式为yyyy-MM（可选）
 * @returns {Promise}
 */
export function getDepartmentMultiPerformances(params) {
	return request({
		url: '/performance/departments/list',
		method: 'post',
		data: params,
	})
}

/**
 * 获取部门业绩统计数据
 * @param {Object} params - 查询参数
 * @param {Array} params.departmentIds - 部门ID数组
 * @param {String} params.startDate - 开始年月，格式为yyyy-MM（可选）
 * @param {String} params.endDate - 结束年月，格式为yyyy-MM（可选）
 * @returns {Promise}
 */
export function getDepartmentPerformanceStats(params) {
	return request({
		url: '/performance/departments/stats',
		method: 'post',
		data: params,
	})
}


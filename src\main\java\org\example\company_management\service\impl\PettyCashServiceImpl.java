package org.example.company_management.service.impl;

import org.example.company_management.entity.PettyCash;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.PettyCashMapper;
import org.example.company_management.service.PettyCashService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PettyCashServiceImpl implements PettyCashService {

    @Autowired
    private PettyCashMapper pettyCashMapper;

    @Override
    public PageResult<PettyCash> getPettyCashByPage(Map<String, Object> params) {
        // 计算分页参数
        Integer page = (Integer) params.get("page");
        Integer size = (Integer) params.get("size");
        if (page != null && size != null) {
            params.put("offset", (page - 1) * size);
            params.put("limit", size);
        }

        // 查询数据
        List<PettyCash> list = pettyCashMapper.selectByPage(params);
        int total = pettyCashMapper.countTotal(params);

        // 返回分页结果
        PageResult<PettyCash> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        return pageResult;
    }

    @Override
    public PettyCash getPettyCashById(Integer id) {
        return pettyCashMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addPettyCash(PettyCash pettyCash) {
        // 设置初始状态为待审核
        if (pettyCash.getStatus() == null) {
            pettyCash.setStatus("待审核");
        }

        // 添加年月格式校验
        if (pettyCash.getDate() == null || !pettyCash.getDate().matches("^\\d{4}-(0[1-9]|1[0-2])$")) {
            throw new BusinessException("年月格式不正确，应为 YYYY-MM");
        }

        // 如果没有设置员工ID，则设置为当前登录用户
        if (pettyCash.getEmployeeId() == null) {
            // 从ThreadLocal中获取当前登录用户信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
                throw new RuntimeException("用户未登录或登录信息已失效");
            }
            pettyCash.setEmployeeId((Integer) employeeInfo.get("employeeId"));
        }

        pettyCashMapper.insert(pettyCash);
        return pettyCash.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePettyCash(PettyCash pettyCash) {
        return pettyCashMapper.update(pettyCash) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePettyCash(Integer id) {
        return pettyCashMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeletePettyCash(List<Integer> ids) {
        return pettyCashMapper.batchDeleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approvePettyCash(Integer id, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("status", status);
        return pettyCashMapper.approve(params) > 0;
    }

    @Override
    public List<PettyCash> getPendingPettyCash() {
        return pettyCashMapper.selectPending();
    }

    @Override
    public int getPendingPettyCashCount() {
        return pettyCashMapper.countPending();
    }

    @Override
    public PageResult<PettyCash> getMyPettyCashByPage(Map<String, Object> params) {
        // 获取当前登录用户ID
        // 从ThreadLocal中获取当前登录用户信息
        Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
        if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
            throw new RuntimeException("用户未登录或登录信息已失效");
        }
        Integer employeeId = (Integer) employeeInfo.get("employeeId");

        // 设置员工ID到查询参数
        params.put("employeeId", employeeId);

        // 计算分页参数
        Integer page = (Integer) params.get("page");
        Integer size = (Integer) params.get("size");
        if (page != null && size != null) {
            params.put("offset", (page - 1) * size);
            params.put("limit", size);
        }

        // 查询数据
        List<PettyCash> list = pettyCashMapper.selectByEmployeeId(params);
        int total = pettyCashMapper.countTotalByEmployeeId(params);

        // 返回分页结果
        PageResult<PettyCash> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMyPettyCash(Integer id, PettyCash pettyCash) {
        // 获取当前登录用户ID
        // 从ThreadLocal中获取当前登录用户信息
        Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
        if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
            throw new RuntimeException("用户未登录或登录信息已失效");
        }
        Integer employeeId = (Integer) employeeInfo.get("employeeId");

        // 查询备用金记录
        PettyCash existingPettyCash = pettyCashMapper.selectById(id);

        // 验证：1. 记录存在 2. 属于当前用户 3. 状态为"待审核"或"已取消"
        if (existingPettyCash == null ||
                !existingPettyCash.getEmployeeId().equals(employeeId) ||
                (!("待审核".equals(existingPettyCash.getStatus())) && !("已取消".equals(existingPettyCash.getStatus())))) {
            return false;
        }

        // 添加年月格式校验 (如果允许修改)
        if (pettyCash.getDate() != null && !pettyCash.getDate().matches("^\\d{4}-(0[1-9]|1[0-2])$")) {
            throw new BusinessException("年月格式不正确，应为 YYYY-MM");
        }

        // 设置ID和员工ID（确保不会被修改）
        pettyCash.setId(id);
        pettyCash.setEmployeeId(employeeId);
        // 保持状态不变，对于已取消的记录，修改后状态应该变为待审核
        if ("已取消".equals(existingPettyCash.getStatus())) {
            pettyCash.setStatus("待审核");
        } else {
            pettyCash.setStatus("待审核");
        }

        return pettyCashMapper.update(pettyCash) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitToApproval(Integer id) {
        // 获取当前登录用户ID
        // 从ThreadLocal中获取当前登录用户信息
        Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
        if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
            throw new RuntimeException("用户未登录或登录信息已失效");
        }
        Integer employeeId = (Integer) employeeInfo.get("employeeId");

        // 查询备用金记录
        PettyCash existingPettyCash = pettyCashMapper.selectById(id);

        // 验证：1. 记录存在 2. 属于当前用户 3. 状态为"待审核"或"已取消"
        if (existingPettyCash == null ||
                !existingPettyCash.getEmployeeId().equals(employeeId) ||
                (!("待审核".equals(existingPettyCash.getStatus())) && !("已取消".equals(existingPettyCash.getStatus())))) {
            return false;
        }

        return pettyCashMapper.submitToApproval(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelPettyCash(Integer id) {
        // 获取当前登录用户ID
        // 从ThreadLocal中获取当前登录用户信息
        Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
        if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
            throw new RuntimeException("用户未登录或登录信息已失效");
        }
        Integer employeeId = (Integer) employeeInfo.get("employeeId");

        // 查询备用金记录
        PettyCash existingPettyCash = pettyCashMapper.selectById(id);

        // 验证：1. 记录存在 2. 属于当前用户 3. 状态为"待审核"或"审核中"
        if (existingPettyCash == null ||
                !existingPettyCash.getEmployeeId().equals(employeeId) ||
                (!("待审核".equals(existingPettyCash.getStatus())) && !("审核中".equals(existingPettyCash.getStatus())))) {
            return false;
        }

        return pettyCashMapper.cancelPettyCash(id) > 0;
    }

    @Override
    public PageResult<PettyCash> getPettyCashByDepartmentIds(Map<String, Object> params) {
        List<Integer> departmentIds = (List<Integer>) params.get("departmentIds");
        Integer page = (Integer) params.get("page");
        Integer size = (Integer) params.get("size");

        PageResult<PettyCash> pageResult = new PageResult<>();

        // 如果部门ID列表为空，则直接返回空结果
        if (departmentIds == null || departmentIds.isEmpty()) {
            pageResult.setList(new java.util.ArrayList<>());
            pageResult.setTotal(0);
            pageResult.setPageNum(page != null ? page : 1);
            pageResult.setPageSize(size != null ? size : 10);
            return pageResult;
        }

        // 计算分页参数
        if (page != null && size != null) {
            params.put("offset", (page - 1) * size);
            params.put("limit", size);
        } else {
            // Default pagination if not provided, though frontend should always provide them
            params.put("offset", 0);
            params.put("limit", 10);
            page = 1;
            size = 10;
        }

        List<PettyCash> list = pettyCashMapper.selectByDepartmentIds(params);
        int total = pettyCashMapper.countTotalByDepartmentIds(params);

        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum(page);
        pageResult.setPageSize(size);

        return pageResult;
    }
} 
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { useAuthStore } from '@/stores/token'

// 创建axios实例
const service = axios.create({
	baseURL: '/api', // 自动拼接/api前缀
	timeout: 10000, // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
	(config) => {
		// 从localStorage获取token
		const token = localStorage.getItem('token')

		if (token) {
			// 使用标准的Bearer token格式
			config.headers['Authorization'] = `Bearer ${token}`
		}

		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

// 响应拦截器
service.interceptors.response.use(
	(response) => {
		const res = response.data

		// 如果返回的状态码不是200，说明接口有问题，把错误信息显示出来
		if (res.code !== 200) {
			// 401: 未登录或token过期
			if (res.code === 401) {
				const authStore = useAuthStore()
				authStore.logout()
				ElMessage({
					message: res.message || '登录已过期，请重新登录',
					type: 'warning',
					duration: 3000,
				})
				router.push('/login')
				return Promise.reject(new Error(res.message || '登录已过期，请重新登录'))
			}

			ElMessage({
				message: res.message || '系统错误',
				type: 'error',
				duration: 5 * 1000,
			})
			return Promise.reject(new Error(res.message || '系统错误'))
		} else {
			return res
		}
	},
	(error) => {
		const authStore = useAuthStore()
		// 处理401错误
		if (error.response && error.response.status === 401) {
			authStore.logout()
			ElMessage({
				message: '登录已过期，请重新登录',
				type: 'warning',
				duration: 3000,
			})
			router.push('/login')
			return Promise.reject(new Error(error.response.data.message || '登录已过期，请重新登录'))
		}

		// 显示更详细的错误信息
		let errorMessage = '系统错误'
		if (error.response) {
			// 服务器返回了错误状态码
			errorMessage = `请求失败 (${error.response.status}): ${
				error.response.data?.message || '未知错误'
			}`
		} else if (error.request) {
			// 请求已发出，但没有收到响应
			errorMessage = '无法连接到服务器，请检查网络或稍后重试'
			authStore.logout()
			router.push('/login')
		} else {
			// 请求配置出错
			errorMessage = `请求错误: ${error.message}`
		}

		ElMessage({
			message: errorMessage,
			type: 'error',
			duration: 5 * 1000,
		})
		return Promise.reject(error)
	}
)

export default service

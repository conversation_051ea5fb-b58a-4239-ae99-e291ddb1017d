package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Salary;

import java.util.List;
import java.util.Optional;

/**
 * 工资Mapper接口
 */
@Mapper
public interface SalaryMapper {
    /**
     * 查询所有工资记录
     *
     * @return 工资记录列表
     */
    List<Salary> selectAll();

    /**
     * 根据员工ID查询工资记录
     *
     * @param employeeId 员工ID
     * @return 工资记录列表
     */
    List<Salary> selectByEmployeeId(Integer employeeId);

    /**
     * 根据员工ID和日期查询工资记录
     *
     * @param employeeId 员工ID
     * @param date       日期（年月）
     * @return 工资记录
     */
    Salary selectByEmployeeIdAndDate(Integer employeeId, String date);

    /**
     * 分页查询工资记录
     *
     * @param employeeName 员工姓名
     * @param departmentId 部门ID
     * @param yearMonth    年月
     * @return 工资记录列表
     */
    List<Salary> selectPage(@Param("employeeName") String employeeName,
                            @Param("departmentId") Integer departmentId,
                            @Param("yearMonth") String yearMonth);

    /**
     * 根据ID查询工资记录
     *
     * @param id 工资记录ID
     * @return 工资记录
     */
    Salary selectById(Integer id);

    /**
     * 新增工资记录
     *
     * @param salary 工资记录
     * @return 影响行数
     */
    int insert(Salary salary);

    /**
     * 修改工资记录
     *
     * @param salary 工资记录
     * @return 影响行数
     */
    int update(Salary salary);

    /**
     * 删除工资记录
     *
     * @param id 工资记录ID
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 批量删除工资记录
     *
     * @param ids 工资记录ID列表
     * @return 影响行数
     */
    int batchDelete(List<Integer> ids);

    /**
     * 查询多个部门工资统计
     *
     * @param departmentIds 部门ID列表
     * @param date          日期参数，支持两种格式：
     *                      1. 单一日期格式：YYYY-MM
     *                      2. 日期范围格式：range_YYYY-MM_YYYY-MM（例如：range_2023-01_2023-12）
     *                      当使用日期范围格式时，将查询指定日期范围内的所有记录
     * @return 部门工资统计列表
     */
    List<Salary> selectMultiDepartmentSalary(
            @Param("departmentIds") List<Integer> departmentIds,
            @Param("date") String date,
            @Param("employeeName") String employeeName
    );

    /**
     * 查询部门员工工资详情
     *
     * @param departmentId 部门ID
     * @param date         日期（年月）
     * @return 员工工资详情列表
     */
    List<Salary> selectEmployeeSalariesByDepartment(
            @Param("departmentId") Integer departmentId,
            @Param("date") String date
    );

    /**
     * 批量新增工资记录
     * @param salaryList 工资记录列表
     * @return 影响行数
     */
    int batchInsertSalaries(@Param("salaryList") List<Salary> salaryList);

    /**
     * 根据员工ID和日期查询薪资记录。
     *
     * @param employeeId 员工ID
     * @param date       日期 (格式 YYYY-MM)
     * @return 薪资记录，如果找不到则返回null
     */
    Optional<Salary> findByEmployeeIdAndDate(@Param("employeeId") Integer employeeId, @Param("date") String date);
} 
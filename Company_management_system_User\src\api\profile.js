import request from './request'

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
	return request({
		url: '/employee/info',
		method: 'get',
	})
}

/**
 * 更新用户个人信息
 * @param {Object} data - 用户信息对象
 * @returns {Promise}
 */
export function updateUserProfile(data) {
	return request({
		url: '/employee/profile',
		method: 'put',
		data,
	})
}

/**
 * 修改密码
 * @param {Object} data - 包含旧密码和新密码的对象
 * @returns {Promise}
 */
export function changePassword(data) {
	return request({
		url: '/employee/password',
		method: 'put',
		data,
	})
}

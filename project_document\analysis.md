# 销售日报功能需求分析

**文档创建时间**: 2025-06-04 10:43:32 +08:00  
**分析负责人**: PDM, AR, LD  
**文档状态**: 初始版本

## 核心业务需求

### 功能概述
销售日报是一个面向销售人员的日常工作记录系统，用于跟踪客户开发进度、工作效果和个人表现。

### 关键字段需求

#### 1. 统计类字段（系统自动计算）
- **年度新客户总数**: 从client表查询当年新增且由当前员工负责的客户数量
- **当月新客户总数**: 从client表查询当月新增且由当前员工负责的客户数量  
- **距离上次出新客户天数**: 系统计算，基于client表中最近一次新客户的创建时间

#### 2. 客户选择字段（下拉多选）
- **询价客户**: 从当前员工负责的客户中多选
- **出货客户**: 从当前员工负责的客户中多选
- **重点开发客户**: 从当前员工负责的客户中多选

#### 3. 评估字段
- **责任心**: 枚举选择（优秀、中等（等待主管约谈）、差（等待处罚））

#### 4. 工作检查清单
- **下班准备工作**: 多选项目
  - 整理完桌面
  - 整理完50通预计电话邮件上级
  - 会议已开完
  - 准备好明天工作资料
  - 问候领导后打卡离开

#### 5. 文本输入字段
- **今日效果**: 多行文本输入框
- **会议报告**: 多行文本输入框
- **工作日记**: 多行文本输入框（总结和遇到的问题）

## 权限控制需求

### 角色权限矩阵

| 角色 | 权限范围 | 具体权限 |
|------|----------|----------|
| employee | 个人数据 | 只能查看和提交自己的销售日报 |
| manager | 部门数据 | 可查看所负责部门下所有员工的销售日报 |
| admin | 全局数据 | 可查看所有员工的销售日报 |

### 数据过滤规则
- **员工级别**: WHERE employee_id = 当前登录用户ID
- **部门负责人级别**: WHERE employee_id IN (本部门员工ID列表)
- **管理员级别**: 无过滤条件

## 现有系统集成点

### 数据依赖关系
1. **员工信息**: 依赖employee表获取当前用户信息
2. **客户数据**: 依赖client表获取员工负责的客户列表
3. **部门关系**: 依赖department表确定部门负责人权限
4. **角色权限**: 依赖employee.role字段进行权限控制

### 技术集成要求
1. **认证机制**: 复用现有JWT认证体系
2. **权限拦截**: 集成现有权限控制机制
3. **UI风格**: 与现有页面保持一致的Element Plus样式
4. **API规范**: 遵循现有的Result/PageResult响应格式

## 业务流程分析

### 日报提交流程
1. 员工登录系统
2. 进入销售日报页面
3. 系统自动计算统计类数据
4. 员工填写客户选择和文本内容
5. 提交日报（一天只能提交一次，可修改当天的记录）

### 数据查看流程
1. 根据用户角色确定数据范围
2. 分页展示日报列表
3. 支持按时间、员工等条件筛选
4. 提供详情查看功能

## 约束条件

### 业务约束
- 每个员工每天只能提交一份日报
- 只能修改当天的日报记录
- 历史日报不可删除，只能查看

### 技术约束
- 必须与现有数据库结构兼容
- 不能影响现有功能的正常运行
- 需要考虑数据库性能，特别是客户数据查询

### 时间约束
- 需要分阶段实施，确保每个阶段都有可交付的成果

## 风险识别

### 技术风险
- 客户数据量大时的查询性能问题
- 多选客户数据的存储和查询复杂性
- 权限控制的准确性和安全性

### 业务风险
- 用户接受度和使用习惯培养
- 数据准确性和完整性保障
- 与现有工作流程的冲突

## 后续分析要点

1. 详细的数据表设计方案
2. API接口设计规范
3. 前端页面布局和交互设计
4. 性能优化策略
5. 测试策略制定

---

**下一步**: 进入INNOVATE阶段，设计具体的技术方案

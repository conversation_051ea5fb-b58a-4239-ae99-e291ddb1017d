package org.example.company_management.service;

import com.github.pagehelper.Page;
import org.example.company_management.entity.Performance;
import org.example.company_management.dto.DepartmentPerformanceStatsDTO;
import org.example.company_management.dto.PerformanceImportDTO;
import org.example.company_management.utils.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface PerformanceService {
    /**
     * 分页查询业绩记录
     *
     * @param page        页码
     * @param size        每页大小
     * @param employeeId  员工ID，可为null
     * @param employeeName 员工姓名，可为null，用于模糊查询
     * @param departmentId 部门ID，可为null
     * @param yearMonth   年月，可为null，格式为yyyy-MM
     * @return 分页结果
     */
    Page<Performance> getPerformancePage(Integer page, Integer size, Integer employeeId, String employeeName, Integer departmentId, String yearMonth);

    /**
     * 根据ID查询业绩记录
     *
     * @param id 业绩记录ID
     * @return 业绩记录
     */
    Performance getById(Integer id);

    /**
     * 添加业绩记录
     *
     * @param performance 业绩记录
     * @return 是否成功
     */
    boolean addPerformance(Performance performance);

    /**
     * 更新业绩记录
     *
     * @param performance 业绩记录
     * @return 是否成功
     */
    boolean updatePerformance(Performance performance);

    /**
     * 删除业绩记录
     *
     * @param id 业绩记录ID
     * @return 是否成功
     */
    boolean deletePerformance(Integer id);

    /**
     * 批量删除业绩记录
     *
     * @param ids 业绩记录ID列表
     * @return 是否成功
     */
    boolean batchDeletePerformance(List<Integer> ids);

    /**
     * 获取多部门业绩列表
     *
     * @param departmentIds 部门ID列表
     * @param page 页码
     * @param size 每页大小
     * @param startDate 开始年月，格式YYYY-MM，可选
     * @param endDate 结束年月，格式YYYY-MM，可选
     * @param employeeName 员工姓名，可为null，用于模糊查询
     * @return 多部门业绩数据，包含分页信息和记录列表
     */
    Map<String, Object> getMultiDepartmentPerformances(List<Integer> departmentIds, Integer page, Integer size, String startDate, String endDate, String employeeName);

    /**
     * 获取部门业绩统计数据
     * @param departmentIds 部门ID列表
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 部门业绩统计数据列表
     */
    List<DepartmentPerformanceStatsDTO> getPerformanceStatsByDepartments(
        List<Integer> departmentIds, String startDate, String endDate);

    /**
     * 从Excel导入业绩数据
     *
     * @param file 上传的Excel文件
     * @return 导入结果，包含成功、失败条数及详细信息
     */
    ExcelUtil.ImportResult<PerformanceImportDTO> importPerformancesFromExcel(MultipartFile file);
}

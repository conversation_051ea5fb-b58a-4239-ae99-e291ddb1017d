package org.example.company_management.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 工资导入数据传输对象 (DTO)
 * 用于接收和处理从Excel文件中读取的工资信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalaryImportDTO {

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String employeeName;

    /**
     * 身份证号码
     * 用于唯一识别员工。
     */
    @ExcelProperty("身份证号码")
    @NotBlank(message = "身份证号码不能为空")
    @Size(min = 18, max = 18, message = "身份证号码长度必须为18位")
    private String idCard;

    /**
     * 年月
     * 格式应为 YYYY-MM。
     */
    @ExcelProperty("年月")
    @NotBlank(message = "年月不能为空")
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])$", message = "年月格式必须为YYYY-MM")
    private String yearMonth;

    /**
     * 基本工资
     */
    @ExcelProperty("基本工资")
    @DecimalMin(value = "0.0", message = "基本工资不能为负数")
    private BigDecimal basicSalary;

    /**
     * 绩效奖金
     */
    @ExcelProperty("奖金")
    private BigDecimal performanceBonus;

    /**
     * 全勤奖金
     */
    @ExcelProperty("全勤奖")
    private BigDecimal fullAttendanceBonus;

    /**
     * 业务操作奖金
     */
    @ExcelProperty("业务与操作奖金")
    private BigDecimal businessOperationBonus;

    /**
     * 报销
     */
    @ExcelProperty("报销")
    private BigDecimal reimbursement;

    /**
     * 私账
     */
    @ExcelProperty("私账")
    private BigDecimal privateAccount; // Assuming private account can be a deduction if negative.

    /**
     * 扣款 (General Deduction)
     */
    @ExcelProperty("扣借款")
    private BigDecimal deduction;

    /**
     * 请假扣款
     */
    @ExcelProperty("请假")
    private BigDecimal leaveDeduction;

    /**
     * 迟到缺卡扣款
     */
    @ExcelProperty("迟到与缺卡")
    private BigDecimal lateDeduction;

    /**
     * 社保个人部分
     */
    @ExcelProperty("社会保险费个人部分")
    private BigDecimal socialSecurityPersonal;

    /**
     * 公积金
     */
    @ExcelProperty("公积金")
    private BigDecimal providentFund;

    /**
     * 个税
     */
    @ExcelProperty("代扣代缴个税")
    private BigDecimal tax;

    /**
     * 水电费
     */
    @ExcelProperty("水电费")
    private BigDecimal waterElectricityFee;

    /**
     * 实得金额
     */
    @ExcelProperty("实得金额")
    private BigDecimal sumSalary;

    /**
     * 实发工资
     */
    @ExcelProperty("实发工资")
    private BigDecimal actualSalary;

    /**
     * 合计
     * (通常等于实发工资，但根据用户定义，允许用户输入)
     */
    @ExcelProperty("合计")
    // 合计可能为负数
    private BigDecimal totalSalary;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark; // 备注是可选的，所以没有 @NotBlank 或 @NotNull

    // --- 以下为非Excel直接映射字段，用于处理过程 ---


    private Integer rowIndex;

    /**
     * 错误信息
     * 用于记录在处理此行数据时发生的校验错误或其他问题。
     */
    private String errorMessage;
} 
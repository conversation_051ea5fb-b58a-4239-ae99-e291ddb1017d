package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.dto.DepartmentPerformanceStatsDTO;
import org.example.company_management.entity.Performance;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 业绩记录数据访问层
 */
@Mapper
public interface PerformanceMapper {
    /**
     * 查询所有业绩记录
     *
     * @return 业绩记录列表
     */
    List<Performance> selectAll();

    /**
     * 根据条件查询业绩记录
     *
     * @param employeeId   员工ID，可为null
     * @param employeeName 员工姓名，可为null，用于模糊查询
     * @param departmentId 部门ID，可为null
     * @param yearMonth    年月，可为null，格式为yyyy-MM
     * @return 业绩记录列表
     */
    List<Performance> selectByCondition(
            @Param("employeeId") Integer employeeId,
            @Param("employeeName") String employeeName,
            @Param("departmentId") Integer departmentId,
            @Param("yearMonth") String yearMonth
    );

    /**
     * 根据主键查询业绩记录
     *
     * @param id 主键
     * @return 业绩记录
     */
    Performance selectByPrimaryKey(Integer id);

    /**
     * 根据员工ID和日期查询业绩记录
     *
     * @param employeeId 员工ID
     * @param date       日期
     * @return 业绩记录
     */
    Performance selectByEmployeeIdAndDate(Integer employeeId, String date);

    /**
     * 插入业绩记录
     *
     * @param performance 业绩记录
     * @return 影响行数
     */
    int insert(Performance performance);

    /**
     * 根据主键更新业绩记录
     *
     * @param performance 业绩记录
     * @return 影响行数
     */
    int updateByPrimaryKey(Performance performance);

    /**
     * 根据主键删除业绩记录
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 根据部门ID列表和日期查询业绩记录
     *
     * @param departmentIds 部门ID列表
     * @param date          日期，格式为yyyy-MM
     * @param employeeName  员工姓名，可为null，用于模糊查询
     * @return 业绩记录列表
     */
    List<Performance> selectByDepartmentIdsAndDate(
            @Param("departmentIds") List<Integer> departmentIds,
            @Param("date") String date,
            @Param("employeeName") String employeeName
    );

    /**
     * 查询部门业绩统计数据
     *
     * @param departmentIds 部门ID列表
     * @param date          日期参数（可以是单个日期或日期范围）
     * @return 部门业绩统计数据列表
     */
    List<DepartmentPerformanceStatsDTO> selectDepartmentStats(
            @Param("departmentIds") List<Integer> departmentIds,
            @Param("date") String date
    );

    /**
     * 根据员工ID和日期查询特定业绩数据（预估和实际）
     *
     * @param employeeId 员工ID
     * @param date       日期 (YYYY-MM)
     * @return 包含预估和实际业绩的Performance对象，如果无记录则为null
     */
    Performance selectPerformanceDataByEmployeeIdAndDate(@Param("employeeId") Integer employeeId, @Param("date") String date);

    /**
     * 根据部门ID和年月查询部门总费用
     *
     * @param departmentId 部门ID
     * @param yearMonth    年月，格式为yyyy-MM
     * @return 部门总费用
     */
    BigDecimal sumTotalDepartmentExpensesByDepartmentIdAndMonth(@Param("departmentId") Integer departmentId, @Param("yearMonth") String yearMonth);

    /**
     * 根据员工ID和年月查询员工其他费用
     *
     * @param employeeId 员工ID
     * @param yearMonth    年月，格式为yyyy-MM
     * @return 员工其他费用
     */
    BigDecimal sumTotalEmployeeOtherExpensesByEmployeeIdAndMonth(@Param("employeeId") Integer employeeId, @Param("yearMonth") String yearMonth);

    /**
     * 根据员工ID和日期（YYYY-MM）获取业绩记录
     * @param employeeId 员工ID
     * @param date YYYY-MM格式的日期字符串
     * @return 业绩记录，可能为空
     */
    Optional<Performance> findByEmployeeIdAndDate(@Param("employeeId") Integer employeeId, @Param("date") String date);
} 
package org.example.company_management.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 业绩导入Excel数据传输对象
 */
@Data
public class PerformanceImportDTO {

    @ExcelProperty("身份证号码")
    @NotBlank(message = "身份证号码不能为空")
    @Size(min = 18, max = 18, message = "身份证号码长度必须为18位")
    private String idCard;

    @ExcelProperty("年月")
    @NotBlank(message = "年月不能为空")
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])$", message = "年月格式必须为YYYY-MM")
    private String date; // 格式 YYYY-MM

    @ExcelProperty("预估业绩")
    private BigDecimal estimatedPerformance;

    @ExcelProperty("实际业绩")
    private BigDecimal actualPerformance;
} 
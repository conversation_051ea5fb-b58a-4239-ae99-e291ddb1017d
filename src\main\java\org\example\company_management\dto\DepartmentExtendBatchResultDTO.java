package org.example.company_management.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor // Ensure a no-args constructor for Jackson or other frameworks
public class DepartmentExtendBatchResultDTO {
    private int totalProcessed = 0;
    private int totalSuccessfullyExtended = 0;
    private int totalSkippedAsDuplicates = 0;
    private List<String> successfullyExtendedItems = new ArrayList<>();
    private List<String> skippedDuplicateItems = new ArrayList<>();
} 
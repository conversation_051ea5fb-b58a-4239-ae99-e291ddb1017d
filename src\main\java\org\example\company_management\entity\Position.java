package org.example.company_management.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 职位实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Position {
    /**
     * 职位ID
     */
    private Integer positionId;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 职位描述
     */
    private String positionDescription;

    /**
     * 主要部门ID（非数据库字段）
     * 用于表示该职位的主要所属部门，通常取departmentIds的第一个值
     * 在多部门关联的情况下，作为默认或主要的部门ID使用
     * 注意：position表中已不存在此字段，职位与部门的关联通过position_department表维护
     */
    private Integer departmentId;

    /**
     * 所属部门ID列表（非数据库字段，用于前端传递多个部门ID）
     */
    private List<Integer> departmentIds;

    /**
     * 所属部门名称（非数据库字段）
     */
    private String departmentName;

    /**
     * 所属部门名称列表（非数据库字段，用于前端显示）
     */
    private List<String> departmentNames;

    /**
     * 职位状态：Active-启用，Inactive-禁用
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 
import request from './request'

/**
 * 分页查询员工费用 (用户视图)
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.size 每页大小
 * @param {Array<number>} params.departmentIds 部门ID列表
 * @param {string} [params.employeeName] 员工姓名 (可选)
 * @param {string} [params.itemName] 项目名称 (可选)
 * @param {string} [params.date] 年月 (可选, YYYY-MM格式)
 * @returns {Promise<any>}
 */
export function fetchEmployeeExpenses(params) {
  return request({
    url: '/employee-expense/user-view/page',
    method: 'post',
    data: params
  });
}

/**
 * 根据ID获取员工费用详情
 * @param {number} id 员工费用ID
 * @returns {Promise<any>}
 */
export function getEmployeeExpenseById(id) {
  return request({
    url: `/employee-expense/${id}`,
    method: 'get'
  });
}



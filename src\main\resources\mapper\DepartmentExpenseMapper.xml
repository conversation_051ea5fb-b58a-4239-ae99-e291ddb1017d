<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.DepartmentExpenseMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO department_expense (department_id, expense_date, amount, item_name, remark, create_time, update_time)
        VALUES (#{departmentId}, #{expenseDate}, #{amount}, #{itemName}, #{remark}, #{createTime}, #{updateTime})
    </insert>

    <select id="selectPage" resultType="org.example.company_management.entity.DepartmentExpense">
        SELECT de.*, d.department_name as department_name
        FROM department_expense de
        LEFT JOIN department d ON de.department_id = d.department_id
        <where>
            <if test="departmentId != null">
                AND de.department_id = #{departmentId}
            </if>
            <if test="startDate != null">
                AND de.expense_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND de.expense_date &lt;= #{endDate}
            </if>
            <if test="description != null and description != ''">
                AND de.item_name LIKE CONCAT('%', #{description}, '%')
            </if>
        </where>
        ORDER BY de.expense_date DESC, de.create_time DESC
    </select>

    <select id="selectById" resultType="org.example.company_management.entity.DepartmentExpense">
        SELECT de.*, d.department_name as department_name
        FROM department_expense de
        LEFT JOIN department d ON de.department_id = d.department_id
        WHERE de.id = #{id}
    </select>

    <update id="update">
        UPDATE department_expense
        <set>
            <if test="departmentId != null">
                department_id = #{departmentId},
            </if>
            <if test="expenseDate != null">
                expense_date = #{expenseDate},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="itemName != null">
                item_name = #{itemName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM department_expense WHERE id = #{id}
    </delete>

    <delete id="batchDeleteByIds">
        DELETE FROM department_expense
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByDepartmentIdAndExpenseDateAndItemName" resultType="int">
        SELECT COUNT(*)
        FROM department_expense
        WHERE department_id = #{departmentId}
          AND expense_date = #{expenseDate}
          AND item_name = #{itemName}
    </select>

    <select id="selectPageForUserView" resultType="org.example.company_management.entity.DepartmentExpense">
        SELECT de.*, d.department_name as department_name
        FROM department_expense de
        LEFT JOIN department d ON de.department_id = d.department_id
        <where>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND de.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="startDate != null">
                AND de.expense_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND de.expense_date &lt;= #{endDate}
            </if>
            <if test="itemName != null and itemName != ''">
                AND de.item_name LIKE CONCAT('%', #{itemName}, '%')
            </if>
        </where>
        ORDER BY de.expense_date DESC, de.create_time DESC
    </select>

</mapper> 
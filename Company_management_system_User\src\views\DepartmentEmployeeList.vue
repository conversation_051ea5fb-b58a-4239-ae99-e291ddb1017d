<template>
  <div class="department-employee-list">
    <!-- 使用DepartmentNode组件作为根节点，显示当前用户的部门及其子部门 -->
    <div v-if="rootDepartment" class="department-tree-container">
      <DepartmentNode 
        :department="rootDepartment" 
        :depth="0"
      />
    </div>

    <!-- 加载中或错误状态显示 -->
    <div v-else class="empty-state">
      <p v-if="loading">正在加载部门数据...</p>
      <p v-else>无法获取您的部门信息，无法加载员工列表</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, provide } from 'vue';
import {
  ElLoading,
  ElMessage,
  ElTag,
  ElTooltip
} from 'element-plus';
import { useAuthStore } from '@/stores/token'; // 用于获取用户信息
import { getCurrentDepartment, getSubDepartments } from '@/api/department';
import { getDepartmentEmployeesPage } from '@/api/department';
// 导入DepartmentNode组件
import DepartmentNode from '@/components/DepartmentNode.vue';

const authStore = useAuthStore();

// 页面加载状态
const loading = ref(false);

// 根部门信息（当前用户的部门）
const rootDepartment = ref(null);
// 显示数据结构 - 保持兼容现有DepartmentNode组件
const displayData = ref({}); // { deptId: { employees: [], currentPage: 1, pageSize: 10, total: 0, loading: false, searchKeyword: '' } }

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  try {
    // 处理ISO格式的日期字符串（如"2025-04-13T16:00:00.000+00:00"）
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    // 使用本地日期显示（考虑时区），格式为YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
};

// 初始化部门显示数据结构
const initializeDisplayData = (deptId) => {
  if (!displayData.value[deptId]) {
    displayData.value[deptId] = {
      employees: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      searchKeyword: '', // 新增：搜索关键词字段
    };
  }
};

// 获取员工数据 - DepartmentNode组件会调用此方法
const fetchEmployees = async (departmentId, page = 1, pageSize = 10) => {
  if (!departmentId || !displayData.value[departmentId]) return;

  const deptDisplay = displayData.value[departmentId];
  deptDisplay.loading = true;

  try {
    // 调用API获取员工数据
    const params = {
      pageNum: page,
      pageSize: deptDisplay.pageSize
    };
    
    // 如果有搜索关键词，添加到请求参数中
    if (deptDisplay.searchKeyword) {
      params.name = deptDisplay.searchKeyword;
    }
    
    const res = await getDepartmentEmployeesPage(departmentId, params);
    
    if (res.code === 200) {
      deptDisplay.employees = res.data.list || [];
      deptDisplay.total = res.data.total;
      deptDisplay.currentPage = page;
    } else {
      ElMessage.error(res.message || '获取员工数据失败');
      deptDisplay.employees = [];
      deptDisplay.total = 0;
    }
  } catch (error) {
    console.error(`获取部门 ${departmentId} 员工失败:`, error);
    ElMessage.error(`获取部门 ${departmentId} 员工信息失败`);
    deptDisplay.employees = [];
    deptDisplay.total = 0;
  } finally {
    deptDisplay.loading = false;
  }
};

// 搜索部门员工 - 新增方法
const searchEmployees = async (departmentId, keyword) => {
  if (!departmentId || !displayData.value[departmentId]) return;
  
  // 更新搜索关键词
  displayData.value[departmentId].searchKeyword = keyword;
  // 重置为第一页
  displayData.value[departmentId].currentPage = 1;
  // 使用搜索条件重新获取数据
  await fetchEmployees(departmentId, 1);
};

// 处理分页变化 - 需要提供给DepartmentNode组件
const handlePageChange = (departmentId, newPage) => {
  fetchEmployees(departmentId, newPage);
};

// 处理页面大小变化 - 需要提供给DepartmentNode组件
const handleSizeChange = (departmentId, newSize) => {
  if (!displayData.value[departmentId]) return;
  displayData.value[departmentId].pageSize = newSize;
  // 改变每页条数时，通常回到第一页
  displayData.value[departmentId].currentPage = 1;
  fetchEmployees(departmentId, 1); // 使用新的 pageSize 重新获取第一页
};

// 加载子部门 - 提供给DepartmentNode组件调用
const loadSubDepartmentsForNode = async (departmentId) => {
  if (!departmentId) return [];
  
  try {
    const res = await getSubDepartments(departmentId);
    if (res.code === 200) {
      // 格式化子部门数据，以符合DepartmentNode要求的格式
      const subDepts = (res.data || []).map(dept => ({
        id: dept.departmentId,
        name: dept.departmentName,
        // 可以添加其他需要的属性
      }));
      
      // 预初始化每个子部门的displayData
      subDepts.forEach(dept => {
        initializeDisplayData(dept.id);
      });
      
      return subDepts;
    } else {
      ElMessage.error(res.message || '获取子部门列表失败');
      return [];
    }
  } catch (error) {
    console.error('获取子部门列表失败:', error);
    ElMessage.error('获取子部门列表失败');
    return [];
  }
};

// 加载当前用户部门信息 - 作为根节点
const loadCurrentDepartment = async () => {
  loading.value = true;
  try {
    const res = await getCurrentDepartment();
    
    if (res.code === 200) {
      // 获取当前用户所属部门和负责的部门列表
      const { myDepartment, leadingDepartments } = res.data;
      
      // 处理用户所属部门 (逻辑已移除)
      // if (myDepartment) { ... } // 此部分已根据需求移除
      
      // 初始化 rootDepartment 为 null，以确保在没有负责部门时正确处理
      rootDepartment.value = null;

      // 处理用户负责的部门列表
      if (leadingDepartments && leadingDepartments.length > 0) {
        // 创建'我负责的部门'根节点
        const leadingDepartmentsNode = {
          id: 'leading-departments', // 使用特殊ID，因为这不是实际部门ID
          name: '负责部门',
          isVirtual: true, // 标记为虚拟节点，不加载员工
          subDepartments: leadingDepartments.map(dept => ({
            id: dept.departmentId,
            name: dept.departmentName,
            // 其他需要的属性
          }))
        };
        
        // 直接将负责的部门作为根节点
        rootDepartment.value = leadingDepartmentsNode;
        
        // 为每个负责的部门初始化displayData
        leadingDepartments.forEach(dept => {
          initializeDisplayData(dept.departmentId);
        });
      }
      
      // 如果既没有所属部门（已移除逻辑），也没有负责的部门
      if (!rootDepartment.value) {
        console.error("用户没有负责的部门"); // 更新错误信息
        ElMessage.error("您没有负责的部门，无法加载员工列表"); // 更新错误信息
      }
      
      return rootDepartment.value;
    } else {
      ElMessage.error(res.message || '获取当前部门信息失败');
      rootDepartment.value = null;
      return null;
    }
  } catch (error) {
    console.error('获取当前部门信息失败:', error);
    ElMessage.error('获取当前部门信息失败');
    rootDepartment.value = null;
    return null;
  } finally {
    loading.value = false;
  }
};

// 在所有函数声明都完成后，提供它们给DepartmentNode组件
provide('displayData', displayData);
provide('formatDate', formatDate);
provide('fetchEmployees', fetchEmployees);
provide('searchEmployees', searchEmployees); // 新增：提供搜索方法
provide('handlePageChange', handlePageChange);
provide('handleSizeChange', handleSizeChange);
provide('loadSubDepartmentsForNode', loadSubDepartmentsForNode);

onMounted(async () => {
  // 加载当前用户的部门信息作为根节点
  await loadCurrentDepartment();
  
  if (!rootDepartment.value) {
    console.error("无法获取当前用户的部门信息");
    ElMessage.error("无法获取您的部门信息，无法加载员工列表");
  }
});
</script>

<style scoped>
.department-employee-list {
  padding: 20px;
}

.department-tree-container {
  margin-top: 10px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  font-size: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-top: 20px;
}

/* 确保部门节点根节点没有缩进 */
.department-tree-container :deep(.department-node) {
  margin-left: 0 !important;
}

/* 保持表格悬停效果与原来一致 */
:deep(.el-table--enable-row-hover .el-table__body tr:hover) {
  background-color: #ecf5ff !important;
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-out;
}

:deep(.el-table__body tr:hover > td) {
  background-color: transparent !important; /* Override default td hover */
}

/* 为表格添加底部间距，避免分页器紧贴 */
:deep(.el-table) {
  margin-bottom: 10px;
}

/* 分页容器样式 */
:deep(.pagination-container) {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

/* 直接为分页组件添加样式 */
:deep(.pagination-container > .el-pagination) {
  background-color: #fff;
  padding: 5px 10px;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 
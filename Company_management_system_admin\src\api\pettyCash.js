import request from '@/utils/request'

/**
 * 获取备用金分页列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPettyCashPage(params) {
  return request({
    url: '/petty-cash/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取备用金详情
 * @param {Number} id - 备用金ID
 * @returns {Promise}
 */
export function getPettyCashById(id) {
  return request({
    url: `/petty-cash/${id}`,
    method: 'get'
  })
}

/**
 * 添加备用金记录
 * @param {Object} data - 备用金数据
 * @returns {Promise}
 */
export function addPettyCash(data) {
  return request({
    url: '/petty-cash',
    method: 'post',
    data
  })
}

/**
 * 更新备用金记录
 * @param {Object} data - 备用金数据
 * @returns {Promise}
 */
export function updatePettyCash(data) {
  return request({
    url: `/petty-cash/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除备用金记录
 * @param {Number} id - 备用金ID
 * @returns {Promise}
 */
export function deletePettyCash(id) {
  return request({
    url: `/petty-cash/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除备用金记录
 * @param {Array} ids - 备用金ID数组
 * @returns {Promise}
 */
export function batchDeletePettyCash(ids) {
  return request({
    url: '/petty-cash/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 审核备用金申请
 * @param {Number} id - 备用金ID
 * @param {String} status - 审核状态（已审核/已拒绝）
 * @returns {Promise}
 */
export function approvePettyCash(id, status) {
  return request({
    url: `/petty-cash/approve/${id}`,
    method: 'put',
    data: { status }
  })
}

/**
 * 获取待审批的备用金列表（状态为"审核中"的记录）
 * @returns {Promise}
 */
export function getPendingPettyCash() {
  return request({
    url: '/petty-cash/pending',
    method: 'get'
  })
}

/**
 * 获取待审批的备用金数量
 * @returns {Promise}
 */
export function getPendingPettyCashCount() {
  return request({
    url: '/petty-cash/pending-count',
    method: 'get'
  })
} 
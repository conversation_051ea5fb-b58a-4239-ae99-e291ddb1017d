<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.EmployeeExpenseMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO employee_other_expense (employee_id, expense_date, amount, item_name, remark, create_time, update_time)
        VALUES (#{employeeId}, #{expenseDate}, #{amount}, #{itemName}, #{remark}, #{createTime}, #{updateTime})
    </insert>

    <select id="selectPage" resultType="org.example.company_management.entity.EmployeeExpense">
        SELECT eoe.*, e.name as employee_name, d.department_name as department_name, d.department_id as department_id
        FROM employee_other_expense eoe
        LEFT JOIN employee e ON eoe.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        <where>
            <if test="employeeId != null">
                AND eoe.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="startDate != null">
                AND eoe.expense_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND eoe.expense_date &lt;= #{endDate}
            </if>
            <if test="itemName != null and itemName != ''">
                AND eoe.item_name LIKE CONCAT('%', #{itemName}, '%')
            </if>
        </where>
        ORDER BY eoe.expense_date DESC, eoe.create_time DESC
    </select>

    <select id="selectById" resultType="org.example.company_management.entity.EmployeeExpense">
        SELECT eoe.*, e.name as employee_name
        FROM employee_other_expense eoe
        LEFT JOIN employee e ON eoe.employee_id = e.employee_id
        WHERE eoe.id = #{id}
    </select>

    <update id="update">
        UPDATE employee_other_expense
        <set>
            <if test="employeeId != null">
                employee_id = #{employeeId},
            </if>
            <if test="expenseDate != null">
                expense_date = #{expenseDate},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="itemName != null">
                item_name = #{itemName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM employee_other_expense WHERE id = #{id}
    </delete>

    <delete id="batchDeleteByIds">
        DELETE FROM employee_other_expense
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByEmployeeIdAndExpenseDateAndItemName" resultType="int">
        SELECT COUNT(*)
        FROM employee_other_expense
        WHERE employee_id = #{employeeId}
          AND expense_date = #{expenseDate}
          AND item_name = #{itemName}
    </select>

    <select id="selectPageForUserView" resultType="org.example.company_management.entity.EmployeeExpense">
        SELECT eoe.*, e.name as employee_name, d.department_name as department_name, d.department_id as department_id
        FROM employee_other_expense eoe
        LEFT JOIN employee e ON eoe.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        <where>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND e.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="startDate != null">
                AND eoe.expense_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND eoe.expense_date &lt;= #{endDate}
            </if>
            <if test="itemName != null and itemName != ''">
                AND eoe.item_name LIKE CONCAT('%', #{itemName}, '%')
            </if>
        </where>
        ORDER BY eoe.expense_date DESC, eoe.create_time DESC
    </select>

</mapper> 
<script setup>
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
    isVisible: {
        type: Boolean,
        required: true,
    },
    itemsToExtend: { // 原始项目列表，不包含勾选状态
        type: Array,
        required: true,
        default: () => []
    },
    monthOptions: {
        type: Array,
        required: true,
        default: () => []
    },
    entityName: {
        type: String,
        default: '项目'
    },
    itemKeyField: { // 用于 el-table row-key 和内部映射的字段名
        type: String,
        required: true
    },
    itemDisplayFields: { // 定义如何在表格中显示每个项目
        type: Array,
        required: true,
        default: () => [
            // 示例: { label: '名称', prop: 'name', minWidth: '150px' }
            //       { label: '金额', prop: 'amount', width: '100px', align: 'right', isCurrency: true }
        ]
    }
});

const emit = defineEmits(['update:isVisible', 'submit-extend']);

const dialogItemsRef = ref([]); // 在对话框内部管理的项目列表，包含 isSelectedForExtend 状态
const targetMonthsRef = ref([]);
const loadingRef = ref(false);

// 货币格式化函数 (暂时复制)
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return '¥' + parseFloat(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const initializeDialogItems = () => {
    dialogItemsRef.value = JSON.parse(JSON.stringify(props.itemsToExtend)).map(item => ({
        ...item,
        isSelectedForExtend: true, // 默认全选传入的项目
        _internalKey: item[props.itemKeyField] || Symbol() // 确保有唯一的 key
    }));
};

watch(() => props.isVisible, (newVal) => {
    if (newVal) {
        initializeDialogItems();
        targetMonthsRef.value = []; // 重置目标月份
    }
});

watch(() => props.itemsToExtend, () => {
    if (props.isVisible) {
        initializeDialogItems();
    }
}, { deep: true });


const isAllSelected = computed({
    get: () => dialogItemsRef.value.length > 0 && dialogItemsRef.value.every(item => item.isSelectedForExtend),
    set: (val) => {
        dialogItemsRef.value.forEach(item => {
            item.isSelectedForExtend = val;
        });
    }
});

const isIndeterminate = computed(() => {
    const selectedCount = dialogItemsRef.value.filter(item => item.isSelectedForExtend).length;
    return selectedCount > 0 && selectedCount < dialogItemsRef.value.length;
});

const handleCloseDialog = () => {
    emit('update:isVisible', false);
};

const handleSubmit = () => {
    const selectedItems = dialogItemsRef.value.filter(item => item.isSelectedForExtend);
    if (selectedItems.length === 0) {
        ElMessage.warning(`请至少勾选一个${props.entityName}进行延用`);
        return;
    }
    if (targetMonthsRef.value.length === 0) {
        ElMessage.warning('请至少选择一个目标月份');
        return;
    }

    loadingRef.value = true;
    // 构建提交给父组件的数据，移除内部辅助字段
    const itemsToSubmit = selectedItems.map(item => {
        const { isSelectedForExtend, _internalKey, ...rest } = item;
        return rest;
    });

    emit('submit-extend', {
        selectedItems: itemsToSubmit,
        targetMonths: targetMonthsRef.value
    });
    // loadingRef 将由父组件在API调用后处理，或如果API调用快，可以直接在这里关闭对话框
    // 为了简单，假设父组件会处理关闭和loading
    // loadingRef.value = false; // 通常由父组件重置或通过prop控制
};

// 暴露 resetLoadingState 方法给父组件
const resetLoadingState = () => {
    loadingRef.value = false;
};

defineExpose({ resetLoadingState });

</script>

<template>
    <el-dialog
        :model-value="isVisible"
        :title="`批量延用${entityName}`"
        width="750px"
        destroy-on-close
        class="custom-dialog batch-extend-dialog-reusable"
        :close-on-click-modal="false"
        @update:modelValue="val => emit('update:isVisible', val)"
        @close="handleCloseDialog"
    >
        <div v-if="dialogItemsRef.length > 0" class="extend-dialog-content">
            <p class="dialog-tip" style="margin-bottom: 15px;">
                勾选需要延用的{{ entityName }}，并选择目标月份：
            </p>
            <el-table
                :data="dialogItemsRef"
                border
                size="small"
                max-height="280px"
                class="extend-items-table"
                :row-key="'_internalKey'"
            >
                <el-table-column width="55" align="center">
                    <template #header>
                        <el-checkbox
                            v-model="isAllSelected"
                            :indeterminate="isIndeterminate"
                            title="全选/取消全选"
                        />
                    </template>
                    <template #default="{ row }">
                        <el-checkbox v-model="row.isSelectedForExtend" />
                    </template>
                </el-table-column>

                <template v-for="field in itemDisplayFields" :key="field.prop">
                    <el-table-column
                        :prop="field.prop"
                        :label="field.label"
                        :width="field.width"
                        :min-width="field.minWidth"
                        :align="field.align || 'left'"
                        show-overflow-tooltip
                    >
                        <template #default="{ row }">
                            <span v-if="field.isCurrency">{{ formatCurrency(row[field.prop]) }}</span>
                            <span v-else-if="field.formatter">{{ field.formatter(row[field.prop], row) }}</span>
                            <span v-else>{{ row[field.prop] === null || row[field.prop] === undefined || row[field.prop] === '' ? '-' : row[field.prop] }}</span>
                        </template>
                    </el-table-column>
                </template>
            </el-table>

            <el-form label-width="160px" style="margin-top: 20px; margin-bottom: 0px;">
                <el-form-item label="选择延用目标月份" required>
                    <el-select
                        v-model="targetMonthsRef"
                        multiple
                        filterable
                        placeholder="请选择一个或多个目标月份"
                        style="width: 300px;"
                        clearable
                        size="default"
                    >
                        <el-option
                            v-for="item in monthOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <p class="dialog-tip" style="margin-top: 0px; font-size: 12px; color: #909399;">
                提示：系统将为每个勾选的{{ entityName }}和每个目标月份创建新记录。已存在对应数据不会覆盖，请手动编辑
            </p>
        </div>
        <div v-else class="empty-extend-dialog">
            <el-empty :description="`没有有效的${entityName}可供延用（可能因重复或未选择被过滤）`" />
        </div>

        <div class="dialog-footer" style="margin-top: 10px; padding-bottom: 15px;">
            <el-button @click="handleCloseDialog">取消</el-button>
            <el-button
                type="primary"
                :loading="loadingRef"
                @click="handleSubmit"
                :disabled="dialogItemsRef.filter(i => i.isSelectedForExtend).length === 0 || targetMonthsRef.length === 0"
            >确定延用</el-button>
        </div>
    </el-dialog>
</template>

<style scoped>
/* Styles migrated and adapted from EmployeeExpense.vue */
.batch-extend-dialog-reusable .dialog-tip {
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
}

.batch-extend-dialog-reusable .extend-items-table {
    margin-bottom: 15px;
}

.batch-extend-dialog-reusable .empty-extend-dialog {
    padding: 20px;
    text-align: center;
}

/* Copied from EmployeeExpense.vue for dialog consistency */
:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}
/* :deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0px;  Adjusted specific to this dialog's content
} */
:deep(.custom-dialog .el-dialog__footer) {
    display: none; /* Assuming custom footer buttons are used as in the original */
}

.batch-extend-dialog-reusable :deep(.el-dialog__body) {
    padding: 15px 25px 5px 25px; /* Specific padding for this dialog */
}

/* Styles for vertical alignment of form item label and content */
.batch-extend-dialog-reusable :deep(.el-form-item__label) {
    display: flex;
    align-items: center;
    height: 32px; /* 与 el-select (size=default) 高度近似匹配 */
    line-height: 1; /* 避免 line-height 造成额外空间 */
    padding-bottom: 0;
    padding-top: 0;
}

.batch-extend-dialog-reusable :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
}
/* End of styles for vertical alignment */

.dialog-footer { /* Standard footer style */
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 0 20px 10px 20px; /* Match other dialogs if needed */
    gap: 10px;
}
</style> 
# 销售日报API接口规范

**版本**: v1.0  
**创建时间**: 2025-06-04 10:46:28 +08:00  
**设计负责人**: AR, LD  
**审核状态**: 待审核

## 设计原则

- **RESTful风格**: 遵循REST API设计规范
- **统一响应格式**: 使用现有的Result/PageResult封装
- **权限控制**: 集成现有JWT认证和角色权限体系
- **错误处理**: 统一的异常处理和错误码
- **向后兼容**: 保持API版本兼容性

## 基础路径

**Base URL**: `/api/sales-report`

## 通用响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": { ... }
}
```

### 分页响应
```json
{
    "code": 200,
    "message": "查询成功", 
    "data": {
        "list": [...],
        "total": 100,
        "pageNum": 1,
        "pageSize": 10
    }
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "参数验证失败",
    "data": null
}
```

## API接口详情

### 1. 提交/更新日报

**接口**: `POST /api/sales-report`  
**权限**: 员工本人  
**描述**: 提交或更新当天的销售日报

#### 请求参数
```json
{
    "reportDate": "2025-06-04",
    "inquiryClients": [1, 5, 12],
    "shippingClients": [3, 8],
    "keyDevelopmentClients": [1, 9, 15],
    "responsibilityLevel": "优秀",
    "endOfDayChecklist": {
        "deskOrganized": true,
        "emailsHandled": true,
        "meetingsCompleted": false,
        "materialsReady": true,
        "greetedLeader": true
    },
    "dailyResults": "今日完成了3个客户的报价工作...",
    "meetingReport": "参加了部门周会，讨论了...",
    "workDiary": "今日主要工作总结：1. 完成客户A的需求分析..."
}
```

#### 响应示例
```json
{
    "code": 200,
    "message": "日报提交成功",
    "data": {
        "id": 123,
        "reportDate": "2025-06-04",
        "yearlyNewClients": 15,
        "monthlyNewClients": 3,
        "daysSinceLastNewClient": 5
    }
}
```

### 2. 获取个人日报详情

**接口**: `GET /api/sales-report/{date}`  
**权限**: 员工本人  
**描述**: 获取指定日期的个人日报详情

#### 路径参数
- `date`: 日期，格式YYYY-MM-DD

#### 响应示例
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "id": 123,
        "employeeId": 5,
        "employeeName": "张三",
        "reportDate": "2025-06-04",
        "yearlyNewClients": 15,
        "monthlyNewClients": 3,
        "daysSinceLastNewClient": 5,
        "inquiryClients": [
            {"clientId": 1, "clientName": "客户A"},
            {"clientId": 5, "clientName": "客户B"}
        ],
        "shippingClients": [...],
        "keyDevelopmentClients": [...],
        "responsibilityLevel": "优秀",
        "endOfDayChecklist": {
            "deskOrganized": true,
            "emailsHandled": true,
            "meetingsCompleted": false,
            "materialsReady": true,
            "greetedLeader": true,
            "completedCount": 4,
            "totalCount": 5
        },
        "dailyResults": "今日完成了3个客户的报价工作...",
        "meetingReport": "参加了部门周会，讨论了...",
        "workDiary": "今日主要工作总结：1. 完成客户A的需求分析...",
        "createTime": "2025-06-04T10:30:00+08:00",
        "updateTime": "2025-06-04T15:20:00+08:00"
    }
}
```

### 3. 分页查询日报列表

**接口**: `GET /api/sales-report/page`  
**权限**: 根据角色过滤数据  
**描述**: 分页查询销售日报列表

#### 查询参数
- `pageNum`: 页码，默认1
- `pageSize`: 每页大小，默认10
- `employeeId`: 员工ID（可选，管理员可指定）
- `departmentId`: 部门ID（可选，部门负责人可指定）
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）
- `responsibilityLevel`: 责任心等级（可选）

#### 权限过滤规则
- **employee**: 只能查看自己的日报
- **manager**: 可查看本部门员工的日报
- **admin**: 可查看所有日报

#### 响应示例
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 123,
                "employeeId": 5,
                "employeeName": "张三",
                "departmentName": "销售部",
                "reportDate": "2025-06-04",
                "responsibilityLevel": "优秀",
                "yearlyNewClients": 15,
                "monthlyNewClients": 3,
                "daysSinceLastNewClient": 5,
                "createTime": "2025-06-04T10:30:00+08:00"
            }
        ],
        "total": 100,
        "pageNum": 1,
        "pageSize": 10
    }
}
```

### 4. 获取员工负责的客户列表

**接口**: `GET /api/sales-report/my-clients`  
**权限**: 员工本人  
**描述**: 获取当前员工负责的客户列表，用于日报中的客户选择

#### 响应示例
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "clientId": 1,
            "clientName": "客户A",
            "category": "海运",
            "status": "已合作"
        },
        {
            "clientId": 5,
            "clientName": "客户B", 
            "category": "空运",
            "status": "报价中"
        }
    ]
}
```

### 5. 删除日报

**接口**: `DELETE /api/sales-report/{id}`  
**权限**: 员工本人（只能删除当天的）或管理员  
**描述**: 删除指定的销售日报

#### 路径参数
- `id`: 日报ID

#### 响应示例
```json
{
    "code": 200,
    "message": "删除成功",
    "data": null
}
```

### 6. 获取日报统计数据

**接口**: `GET /api/sales-report/statistics`  
**权限**: 管理员和部门负责人  
**描述**: 获取日报相关的统计数据

#### 查询参数
- `departmentId`: 部门ID（可选）
- `startDate`: 开始日期
- `endDate`: 结束日期

#### 响应示例
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "totalReports": 150,
        "avgResponsibilityScore": 4.2,
        "responsibilityDistribution": {
            "优秀": 80,
            "中等": 50,
            "差": 20
        },
        "avgChecklistCompletion": 0.85,
        "topPerformers": [
            {"employeeName": "张三", "score": 4.8},
            {"employeeName": "李四", "score": 4.6}
        ]
    }
}
```

## 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 数据冲突（如重复提交） |
| 500 | 服务器内部错误 |

## 权限控制

### JWT Token验证
- 所有接口都需要在Header中携带Authorization: Bearer {token}
- Token过期自动返回401，前端跳转登录页

### 角色权限矩阵

| 接口 | employee | manager | admin |
|------|----------|---------|-------|
| 提交日报 | ✅ | ✅ | ✅ |
| 查看个人日报 | ✅(本人) | ✅ | ✅ |
| 日报列表 | ✅(本人) | ✅(本部门) | ✅(全部) |
| 客户列表 | ✅(本人) | ✅ | ✅ |
| 删除日报 | ✅(当天) | ✅ | ✅ |
| 统计数据 | ❌ | ✅(本部门) | ✅(全部) |

---

**更新记录**:
- v1.0 (2025-06-04): 初始API设计，基于RESTful规范和现有系统架构

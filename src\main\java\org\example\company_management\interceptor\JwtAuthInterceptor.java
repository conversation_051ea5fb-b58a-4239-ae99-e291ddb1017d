package org.example.company_management.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.example.company_management.utils.JwtUtil;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证拦截器
 */
@Component
public class JwtAuthInterceptor implements HandlerInterceptor {
    
    private static final String AUTH_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 从请求头中获取token
        String token = request.getHeader(AUTH_HEADER);
        
        // 如果请求头中没有token，尝试从请求参数中获取
        if (token == null || token.isEmpty()) {
            token = request.getParameter("token");
        }
        
        // 如果token存在且以Bearer 开头，去掉前缀
        if (token != null && token.startsWith(TOKEN_PREFIX)) {
            token = token.substring(TOKEN_PREFIX.length());
        }
        
        // 如果token为空，返回401未授权状态码
        if (token == null || token.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"未登录或token已过期\"}");
            return false;
        }
        
        // 验证token是否有效
        if (!JwtUtil.validateToken(token)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"token无效或已过期\"}");
            return false;
        }
        
        // 从token中获取员工信息并存入ThreadLocal
        try {
            Integer employeeId = JwtUtil.getUserId(token);
            String phone = JwtUtil.getUsername(token);
            String name = JwtUtil.getClaim(token, "name");
            String role = JwtUtil.getClaim(token, "role");
            
            // 只存储必要的员工信息到ThreadLocal
            Map<String, Object> employeeInfo = new HashMap<>();
            employeeInfo.put("employeeId", employeeId);
            employeeInfo.put("phone", phone);
            employeeInfo.put("name", name);
            employeeInfo.put("role", role);
            
            // 将员工信息存储到ThreadLocal中
            ThreadLocalUtil.set("employee", employeeInfo);
            // 存储token
            ThreadLocalUtil.set("token", token);
            
            return true;
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"token解析失败\"}");
            return false;
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求结束后清除ThreadLocal中的数据，防止内存泄漏
        ThreadLocalUtil.clear();
    }
} 
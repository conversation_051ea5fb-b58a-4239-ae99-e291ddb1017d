import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import router from './router'
import App from './App.vue'
import './style.css'
// 导入全局组件
import GlobalComponents from './components/index.js'

const app = createApp(App)

app.use(createPinia())
app.use(ElementPlus, {
	locale: zhCn,
	size: 'default',
})
app.use(router)
// 注册全局组件
GlobalComponents(app)

app.mount('#app')

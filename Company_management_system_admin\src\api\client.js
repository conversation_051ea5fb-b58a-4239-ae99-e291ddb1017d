import request from '@/utils/request'

/**
 * 分页查询客户
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.name] 客户名称(可选)
 * @param {number} [params.departmentId] 部门ID(可选)
 * @param {string} [params.employeeName] 员工姓名(可选)
 * @param {string} [params.category] 客户分类(可选)
 * @param {string} [params.status] 审批状态(可选)
 * @param {string} [params.clientStatus] 客户状态(可选)
 * @returns {Promise<any>}
 */
export function getClientPage(params) {
	return request({
		url: '/client/page',
		method: 'get',
		params: {
			page: params.pageNum,
			limit: params.pageSize,
			name: params.name,
			departmentId: params.departmentId,
			employeeName: params.employeeName,
			category: params.category,
			status: params.status,
			clientStatus: params.clientStatus
		},
	})
}

/**
 * 获取所有客户列表
 * @returns {Promise<any>}
 */
export function getClientList() {
	return request({
		url: '/client/all',
		method: 'get',
	})
}

/**
 * 根据ID查询客户
 * @param {number} id 客户ID
 * @returns {Promise<any>}
 */
export function getClientById(id) {
	return request({
		url: `/client/${id}`,
		method: 'get',
	})
}

/**
 * 新增客户
 * @param {Object} data 客户信息
 * @returns {Promise<any>}
 */
export function addClient(data) {
	return request({
		url: '/client',
		method: 'post',
		data,
	})
}

/**
 * 更新客户
 * @param {Object} data 客户信息
 * @returns {Promise<any>}
 */
export function updateClient(data) {
	return request({
		url: '/client',
		method: 'put',
		data,
	})
}

/**
 * 删除客户
 * @param {number} id 客户ID
 * @returns {Promise<any>}
 */
export function deleteClient(id) {
	return request({
		url: `/client/${id}`,
		method: 'delete',
	})
}

/**
 * 根据名称搜索客户
 * @param {string} name 客户名称(模糊搜索)
 * @returns {Promise<any>}
 */
export function searchClientByName(name) {
	return request({
		url: '/client/page',
		method: 'get',
		params: {
			page: 1,
			limit: 10,
			name: name,
		},
	})
}

/**
 * 导出客户数据
 * @param {Object} params - 导出过滤参数
 * @returns {Promise} - 返回请求的Promise对象
 */
export function exportClients(params) {
	return request({
		url: '/client/export',
		method: 'get',
		params,
		responseType: 'blob',
	})
}

/**
 * 更新客户状态
 * @param {Object} data - 更新状态数据
 * @param {number} data.clientId - 客户ID
 * @param {string} data.status - 新状态值
 * @returns {Promise<any>}
 */
export function updateClientStatus(data) {
	return request({
		url: '/client/update-status',
		method: 'post',
		data,
	})
}

/**
 * 批量更新客户状态
 * @param {Object} data - 批量更新状态数据
 * @param {Array<number>} data.clientIds - 客户ID数组
 * @param {string} data.status - 新状态值
 * @returns {Promise<any>}
 */
export function batchUpdateClientStatus(data) {
	return request({
		url: '/client/batch-update-status',
		method: 'post',
		data,
	})
}

/**
 * 获取待审核的客户列表
 * @param {Object} params - 查询参数
 * @returns {Promise<any>}
 */
export function getPendingClients(params) {
	return request({
		url: '/client/pending',
		method: 'get',
		params,
	})
}

/**
 * 获取待审批的客户数量
 * @returns {Promise<any>}
 */
export function getPendingClientCount() {
	return request({
		url: '/client/pending-count',
		method: 'get',
	})
} 
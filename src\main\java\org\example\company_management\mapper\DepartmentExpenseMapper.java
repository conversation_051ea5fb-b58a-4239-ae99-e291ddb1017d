package org.example.company_management.mapper;

import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.DepartmentExpense;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface DepartmentExpenseMapper {
    /**
     * 新增部门开销记录
     *
     * @param departmentExpense 部门开销实体
     */
    void insert(DepartmentExpense departmentExpense);

    /**
     * 分页查询部门开销记录
     *
     * @param params 查询条件 (Map)
     * @return 分页结果
     */
    Page<DepartmentExpense> selectPage(Map<String, Object> params);

    /**
     * 根据ID查询部门开销记录
     *
     * @param id 开销记录ID
     * @return 部门开销实体
     */
    DepartmentExpense selectById(Long id);

    /**
     * 更新部门开销记录
     *
     * @param departmentExpense 部门开销实体
     * @return 影响行数
     */
    int update(DepartmentExpense departmentExpense);

    /**
     * 根据ID删除部门开销记录
     *
     * @param id 开销记录ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据ID列表批量删除部门开销记录
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(List<Long> ids);

    /**
     * 根据部门ID、开销日期和项目名称查询部门开销记录数量
     *
     * @param departmentId 部门ID
     * @param expenseDate 开销日期
     * @param itemName 项目名称
     * @return 部门开销记录数量
     */
    int countByDepartmentIdAndExpenseDateAndItemName(@Param("departmentId") Long departmentId,
                                                       @Param("expenseDate") LocalDate expenseDate,
                                                       @Param("itemName") String itemName);

    /**
     * 分页查询部门开销记录 (用户视图，支持多部门ID)
     *
     * @param params 查询条件 (Map, 包含 departmentIds, startDate, endDate, itemName)
     * @return 分页结果
     */
    Page<DepartmentExpense> selectPageForUserView(Map<String, Object> params);
} 
package org.example.company_management.service;

import org.example.company_management.entity.Employee;
import org.example.company_management.entity.EmployeeNotification;
import org.example.company_management.utils.Result;

import java.util.List;

public interface NotificationService {

    /**
     * 为指定员工生成或更新通知。
     * 此方法将包含核心的通知检查和生成逻辑。
     * @param employee 需要检查通知的员工对象
     */
    void generateNotificationsForEmployee(Employee employee);

    /**
     * 获取指定员工在铃铛中显示的通知列表。
     * @param employeeId 员工ID
     * @return 通知列表 (DTOs or Entities)
     */
    List<EmployeeNotification> getBellNotificationsForEmployee(Integer employeeId);

    /**
     * 处理用户对单个通知的"我知道了"操作。
     * @param notificationId 通知ID
     * @param employeeId 当前操作的员工ID (用于验证权限或记录)
     * @return 操作结果
     */
    Result<?> dismissNotification(Long notificationId, Integer employeeId);

    /**
     * 用户在铃铛中标记通知为已读。
     * @param notificationId 通知ID
     * @param employeeId 当前操作的员工ID
     * @return 操作结果
     */
    Result<?> markAsReadInBell(Long notificationId, Integer employeeId);

    /**
     * 自动清理过期的通知
     * 将超过30天的DISMISSED和ARCHIVED通知进行清理
     */
    void cleanupExpiredNotifications();

    /**
     * 批量处理/dismiss通知。
     * @param notificationIds 要处理的通知ID列表
     * @param employeeId 当前操作的员工ID
     * @return 操作结果
     */
    Result<?> dismissMultipleNotifications(List<Long> notificationIds, Integer employeeId);

    /**
     * 获取应弹窗显示的通知 (如果前端后续仍明确需要此独立接口)。
     * 目前前端主要通过铃铛通知及登录时自动打开通知中心。
     * @param employeeId 员工ID
     * @return 通知列表
     */
    List<EmployeeNotification> getPopupNotificationsForEmployee(Integer employeeId);

    /**
     * 异步为员工生成通知。
     * @param employee 员工对象
     */
    void generateNotificationsForEmployeeAsync(Employee employee);

} 
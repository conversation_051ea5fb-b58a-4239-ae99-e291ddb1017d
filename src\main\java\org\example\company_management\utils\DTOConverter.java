package org.example.company_management.utils;

import org.example.company_management.dto.EmployeeDTO;
import org.example.company_management.entity.Employee;
import org.springframework.beans.BeanUtils;

/**
 * DTO转换工具类
 */
public class DTOConverter {

    /**
     * 将EmployeeDTO转换为Employee实体
     *
     * @param dto EmployeeDTO对象
     * @return Employee实体对象
     */
    public static Employee convertToEmployee(EmployeeDTO dto) {
        if (dto == null) {
            return null;
        }

        Employee employee = new Employee();
        BeanUtils.copyProperties(dto, employee);
        return employee;
    }

    /**
     * 将Employee实体转换为EmployeeDTO
     *
     * @param employee Employee实体对象
     * @return EmployeeDTO对象
     */
    public static EmployeeDTO convertToEmployeeDTO(Employee employee) {
        if (employee == null) {
            return null;
        }

        EmployeeDTO dto = new EmployeeDTO();
        BeanUtils.copyProperties(employee, dto);
        return dto;
    }
} 
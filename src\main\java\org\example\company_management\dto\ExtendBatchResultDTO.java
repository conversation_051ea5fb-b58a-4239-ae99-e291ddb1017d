package org.example.company_management.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtendBatchResultDTO {
    private List<String> successfullyExtendedItems = new ArrayList<>();
    private List<String> skippedDuplicateItems = new ArrayList<>();
    private int totalProcessed;
    private int totalSuccessfullyExtended;
    private int totalSkippedAsDuplicates;
} 
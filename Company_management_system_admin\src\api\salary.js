import request from '@/utils/request'

/**
 * 分页查询工资记录
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页大小
 * @param {String} params.employeeName - 员工姓名（可选）
 * @param {Number} params.departmentId - 部门ID（可选）
 * @param {String} params.yearMonth - 年月（可选）
 * @returns {Promise} - 返回Promise对象
 */
export function getSalaryPage(params) {
	return request({
		url: '/salary/page',
		method: 'get',
		params,
	})
}

/**
 * 根据员工ID和日期获取工资记录
 * @param {Number} employeeId - 员工ID
 * @param {String} date - 年月（格式：YYYY-MM）
 * @returns {Promise} - 返回Promise对象
 */
export function getSalaryByEmployeeIdAndDate(employeeId, date) {
	return request({
		url: `/salary/employee/${employeeId}`,
		method: 'get',
		params: { date },
	})
}

/**
 * 获取指定员工的所有工资记录
 * @param {Number} employeeId - 员工ID
 * @returns {Promise} - 返回Promise对象
 */
export function getSalariesByEmployeeId(employeeId) {
	return request({
		url: `/salary/employee/${employeeId}`,
		method: 'get',
	})
}

/**
 * 根据ID获取工资记录
 * @param {Number} id - 工资记录ID
 * @returns {Promise} - 返回Promise对象
 */
export function getSalaryById(id) {
	return request({
		url: `/salary/${id}`,
		method: 'get',
	})
}

/**
 * 添加工资记录
 * @param {Object} data - 工资记录数据
 * @returns {Promise} - 返回Promise对象
 */
export function addSalary(data) {
	return request({
		url: '/salary',
		method: 'post',
		data,
	})
}

/**
 * 更新工资记录
 * @param {Object} data - 工资记录数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateSalary(data) {
	return request({
		url: '/salary',
		method: 'put',
		data,
	})
}

/**
 * 删除工资记录
 * @param {Number} id - 工资记录ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteSalary(id) {
	return request({
		url: `/salary/${id}`,
		method: 'delete',
	})
}

/**
 * 批量删除工资记录
 * @param {Array} ids - 工资记录ID列表
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteSalary(ids) {
	return request({
		url: '/salary/batch',
		method: 'delete',
		data: ids,
	})
}

/**
 * 获取薪资统计数据
 * @param {Object} params - 统计参数（如按月份、部门等）
 * @returns {Promise} - 返回请求的Promise对象
 */
export function getSalaryStats(params) {
	return request({
		url: '/salary/stats',
		method: 'get',
		params,
	})
}

/**
 * 批量生成薪资记录
 * @param {Object} data - 批量生成参数
 * @returns {Promise} - 返回请求的Promise对象
 */
export function generateSalaryBatch(data) {
	return request({
		url: '/salary/generate',
		method: 'post',
		data,
	})
}

/**
 * 导入工资Excel文件
 * @param {File} file - Excel文件formData
 * @returns {Promise} - 返回Promise对象
 */
export function importSalaries(formData) {
	return request({
		url: '/salary/import',
		method: 'post',
		data: formData,
		headers: {
			'Content-Type': 'multipart/form-data', // 通常el-upload会自动处理，但明确指出有益
		},
	})
}

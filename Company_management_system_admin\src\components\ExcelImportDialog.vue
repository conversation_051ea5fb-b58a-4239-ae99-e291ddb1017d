<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { UploadFilled, Document, Download } from '@element-plus/icons-vue';

// 定义Props
const props = defineProps({
  // 用于v-model绑定对话框可见性
  modelValue: {
    type: Boolean,
    required: true
  },
  // 对话框标题
  title: {
    type: String,
    default: '导入Excel数据'
  },
  // 可接受的文件类型
  acceptFileTypes: {
    type: String,
    default: '.xlsx,.xls'
  },
  // 文件类型描述，用于提示用户
  fileTypeDescription: {
    type: String,
    default: 'Excel文件 (.xlsx, .xls)'
  },
  // 最大文件大小(MB)
  maxFileSizeMB: {
    type: Number,
    default: 200
  },
  // New props for reusability
  importType: { // 'performance', 'salary', etc.
    type: String,
    required: true,
  },
  dialogTitle: {
    type: String,
    default: '导入Excel数据', // Generic default
  },
  templateFileName: { // e.g., '业绩导入模板.xlsx'
    type: String,
    required: true,
  },
  uploadUrl: { // API endpoint for upload
    type: String,
    required: true,
  },
  successMessageFormatter: {
    type: Function,
    default: (importResult) => `数据文件全部导入成功！共处理 ${importResult.processedRows || 0} 行，成功导入 ${importResult.successCount || 0} 条记录。`,
  },
  partialSuccessMessageFormatter: {
    type: Function,
    default: (importResult) => {
      let msg = `数据文件导入处理完成。共处理 ${importResult.processedRows || 0} 行，成功 ${importResult.successCount || 0} 行，失败/跳过 ${importResult.failureCount || 0} 行。`;
      if (importResult.generalErrors && importResult.generalErrors.length > 0) {
        msg += ` 通用错误: ${importResult.generalErrors.join('; ')}`;
      }
      return msg;
    },
  },
});

// 定义Emits
const emits = defineEmits(['update:modelValue', 'submit-import', 'import-success']);

// 内部响应式状态
const uploadRef = ref(null);
const isDraggingOver = ref(false);
const isLoading = ref(false);
const visible = ref(props.modelValue);
const uploading = ref(false);
const uploadProgress = ref(0);
const fileList = ref([]);

// Computed properties for dynamic content based on props
const currentDialogTitle = computed(() => props.dialogTitle);
const currentTemplateFileName = computed(() => props.templateFileName);
const currentUploadUrl = computed(() => props.uploadUrl);

// Base path for templates, adjust if your structure is different
const templateBaseUrl = '/templates/'; 
const fullTemplateUrl = computed(() => `${templateBaseUrl}${currentTemplateFileName.value}`);

// getToken 函数，尝试从 localStorage 获取 token
const getToken = () => {
  const token = localStorage.getItem('token'); // 假设 token 存储的键是 'token'
  return token || ''; // 如果没有找到 token，返回空字符串
};

// Authorization header for upload
const headers = computed(() => {
  const token = getToken();
  if (token) { // 只在 token 存在时添加 Authorization header
    return {
      Authorization: 'Bearer ' + token,
    };
  }
  return {}; // 如果没有 token，返回空对象或根据需要处理
});

watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal;
    if (newVal) {
      // Reset state when dialog opens
      uploading.value = false;
      uploadProgress.value = 0;
      fileList.value = [];
      isLoading.value = false;
    }
  }
);

// 判断文件类型是否为Excel
const isExcelFile = (file) => {
  const name = file.name.toLowerCase();
  const type = file.type;
  return name.endsWith('.xlsx') || 
         name.endsWith('.xls') ||
         type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
         type === 'application/vnd.ms-excel';
};

// 验证文件
const validateFile = (file) => {
  // 检查文件类型
  if (!isExcelFile(file)) {
    ElMessage.error(`请上传${props.fileTypeDescription}`);
    return false;
  }
  
  // 检查文件大小
  const maxSizeBytes = props.maxFileSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    ElMessage.error(`文件大小不能超过 ${props.maxFileSizeMB}MB`);
    return false;
  }
  
  return true;
};

// 关闭对话框
const handleCloseDialog = () => {
  fileList.value = [];
  uploadRef.value?.clearFiles();
  isLoading.value = false;
  uploading.value = false;
  uploadProgress.value = 0;
  emits('update:modelValue', false);
};

// 处理上传组件的文件改变
const handleChangeInUpload = (uploadFile, uploadFiles) => {
  if (uploadFiles.length > 0) {
    const latestFile = uploadFiles[uploadFiles.length - 1];
    const rawFile = latestFile.raw;
    
    if (rawFile && validateFile(rawFile)) {
      fileList.value = [latestFile];
    } else {
      fileList.value = [];
    }
  } else {
    fileList.value = [];
  }
};

// 处理超出文件数量限制
const handleUploadExceed = () => {
  ElMessage.warning('一次只能选择一个文件');
};

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  const items = event.clipboardData?.files;
  
  if (items && items.length === 1) {
    const pastedFileRaw = items[0];
    
    if (validateFile(pastedFileRaw)) {
      const newPastedUploadFile = {
        name: pastedFileRaw.name,
        raw: pastedFileRaw,
        uid: Date.now() + Math.random(),
        size: pastedFileRaw.size,
        status: 'ready',
      };
      fileList.value = [newPastedUploadFile];
      ElMessage.success(`已通过粘贴选择文件: ${pastedFileRaw.name}`);
    }
  } else if (items && items.length > 1) {
    ElMessage.warning('一次只能粘贴一个文件。');
  }
};

// 处理提交导入
const submitUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件。');
    return;
  }
  if (uploadRef.value && typeof uploadRef.value.submit === 'function') {
    uploadRef.value.submit();
  } else {
    ElMessage.error('上传组件初始化失败，请刷新页面重试。');
  }
};

// 处理拖拽事件
const onDragEnter = () => {
  isDraggingOver.value = true;
};

const onDragLeave = () => {
  isDraggingOver.value = false;
};

const onDrop = () => {
  isDraggingOver.value = false;
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const handleBeforeUpload = (file) => {
  uploading.value = true;
  uploadProgress.value = 0;
  return true;
};

const handleUploadProgress = (event) => {
  if (event && typeof event.percent === 'number') {
    uploadProgress.value = Math.floor(event.percent);
  } else if (event && event.total > 0) {
    uploadProgress.value = parseInt(String((event.loaded / event.total) * 100), 10);
  }
};

const handleUploadSuccess = (response, uploadFile) => {
  uploading.value = false;
  uploadProgress.value = 100;
  if (response.code === 200) {
    const importResult = response.data;
    let message = '';
    if (importResult.failureCount > 0 || (importResult.generalErrors && importResult.generalErrors.length > 0)) {
      message = props.partialSuccessMessageFormatter(importResult);
      ElMessage.warning({ message, duration: 5000, showClose: true });
    } else {
      message = props.successMessageFormatter(importResult);
      ElMessage.success({ message, duration: 3000 });
    }
    emits('import-success', importResult, props.importType);
    emits('update:modelValue', false);
  } else {
    ElMessage.error(response.message || '文件上传处理失败，请检查文件内容或联系管理员。');
  }
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
  fileList.value = [];
};

const handleUploadError = (error, uploadFile) => {
  uploading.value = false;
  let errorMessage = '文件上传失败，请重试。';
  try {
    let errorData = error;
    if (error.message) {
        try {
            errorData = JSON.parse(error.message);
        } catch (e) {
            errorMessage = error.message;
        }
    }

    if (errorData && errorData.message) {
      errorMessage = errorData.message;
    } else if (error.response && error.response.data && error.response.data.message) {
      errorMessage = error.response.data.message;
    } else if (typeof error === 'string') {
        errorMessage = error;
    }

  } catch (e) {
    if (error && error.message) {
        errorMessage = error.message;
    }
  }
  ElMessage.error(errorMessage);
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
  fileList.value = [];
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="currentDialogTitle"
    width="600px"
    :before-close="handleCloseDialog"
    destroy-on-close
    @paste.prevent.stop="handlePaste"
    class="excel-import-dialog"
    append-to-body
    :close-on-click-modal="!uploading"
    :close-on-press-escape="!uploading"
  >
    <div class="import-container">
      <el-upload
        ref="uploadRef"
        drag
        :action="currentUploadUrl"
        :headers="headers"
        :file-list="fileList"
        :auto-upload="false"
        :limit="1"
        :accept="acceptFileTypes"
        :on-change="handleChangeInUpload"
        :on-exceed="handleUploadExceed"
        :before-upload="handleBeforeUpload"
        :on-progress="handleUploadProgress"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        @dragenter="onDragEnter"
        @dragleave="onDragLeave"
        @drop="onDrop"
        :class="{ 'is-dragging-over': isDraggingOver }"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 {{ fileTypeDescription }}，且不超过 {{ maxFileSizeMB }}MB
          </div>
          <div class="el-upload__tip paste-tip">
            <strong>提示：</strong>您也可以从文件管理器复制Excel文件后，按 Ctrl+V 粘贴到此区域
            <br>
            <strong>提示：</strong>请根据管理员提供的Excel模板格式进行上传
          </div>
        </template>
      </el-upload>

      <div v-if="fileList.length > 0 && fileList[0]" class="selected-file-info">
        <h4>已选择文件：</h4>
        <p>
          <el-icon><document /></el-icon>
          <span class="file-name">{{ fileList[0].name }}</span>
          <span class="file-size">({{ formatFileSize(fileList[0].size) }})</span>
        </p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog" :disabled="uploading">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploading" :disabled="uploading || fileList.length === 0">
          {{ uploading ? '上传中...' : '确 定 上 传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.excel-import-dialog {
  /* 对话框样式 */
  width: 600px;
}

.import-container {
  padding: 20px;
}

.el-upload {
  width: 100%;
  transition: all 0.3s;
}

.el-upload-dragger {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
}

.el-icon--upload {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.el-upload__text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.el-upload__text em {
  color: #409EFF;
  font-style: normal;
  cursor: pointer;
}

.el-upload__tip {
  color: #909399;
  font-size: 14px;
  margin: 8px 0;
  text-align: center;
}

.paste-tip {
  margin-top: 12px;
  padding: 8px;
  background-color: #ecf8ff;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  text-align: left;
}

.is-dragging-over .el-upload-dragger {
  border: 2px dashed #409EFF;
  background-color: #ecf5ff;
}

.selected-file-info {
  margin-top: 24px;
  padding: 16px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.selected-file-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.selected-file-info p {
  display: flex;
  align-items: center;
  margin: 0;
  color: #606266;
}

.file-name {
  margin: 0 8px;
  font-weight: 500;
}

.file-size {
  color: #909399;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style> 
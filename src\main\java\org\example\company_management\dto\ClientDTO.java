package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.example.company_management.validation.ValidationGroups;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户DTO，用于前端和后端之间的数据传输
 */
@Data
public class ClientDTO {
    /**
     * 客户ID
     * 添加时不需要，修改时必须
     */
    @NotNull(message = "客户ID不能为空", groups = {ValidationGroups.Update.class})
    private Integer clientId;

    /**
     * 客户名称
     * 添加和修改时都必须
     */
    @NotBlank(message = "客户名称不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(min = 2, max = 50, message = "客户名称长度必须在2-50个字符之间", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String name;

    /**
     * 客户邮箱
     * 添加和修改时都必须
     */
    @NotBlank(message = "客户邮箱不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Email(message = "邮箱格式不正确", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(max = 100, message = "邮箱长度不能超过100个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String email;

    /**
     * 客户电话
     * 添加和修改时都必须
     */
    @NotBlank(message = "客户电话不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String phone;

    /**
     * 负责员工ID
     * 可选字段
     */
    private Integer employeeId;

    /**
     * 客户分类（海运、散货、空运、快递等）
     * 添加和修改时都必须
     */
    @NotBlank(message = "客户分类不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(max = 20, message = "客户分类长度不能超过20个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String category;

    /**
     * 合作状态（审核中、已拒绝、报价中、已合作）
     * 添加和修改时都必须
     */
    @NotBlank(message = "合作状态不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(max = 20, message = "合作状态长度不能超过20个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String status;

    @Size(max = 50, message = "联系人名称长度不能超过50个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String contactPerson;

    @Size(max = 255, message = "备注长度不能超过255个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String remark;

    /**
     * 负责员工姓名
     * 仅用于展示，不参与数据传输
     */
    private String employeeName;

    /**
     * 拒绝备注
     */
    private String rejectRemark;

    /**
     * 操单时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;

    /**
     * 创建时间
     * 仅用于返回给前端展示，不用于接收前端数据
     */
    private Date createTime;

    /**
     * 更新时间
     * 仅用于返回给前端展示，不用于接收前端数据
     */
    private Date updateTime;
} 
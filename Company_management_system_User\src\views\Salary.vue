<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { getEmployeeSalaries, getSalaryDetail } from '../api/salary';
import { ElMessage, ElLoading } from 'element-plus';
import { Search, RefreshRight, Calendar } from '@element-plus/icons-vue';

// 工资数据
const salaryList = ref([]);
const loading = ref(false);
// 选中的日期
const selectedMonth = ref('');
// 详情数据
const salaryDetail = ref(null);
// 详情对话框
const detailDialogVisible = ref(false);

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    // 检查日期是否为YYYY-MM格式
    const datePattern = /^\d{4}-\d{2}$/;
    if (datePattern.test(dateString)) {
        const [year, month] = dateString.split('-');
        return `${year}年${month}月`;
    }
    return dateString;
};

// 加载工资列表
const loadSalaryList = async () => {
    try {
        loading.value = true;
        let params = {
            page: pagination.page,
            size: pagination.size,
        };

        if (selectedMonth.value) {
            // 转换日期格式 YYYY-MM-DD -> YYYY-MM
            params.date = selectedMonth.value.substring(0, 7);
        }

        const response = await getEmployeeSalaries(params);

        if (response.code === 200) {
            salaryList.value = response.data || [];
            if (response.data && typeof response.data === 'object') {
                // 处理返回的分页数据
                if (Array.isArray(response.data.records)) {
                    salaryList.value = response.data.records;
                    pagination.total = response.data.total || 0;
                } else {
                    salaryList.value = response.data;
                    pagination.total = response.data.length || 0;
                }
            }
        } else {
            ElMessage.error(response.message || '获取工资数据失败');
        }
    } catch (error) {
        console.error('获取工资列表失败:', error);
        ElMessage.error('获取工资列表失败，请稍后再试');
    } finally {
        loading.value = false;
    }
};

// 根据日期过滤工资
const filterByMonth = async () => {
    pagination.page = 1; // 重置页码
    await loadSalaryList();
};

// 重置过滤条件
const resetFilter = async () => {
    selectedMonth.value = '';
    pagination.page = 1; // 重置页码
    await loadSalaryList();
};

// 查看详情
const viewDetail = async (date) => {
    try {
        loading.value = true;
        const response = await getSalaryDetail(date);

        if (response.code === 200) {
            salaryDetail.value = response.data;
            detailDialogVisible.value = true;
        } else {
            ElMessage.error(response.message || '获取工资详情失败');
        }
    } catch (error) {
        console.error('获取工资详情失败:', error);
        ElMessage.error('获取工资详情失败，请稍后再试');
    } finally {
        loading.value = false;
    }
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadSalaryList();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadSalaryList();
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 初始加载
onMounted(() => {
    loadSalaryList();
});
</script>

<template>
    <div class="salary-container">
        <!-- 搜索工具栏 -->
        <div class="toolbar">
            <div class="filter-actions">
                <el-date-picker
                    v-model="selectedMonth"
                    type="month"
                    placeholder="选择月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM-DD"
                    :prefix-icon="Calendar"
                    @change="filterByMonth"
                    clearable
                />
                <el-button
                    @click="resetFilter"
                    :disabled="!selectedMonth"
                >
                    <el-icon>
                        <RefreshRight />
                    </el-icon>
                    重置
                </el-button>
            </div>
        </div>

        <!-- 工资表格 -->
        <el-table
            :data="salaryList"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            row-key="id"
            :max-height="'calc(100vh - 280px)'"
            class="custom-table"
        >
            <el-table-column
                label="年月"
                prop="date"
                width="120"
                align="center"
            >
                <template #default="{ row }">
                    <el-tag type="info">{{ formatDate(row.date) }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="基本工资"
                prop="basicSalary"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.basicSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="奖金"
                prop="performanceBonus"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.performanceBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="全勤奖"
                prop="fullAttendanceBonus"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.fullAttendanceBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="业务与操作奖金"
                prop="businessOperationBonus"
                min-width="130" 
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.businessOperationBonus) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实得金额"
                prop="sumSalary"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.sumSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="请假"
                prop="leaveDeduction"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.leaveDeduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="扣款" 
                prop="deduction"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.deduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="迟到与缺卡"
                prop="lateDeduction"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.lateDeduction) }}
                </template>
            </el-table-column>

            <el-table-column
                label="社会保险费个人部分"
                prop="socialSecurityPersonal"
                min-width="150" 
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.socialSecurityPersonal) }}
                </template>
            </el-table-column>

            <el-table-column
                label="公积金"
                prop="providentFund"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.providentFund) }}
                </template>
            </el-table-column>

            <el-table-column
                label="代扣代缴个税"
                prop="tax"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.tax) }}
                </template>
            </el-table-column>

            <el-table-column
                label="水电费"
                prop="waterElectricityFee"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.waterElectricityFee) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实发工资"
                prop="actualSalary"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualSalary) }}
                </template>
            </el-table-column>

            <el-table-column
                label="报销"
                prop="reimbursement"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.reimbursement) }}
                </template>
            </el-table-column>

            <el-table-column
                label="私帐"
                prop="privateAccount"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.privateAccount) }}
                </template>
            </el-table-column>

            <el-table-column
                label="合计"
                prop="totalSalary"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    <span class="total-salary">{{ formatCurrency(row.totalSalary) }}</span>
                </template>
            </el-table-column>

            <el-table-column
                label="备注"
                prop="remark"
                min-width="150"
                align="left"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ row.remark || '-' }}
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                width="120"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <el-button
                        type="primary"
                        size="small"
                        @click="viewDetail(row.date)"
                    >
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <div
            v-if="salaryList.length === 0 && !loading"
            class="empty-block"
        >
            <el-empty description="暂无工资数据" />
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 详情对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="工资详情"
            width="520px"
            destroy-on-close
            class="custom-dialog"
            :close-on-click-modal="false"
        >
            <div
                v-if="salaryDetail"
                class="detail-container"
            >
                <div class="detail-header">
                    <span class="date-label">{{ formatDate(salaryDetail.date) }}</span>
                    <span class="total-amount">{{ formatCurrency(salaryDetail.totalSalary) }}</span>
                </div>

                <div class="detail-content">
                    <div class="detail-item">
                        <span class="detail-label">基本工资</span>
                        <span class="detail-value">{{ formatCurrency(salaryDetail.basicSalary) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">奖金</span>
                        <span class="detail-value bonus">{{ formatCurrency(salaryDetail.performanceBonus) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">全勤奖</span>
                        <span class="detail-value full-attendance-bonus">{{ formatCurrency(salaryDetail.fullAttendanceBonus) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">业务与操作奖金</span>
                        <span class="detail-value business-operation-bonus">{{ formatCurrency(salaryDetail.businessOperationBonus) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">实得金额</span>
                        <span class="detail-value sum-salary">{{ formatCurrency(salaryDetail.sumSalary) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">请假</span>
                        <span class="detail-value leave-deduction">{{ formatCurrency(salaryDetail.leaveDeduction) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">扣款</span>
                        <span class="detail-value deduction">{{ formatCurrency(salaryDetail.deduction) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">迟到与缺卡</span>
                        <span class="detail-value late-deduction">{{ formatCurrency(salaryDetail.lateDeduction) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">社会保险费个人部分</span>
                        <span class="detail-value social-security-personal">{{ formatCurrency(salaryDetail.socialSecurityPersonal) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">公积金</span>
                        <span class="detail-value provident-fund">{{ formatCurrency(salaryDetail.providentFund) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">代扣代缴个税</span>
                        <span class="detail-value tax">{{ formatCurrency(salaryDetail.tax) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">水电费</span>
                        <span class="detail-value water-electricity-fee">{{ formatCurrency(salaryDetail.waterElectricityFee) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">实发工资</span>
                        <span class="detail-value actual-salary">{{ formatCurrency(salaryDetail.actualSalary) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">报销</span>
                        <span class="detail-value reimbursement">{{ formatCurrency(salaryDetail.reimbursement) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">私帐</span>
                        <span class="detail-value private-account">{{ formatCurrency(salaryDetail.privateAccount) }}</span>
                    </div>
                    <el-divider></el-divider>
                    <div class="detail-item">
                        <span class="detail-label">合计</span>
                        <span class="detail-value total-salary">{{ formatCurrency(salaryDetail.totalSalary) }}</span>
                    </div>
                    <div class="detail-item remark-item">
                        <span class="detail-label">备注</span>
                        <span class="detail-value remark-value">{{ salaryDetail.remark || '-' }}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <div class="summary-item">
                        <span>工资合计</span>
                        <span class="summary-value">{{ formatCurrency(salaryDetail.totalSalary) }}</span>
                    </div>
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style scoped>
.salary-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.empty-block {
    padding: 30px 0;
    text-align: center;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.total-salary {
    font-weight: bold;
    color: #303133;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__header-wrapper th) {
    background-color: #f5f7fa;
    font-weight: 600;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 24px;
}

:deep(.custom-dialog .el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
}

/* 详情弹窗样式 */
.detail-container {
    padding: 0;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.date-label {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
}

.total-amount {
    font-size: 22px;
    font-weight: 700;
    color: #67c23a;
}

.detail-content {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-size: 15px;
    color: #606266;
}

.detail-value {
    font-size: 16px;
    color: #303133;
    font-weight: 500;
}

.detail-value.bonus {
    color: #409eff;
}

.detail-value.full-attendance-bonus {
    color: #e6a23c;
}

.detail-value.business-operation-bonus {
    color: #529b2e;
}

.detail-value.sum-salary {
    color: #67c23a;
}

.detail-value.leave-deduction {
    color: #f56c6c;
}

.detail-value.deduction {
    color: #f56c6c;
}

.detail-value.late-deduction {
    color: #e6a23c;
}

.detail-value.social-security-personal {
    color: #67c23a;
}

.detail-value.provident-fund {
    color: #67c23a;
}

.detail-value.tax {
    color: #f56c6c;
}

.detail-value.water-electricity-fee {
    color: #67c23a;
}

.detail-value.actual-salary {
    color: #67c23a;
}

.detail-value.reimbursement {
    color: #67c23a;
}

.detail-value.private-account {
    color: #67c23a;
}

.detail-item.remark-item {
    align-items: flex-start;
}

.detail-value.remark-value {
    white-space: pre-wrap;
    word-break: break-all;
    text-align: right;
    flex-grow: 1;
    margin-left: 10px;
}

.summary-section {
    margin-top: 20px;
    background-color: #ecf8f0;
    border-radius: 8px;
    padding: 15px 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #303133;
}

.summary-value {
    font-size: 18px;
    font-weight: 700;
    color: #67c23a;
}

:deep(.el-divider--horizontal) {
    margin: 16px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-actions {
        width: 100%;
        margin-top: 10px;
    }

    .pagination-container {
        position: static;
        margin-top: 20px;
        width: 100%;
        display: flex;
        justify-content: center;
    }
}
</style> 
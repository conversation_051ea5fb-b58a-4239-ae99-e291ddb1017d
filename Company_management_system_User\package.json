{"name": "company-management-system-user", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "element-plus": "^2.9.7", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "terser": "^5.39.0", "vite": "^6.2.0"}}
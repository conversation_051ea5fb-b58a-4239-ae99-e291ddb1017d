package org.example.company_management.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 备用金实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PettyCash {
    private Integer id;
    private Integer employeeId;
    private String employeeName; // 非数据库字段，用于显示
    private String department; // 非数据库字段，用于显示
    private String position; // 非数据库字段，用于显示
    private Integer departmentId; // 非数据库字段，用于关联查询
    private String purpose;
    private BigDecimal amount;
    private String status;
    private String date; // Format YYYY-MM, as per init.sql change (added in a previous migration)
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 
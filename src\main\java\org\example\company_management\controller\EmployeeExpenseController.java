package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.BatchExtendExpenseRequestDTO;
import org.example.company_management.dto.EmployeeExpenseDTO;
import org.example.company_management.dto.ExtendBatchResultDTO;
import org.example.company_management.service.EmployeeExpenseService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 员工费用管理 Controller
 */
@RestController
@RequestMapping("/employee-expense")
@Slf4j
public class EmployeeExpenseController {

    @Autowired
    private EmployeeExpenseService employeeExpenseService;

    /**
     * 新增员工费用 (支持单条或批量基于DTO内容)
     *
     * @param employeeExpenseDTO 员工费用数据 (可能包含单条或批量信息)
     * @return Result 通用返回结果
     */
    @PostMapping
    public Result createEmployeeExpense(@Validated @RequestBody EmployeeExpenseDTO employeeExpenseDTO) {
        log.info("创建员工费用请求 (单条或批量): {}", employeeExpenseDTO);
        employeeExpenseService.createSingleOrBatch(employeeExpenseDTO);
        return Result.success();
    }

    /**
     * 分页查询员工费用
     *
     * @param pageNum      分页页码
     * @param pageSize     每页数量
     * @param employeeId   员工ID (可选)
     * @param employeeName 员工姓名 (可选)
     * @param startDate    费用日期开始 (可选, YYYY-MM-DD)
     * @param endDate      费用日期结束 (可选, YYYY-MM-DD)
     * @param itemName     项目名称 (可选)
     * @return Result<PageResult> 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult> getEmployeeExpensePage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) Long employeeId,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String itemName) {

        log.info("分页查询员工费用, pageNum: {}, pageSize: {}, employeeId: {}, employeeName: {}, startDate: {}, endDate: {}, itemName: {}",
                pageNum, pageSize, employeeId, employeeName, startDate, endDate, itemName);

        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        if (employeeId != null) {
            params.put("employeeId", employeeId);
        }
        if (employeeName != null && !employeeName.isEmpty()) {
            params.put("employeeName", employeeName);
        }
        if (startDate != null) {
            params.put("startDate", startDate);
        }
        if (endDate != null) {
            params.put("endDate", endDate);
        }
        if (itemName != null && !itemName.isEmpty()) {
            params.put("itemName", itemName);
        }

        PageResult pageResult = employeeExpenseService.getPage(params);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询员工费用
     *
     * @param id 员工费用ID
     * @return Result<EmployeeExpenseDTO> 员工费用数据
     */
    @GetMapping("/{id}")
    public Result<EmployeeExpenseDTO> getEmployeeExpenseById(@PathVariable Long id) {
        log.info("根据ID查询员工费用, ID: {}", id);
        EmployeeExpenseDTO employeeExpenseDTO = employeeExpenseService.getById(id);
        return Result.success(employeeExpenseDTO);
    }

    /**
     * 修改员工费用
     *
     * @param employeeExpenseDTO 员工费用数据
     * @return Result 通用返回结果
     */
    @PutMapping
    public Result updateEmployeeExpense(@Validated(ValidationGroups.Update.class) @RequestBody EmployeeExpenseDTO employeeExpenseDTO) {
        log.info("修改员工费用: {}", employeeExpenseDTO);
        employeeExpenseService.update(employeeExpenseDTO);
        return Result.success();
    }

    /**
     * 根据ID删除员工费用
     *
     * @param id 员工费用ID
     * @return Result 通用返回结果
     */
    @DeleteMapping("/{id}")
    public Result deleteEmployeeExpenseById(@PathVariable Long id) {
        log.info("根据ID删除员工费用, ID: {}", id);
        employeeExpenseService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除员工费用
     *
     * @param ids ID列表
     * @return Result 通用返回结果
     */
    @PostMapping("/batch-delete")
    public Result batchDeleteEmployeeExpense(@RequestBody List<Long> ids) {
        log.info("批量删除员工费用, IDs: {}", ids);
        employeeExpenseService.batchDeleteByIds(ids);
        return Result.success();
    }

    /**
     * 批量延用员工费用 (基于选择的现有费用复制到新的月份)
     *
     * @param batchExtendExpenseRequestDTO 包含多个待创建费用项的请求体
     * @return Result<ExtendBatchResultDTO> 通用返回结果，包含操作详情
     */
    @PostMapping("/extend-batch")
    public Result<ExtendBatchResultDTO> extendBatchEmployeeExpenses(@Validated @RequestBody BatchExtendExpenseRequestDTO batchExtendExpenseRequestDTO) {
        log.info("批量延用员工费用接口请求，共 {} 项", batchExtendExpenseRequestDTO.getItems() != null ? batchExtendExpenseRequestDTO.getItems().size() : 0);
        ExtendBatchResultDTO resultDetails = employeeExpenseService.extendBatch(batchExtendExpenseRequestDTO);
        return Result.success(resultDetails);
    }

    /**
     * 分页查询员工费用 (用户视图)
     *
     * @param requestBody 包含查询参数的请求体
     * @return Result<PageResult> 分页结果
     */
    @PostMapping("/user-view/page")
    public Result<PageResult> getUserViewEmployeeExpensePage(
            @RequestBody Map<String, Object> requestBody) {

        log.info("用户视图分页查询员工费用, 请求参数: {}", requestBody);

        Map<String, Object> params = new HashMap<>();

        // 将前端参数映射到后端参数
        params.put("pageNum", requestBody.getOrDefault("page", 1));
        params.put("pageSize", requestBody.getOrDefault("size", 10));

        if (requestBody.containsKey("departmentIds") && requestBody.get("departmentIds") != null) {
            params.put("departmentIds", requestBody.get("departmentIds"));
        }

        if (requestBody.containsKey("employeeName") && requestBody.get("employeeName") != null) {
            String employeeName = requestBody.get("employeeName").toString();
            if (!employeeName.isEmpty()) {
                params.put("employeeName", employeeName);
            }
        }

        if (requestBody.containsKey("itemName") && requestBody.get("itemName") != null) {
            String itemName = requestBody.get("itemName").toString();
            if (!itemName.isEmpty()) {
                params.put("itemName", itemName);
            }
        }

        if (requestBody.containsKey("date") && requestBody.get("date") != null) {
            String month = requestBody.get("date").toString();
            if (!month.isEmpty()) {
                params.put("month", month);
            }
        }

        PageResult pageResult = employeeExpenseService.getPageForUserView(params);
        return Result.success(pageResult);
    }
} 
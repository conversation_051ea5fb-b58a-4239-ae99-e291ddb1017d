# 销售日报数据库设计

**版本**: v1.0  
**创建时间**: 2025-06-04 10:46:28 +08:00  
**设计负责人**: AR  
**审核状态**: 待审核

## 设计原则

- **KISS原则**: 保持设计简单直接，避免过度复杂化
- **YAGNI原则**: 只实现当前明确需求，避免预设未来功能
- **一致性**: 与现有数据库设计保持命名和结构一致性
- **性能优化**: 合理设计索引，优化查询性能

## 主表设计

### sales_daily_report (销售日报表)

```sql
CREATE TABLE IF NOT EXISTS `sales_daily_report` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日报ID',
    `employee_id` INT NOT NULL COMMENT '员工ID',
    `report_date` DATE NOT NULL COMMENT '日报日期',
    
    -- 统计字段（系统自动计算）
    `yearly_new_clients` INT NOT NULL DEFAULT 0 COMMENT '年度新客户总数',
    `monthly_new_clients` INT NOT NULL DEFAULT 0 COMMENT '当月新客户总数', 
    `days_since_last_new_client` INT NOT NULL DEFAULT 0 COMMENT '距离上次出新客户天数',
    
    -- 客户选择字段（JSON格式存储客户ID数组）
    `inquiry_clients` JSON COMMENT '询价客户ID列表',
    `shipping_clients` JSON COMMENT '出货客户ID列表',
    `key_development_clients` JSON COMMENT '重点开发客户ID列表',
    
    -- 评估字段
    `responsibility_level` ENUM('优秀', '中等', '差') NOT NULL COMMENT '责任心评级',
    
    -- 工作检查清单（JSON格式存储）
    `end_of_day_checklist` JSON COMMENT '下班准备工作检查清单',
    
    -- 文本输入字段
    `daily_results` TEXT COMMENT '今日效果',
    `meeting_report` TEXT COMMENT '会议报告',
    `work_diary` TEXT COMMENT '工作日记',
    
    -- 系统字段
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `report_date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee`(`employee_id`) ON DELETE CASCADE,
    
    -- 索引优化
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_report_date` (`report_date`),
    INDEX `idx_employee_date` (`employee_id`, `report_date`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售日报表';
```

## JSON字段结构定义

### inquiry_clients / shipping_clients / key_development_clients
```json
{
    "clientIds": [1, 5, 12, 23],
    "lastUpdated": "2025-06-04T10:46:28+08:00"
}
```

### end_of_day_checklist
```json
{
    "items": {
        "deskOrganized": true,
        "emailsHandled": false,
        "meetingsCompleted": true,
        "materialsReady": true,
        "greetedLeader": false
    },
    "completedCount": 3,
    "totalCount": 5,
    "lastUpdated": "2025-06-04T10:46:28+08:00"
}
```

## 数据约束

### 业务约束
- 每个员工每天只能有一条日报记录（通过唯一索引保证）
- 责任心评级必须为预定义的枚举值
- 日报日期不能是未来日期
- 客户ID必须在client表中存在（应用层验证）

### 数据完整性
- employee_id外键约束确保员工存在
- JSON字段格式验证（应用层实现）
- 统计字段非负数验证

## 查询优化策略

### 常用查询模式
1. **按员工查询日报**: 使用 `idx_employee_id` 索引
2. **按日期范围查询**: 使用 `idx_report_date` 索引  
3. **员工特定日期查询**: 使用 `uk_employee_date` 唯一索引
4. **最近日报查询**: 使用 `idx_create_time` 索引

### JSON字段查询
```sql
-- 查询包含特定客户的日报
SELECT * FROM sales_daily_report 
WHERE JSON_CONTAINS(inquiry_clients->'$.clientIds', '5');

-- 查询检查清单完成度
SELECT * FROM sales_daily_report 
WHERE JSON_EXTRACT(end_of_day_checklist, '$.completedCount') >= 4;
```

## 统计字段计算逻辑

### yearly_new_clients (年度新客户总数)
```sql
SELECT COUNT(*) FROM client 
WHERE employee_id = ? 
AND YEAR(create_time) = YEAR(CURDATE())
AND status = '已合作';
```

### monthly_new_clients (当月新客户总数)  
```sql
SELECT COUNT(*) FROM client 
WHERE employee_id = ? 
AND YEAR(create_time) = YEAR(CURDATE())
AND MONTH(create_time) = MONTH(CURDATE())
AND status = '已合作';
```

### days_since_last_new_client (距离上次出新客户天数)
```sql
SELECT DATEDIFF(CURDATE(), MAX(create_time)) 
FROM client 
WHERE employee_id = ? 
AND status = '已合作';
```

## 性能考虑

### 预期数据量
- 假设100个员工，每天1条记录
- 年数据量：100 × 365 = 36,500条
- 5年数据量：约18万条

### 性能优化
- 主键使用BIGINT支持大数据量
- 合理的索引设计覆盖常用查询
- JSON字段查询需要注意性能，必要时可添加虚拟列索引

## 扩展性设计

### 向后兼容
- JSON字段支持灵活扩展
- 可通过ALTER TABLE添加新字段
- 索引可根据查询需求动态调整

### 升级路径
- 如需复杂统计，可考虑添加汇总表
- 如需审计功能，可添加操作日志表
- 如需工作流，可添加审批状态字段

---

**更新记录**:
- v1.0 (2025-06-04): 初始设计，基于方案一的简化单表设计

# 公司管理系统 - 后端服务

## 项目概述

公司管理系统是一个全面的企业内部管理平台，为企业提供员工、部门、职位、薪资、业绩、客户管理以及智能通知提醒等核心业务管理功能。系统采用前后端分离架构，本仓库包含后端服务的源代码。

系统设计目标是提供完整的公司内部业务管理能力，包括人力资源管理、组织架构管理、业绩考核、薪资管理等，帮助企业实现数字化管理转型，提高管理效率。

## 技术架构

### 开发环境

- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Spring Boot 3.x

### 技术栈

- **核心框架**：Spring Boot
- **持久层**：MyBatis + MySQL
- **安全认证**：JWT (JSON Web Token) - 主要通过手机号进行认证，通过 `JwtAuthInterceptor` 实现，并使用 `JwtUtil` 进行令牌管理。
- **日志管理**：Logback
- **API文档**：基于代码注释的文档
- **分页插件**：PageHelper
- **数据校验**：Jakarta Validation，并使用校验组 (`ValidationGroups.java`) 区分不同操作场景的校验规则。
- **权限控制**：基于拦截器的权限控制
- **工具库**：Lombok
- **统一响应格式**：使用 `Result.java` 封装API响应，`PageResult.java` 处理分页数据。
- **全局异常处理**：通过 `GlobalExceptionHandler.java` 统一处理系统异常。
- **密码加密**：使用MD5算法 (`Md5Util.java`) 加密存储用户密码。
- **请求上下文工具**：使用 `ThreadLocalUtil` 在请求处理链路中传递用户信息。
- **API日志记录**：通过AOP (`ApiLogAspect.java`) 实现对API请求和响应的日志记录。
- **异步与定时任务**：使用Spring Boot的 `@Async` 实现异步方法执行（例如，登录时异步生成通知），通过 `@EnableScheduling` 和 `@Scheduled` 实现定时任务（例如，每日定时生成通知）。

### 项目结构

```
src/main/java/org/example/company_management/
├── aspect          # 切面（用于日志、权限等横切关注点）
├── config          # 配置类
├── controller      # 控制器层（API接口）
├── dto             # 数据传输对象
├── entity          # 实体类（数据模型）
├── exception       # 自定义异常处理
├── interceptor     # 拦截器（认证、权限等）
├── mapper          # 数据访问层接口
├── service         # 业务逻辑层接口
│   └── impl        # 业务逻辑实现类
├── task            # 定时任务 (如通知生成)
├── utils           # 工具类
└── validation      # 数据验证
```

## 功能模块

系统已实现的主要功能模块包括：

### 1. 认证与权限管理

- 基于手机号和密码的管理员和员工登录
- 基于JWT的身份验证
- 个人信息维护（支持手机号更新含唯一性校验，邮箱更新可选，身份证信息不可编辑但仍可查看）
- 基于角色的权限控制（ADMIN/USER）

### 2. 员工管理

- 员工信息的CRUD操作 (包含必填且唯一的手机号字段；添加员工时邮箱字段可选)
- 员工状态管理（在职/离职）
- 员工部门与职位关联
- 员工列表与详情查询
- 按部门筛选员工

### 3. 部门管理

- 部门信息的CRUD操作
- 部门负责人设置
- 部门层级结构（父子部门）
- 部门状态管理（启用/禁用）
- 按部门负责人查询部门

### 4. 职位管理

- 职位信息的CRUD操作
- 职位与部门关联
- 职位薪资范围设置
- 职位状态管理

### 5. 薪资管理

- 员工薪资记录管理
- 薪资历史记录查询
- 薪资统计与报表
- 按部门、时间筛选薪资记录

### 6. 业绩管理

- 员工业绩考核管理
- 业绩等级设置
- 业绩周期管理
- 业绩统计与分析

### 7. 客户管理

- 客户信息的CRUD操作
- 客户分类与标签
- 客户联系人管理
- 客户状态跟踪

### 8. 备用金管理

- 备用金申请与审批
- 备用金使用记录
- 备用金统计报表
- 按部门、时间筛选备用金记录

### 9. 仪表盘

- 系统概览统计数据
- 员工、部门、职位数量统计
- 业绩数据汇总展示

### 10. 智能通知提醒系统

- **目的**: 为特定职位（当前主要为"业务员"）的员工提供基于预定义规则的自动化通知与提醒。
- **规则驱动**: 系统通知的生成逻辑基于 `notification_rules` 表中定义的规则，每条规则包含条件类型、目标职位、消息模板、提醒间隔等。
- **条件触发类型**:
    - `PERFORMANCE_VS_SALARY`: 上月业绩低于特定计算方法得出的应发工资。
    - `NEW_CLIENTS_LAST_MONTH`: 上月无新增"已合作"状态的客户。
    - `NEW_CLIENTS_THIS_MONTH_AFTER_15TH`: 本月时间过半（15号之后）仍无新增"已合作"状态的客户。
    - `ALWAYS_TRIGGER`: 规则始终触发（例如，用于发送欢迎消息）。
- **动态消息**: 通知内容通过消息模板（支持 `{userName}`, `{actualPerformance}` 等占位符）动态生成，提供个性化信息。
- **提醒间隔与生命周期**:
    - 用户通过前端"我知道了"或"全部处理"操作将通知标记为 `DISMISSED`状态。
    - 系统会根据规则定义的 `default_show_again_after_days` 计算 `next_prompt_date`（下次提醒日期）。
    - 如果到了下次提醒日期，系统会重新检查该员工是否仍满足通知条件，若满足则再次生成通知。
- **通知生成机制**:
    - **定时任务**: `NotificationGenerationTask` 类通过 `@Scheduled` 注解配置，每日凌晨2点自动扫描符合条件的员工并为其生成或更新通知。
    - **登录时触发**: 员工通过 `/auth/employee/login` 成功登录后，会异步触发一次针对该员工的通知生成检查 (`notificationService.generateNotificationsForEmployeeAsync(employee)`)。

## 数据库设计

系统包含以下主要数据表：

- **employee**：员工信息表，存储员工基本信息、账号信息 (包含 `phone` 字段 - VARCHAR, 唯一, 非空；`email` 字段允许NULL，有唯一键
  `uk_email`)
- **department**：部门信息表，存储部门结构和信息
- **position**：职位信息表，存储各职位定义和薪资范围
- **position_department**：职位与部门的关联表
- **salary**：薪资信息表，存储员工薪资发放记录
- **performance**：业绩信息表，存储员工业绩考核记录
- **client**：客户信息表，存储客户基本信息
- **petty_cash**：备用金信息表，存储备用金使用记录
- **department_expense**：部门日常开销表，存储部门发生的各项日常支出。
- **employee_other_expense**：员工其他费用表，存储员工个人产生的除工资外的其他费用。
- **notification_rules**：通知规则定义表，存储通知的触发条件、消息模板、目标用户等。
- **employee_notifications**：员工通知记录表，存储已生成并发送给员工的通知实例。

*注：`employee_other_expense` 表的Java实体类 `EmployeeOtherExpense.java` 已被用户删除。然而，该表定义仍存在于 `init.sql` 中，并且其数据可能通过 `EmployeeOtherExpenseMapper` 在 `NotificationServiceImpl` 的业绩相关通知条件判断中被使用。此功能点的最终状态（保留或彻底移除）需用户进一步明确。*

数据库设计遵循第三范式，使用外键约束确保数据完整性和一致性。

## API接口说明

### 认证接口

- `POST /auth/login`：管理员登录 (基于手机号和密码)
- `POST /auth/employee/login`：员工登录 (基于手机号和密码)
- `POST /auth/register`：管理员注册
- `GET /auth/info`：获取当前登录用户信息
- `PUT /auth/profile`：更新个人资料 (请求体包含 `phone` (必填,唯一), `email` (可选), 不再处理 `idCard`)
- `POST /auth/logout`：退出登录

### 员工管理

- `GET /employee/list`：获取所有员工列表
- `GET /employee/page`：分页获取员工列表
- `GET /employee/{id}`：根据ID获取员工详情
- `GET /employee/department/{departmentId}`：获取指定部门的员工
- `POST /employee/add`：添加新员工
- `PUT /employee/update`：更新员工信息
- `DELETE /employee/{id}`：删除员工
- `PUT /employee/status`：更新员工状态

### 部门管理

- `GET /department/list`：获取所有部门列表
- `GET /department/page`：分页获取部门列表
- `GET /department/{id}`：根据ID获取部门详情
- `POST /department/add`：添加新部门
- `PUT /department/update`：更新部门信息
- `DELETE /department/{id}`：删除部门
- `PUT /department/status`：更新部门状态
- `GET /department/search`：搜索部门
- `GET /department/current`：获取当前用户的部门信息
- `GET /department/{departmentId}/sub`：获取子部门
- `GET /department/responsible-tree`：获取部门树结构

### 职位管理

- `GET /position/list`：获取所有职位列表
- `GET /position/page`：分页获取职位列表
- `GET /position/{id}`：根据ID获取职位详情
- `POST /position/add`：添加新职位
- `PUT /position/update`：更新职位信息
- `DELETE /position/{id}`：删除职位
- `GET /position/department/{departmentId}`：获取指定部门的职位

### 薪资管理

- `GET /salary/list`：获取所有薪资记录
- `GET /salary/page`：分页获取薪资记录
- `GET /salary/{id}`：根据ID获取薪资详情
- `POST /salary/add`：添加薪资记录
- `PUT /salary/update`：更新薪资记录
- `DELETE /salary/{id}`：删除薪资记录
- `GET /salary/employee/{employeeId}`：获取指定员工的薪资记录
- `GET /salary/statistics`：获取薪资统计数据

### 业绩管理

- `GET /performance/list`：获取所有业绩记录
- `GET /performance/page`：分页获取业绩记录 (响应中可能包含平均部门开销、员工费用、预计/实际盈亏等新增财务字段)
- `GET /performance/{id}`：根据ID获取业绩详情
- `POST /performance/add`：添加业绩记录
- `PUT /performance/update`：更新业绩记录
- `DELETE /performance/{id}`：删除业绩记录
- `GET /performance/employee/{employeeId}`：获取指定员工的业绩记录 (响应中可能包含平均部门开销、员工费用、预计/实际盈亏等新增财务字段)
- `GET /performance/department/{departmentId}`：获取指定部门的业绩记录
- `GET /performance/departments/list`：获取多个部门的业绩记录列表 (响应中可能包含平均部门开销、员工费用、预计/实际盈亏等新增财务字段)

### 客户管理

- `GET /client/list`：获取所有客户列表
- `GET /client/page`：分页获取客户列表
- `GET /client/{id}`：根据ID获取客户详情
- `POST /client/add`：添加新客户
- `PUT /client/update`：更新客户信息
- `DELETE /client/{id}`：逻辑删除客户
- `PUT /client/status`：更新客户状态

### 备用金管理

- `GET /pettycash/list`：获取所有备用金记录
- `GET /pettycash/page`：分页获取备用金记录
- `GET /pettycash/{id}`：根据ID获取备用金详情
- `POST /pettycash/add`：添加备用金记录
- `PUT /pettycash/update`：更新备用金记录
- `DELETE /pettycash/{id}`：逻辑删除备用金记录
- `PUT /pettycash/status`：更新备用金状态
- `GET /pettycash/employee/{employeeId}`：获取指定员工的备用金记录
- `GET /pettycash/department/{departmentId}`：获取指定部门的备用金记录

### 仪表盘

- `GET /dashboard/stats`：获取仪表盘统计数据

### 通知接口 (`/notifications`)

基础路径：`/notifications` (实际请求路径可能不含`/api`前缀，取决于前端代理配置及`request.js`中的设定，后端Controller统一配置为`/notifications`)

- **`GET /bell`**: 获取当前登录用户在通知中心（铃铛）应显示的通知列表。
    - 成功响应: `Result<List<EmployeeNotification>>`
- **`POST /{notificationId}/dismiss`**: 用户处理（例如点击"我知道了"）单个通知。将通知状态更新为`DISMISSED`并计算下次提醒日期。
    - 路径参数: `notificationId` - 被处理的通知ID。
    - 成功响应: `Result<String>` (例如, "通知已处理")
- **`POST /{notificationId}/read-in-bell`**: 用户在通知中心将某条通知标记为已读（仅更新前端状态，不改变核心生命周期）。
    - 路径参数: `notificationId` - 被标记的通知ID。
    - 成功响应: `Result<String>` (例如, "通知已标记为已读")
- **`POST /dismiss-multiple`**: 用户批量处理通知（例如点击"全部处理"）。
    - 请求体: `{"ids": [Long, Long, ...]}` - 需要处理的通知ID列表。
    - 成功响应: `Result<String>` (例如, "所有选中通知已处理")
- **`GET /popup`**: 获取当前登录用户需要弹窗显示的通知 (当前实现主要依赖 `/bell` 接口配合前端逻辑决定是否弹窗)。
    - 成功响应: `Result<List<EmployeeNotification>>`

## 系统特点

1. **完整的业务流程**：系统覆盖了公司管理的核心业务流程，提供端到端的管理能力
2. **灵活的权限控制**：基于JWT和角色的声明式权限管理，通过 `JwtAuthInterceptor` 拦截器实现，确保数据安全和操作授权。
3. **数据一致性保障**：使用数据库约束和业务逻辑验证确保数据完整性
4. **良好的代码组织**：遵循三层架构，代码结构清晰，便于维护和扩展
5. **完善的错误处理**：通过 `GlobalExceptionHandler.java` 实现全局异常捕获，统一处理包括参数校验/绑定、业务异常 (
   `BusinessException`，能够将具体的业务校验错误如手机号/邮箱已存在等信息返回给前端)
   、数据完整性冲突（如唯一约束、外键关联错误并提供友好提示）、请求方法错误等多种常见异常，返回统一的错误响应结构，提升用户体验。
6. **详细的API文档**：控制器中包含详细的API文档注释，方便前端集成
7. **标准化的API设计**：所有API接口遵循统一的请求与响应结构 (`Result.java`, `PageResult.java`)，便于前后端协作与维护。

## 运行说明

1. 确保安装了JDK 17+、Maven和MySQL 8.0+
2. 创建数据库：`company_management_system`
3. 修改`application.yml`中的数据库连接信息
4. 执行`src/main/resources/sql/init.sql`初始化数据库
5. 运行`mvn spring-boot:run`启动应用
6. 访问`http://localhost:8080`测试API接口

## 开发计划

- [X] 集成消息通知系统 (核心功能已实现，支持规则配置、条件判断、定时与登录时生成、前端展示与交互)
- [ ] 优化通知系统，例如支持更复杂的规则条件、用户自定义订阅、消息推送等
- [ ] 增加数据导出功能 (例如Excel)
- [ ] 增强报表和数据可视化能力
- [ ] 持续优化性能和安全性
- [ ] (待用户确认) 清理或恢复 `employee_other_expense` 相关功能。
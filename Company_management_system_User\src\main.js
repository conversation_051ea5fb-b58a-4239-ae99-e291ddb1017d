import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import './style.css'

const app = createApp(App)

// 使用路由
app.use(router)
// 使用状态管理
app.use(createPinia())
// 使用Element Plus，设置为中文
app.use(ElementPlus, { locale: zhCn })

app.mount('#app')

package org.example.company_management.config;

import org.example.company_management.interceptor.JwtAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    @Autowired
    private JwtAuthInterceptor jwtAuthInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtAuthInterceptor)
                // 拦截所有请求
                .addPathPatterns("/**")
                // 排除登录和注册相关接口
                .excludePathPatterns(
                        "/auth/login",
                        "/auth/employee/login",   // 员工客户端登录接口
                        "/auth/code",        // 登录获取验证码接口
                        "/auth/register",    // 注册接口
                        "/auth/register/code", // 注册获取验证码接口
                        "/error"
                );
    }
} 
package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.NotificationRule;

import java.util.List;

@Mapper
public interface NotificationRuleMapper {
    /**
     * 获取所有激活的通知规则
     * @return 激活的通知规则列表
     */
    List<NotificationRule> getAllActiveRules();

    /**
     * 根据职位名称获取激活的规则
     * @param positionName 职位名称
     * @return 对应职位激活的通知规则列表
     */
    List<NotificationRule> getActiveRulesByPositionName(@Param("positionName") String positionName);

    /**
     * 获取所有职位激活的通知规则
     * @return 所有职位激活的通知规则列表
     */
    List<NotificationRule> getActiveRulesForAllPositions();
} 
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.SalaryMapper">

    <!-- 结果映射 -->
    <resultMap id="SalaryResultMap" type="org.example.company_management.entity.Salary">
        <id property="id" column="id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="date" column="date"/>
        <result property="basicSalary" column="basic_salary"/>
        <result property="performanceBonus" column="performance_bonus"/>
        <result property="fullAttendanceBonus" column="full_attendance_bonus"/>
        <result property="businessOperationBonus" column="business_operation_bonus"/>
        <result property="sumSalary" column="sum_salary"/>
        <result property="leaveDeduction" column="leave_deduction"/>
        <result property="deduction" column="deduction"/>
        <result property="lateDeduction" column="late_deduction"/>
        <result property="socialSecurityPersonal" column="social_security_personal"/>
        <result property="providentFund" column="provident_fund"/>
        <result property="tax" column="tax"/>
        <result property="waterElectricityFee" column="water_electricity_fee"/>
        <result property="actualSalary" column="actual_salary"/>
        <result property="reimbursement" column="reimbursement"/>
        <result property="privateAccount" column="private_account"/>
        <result property="remark" column="remark"/>
        <result property="totalSalary" column="total_salary"/>
        <!-- 非数据库字段 -->
        <result property="employeeName" column="employee_name"/>
        <result property="department" column="department_name"/>
        <result property="position" column="position_name"/>
    </resultMap>

    <sql id="selectSalaryColumns">
        s.id, s.employee_id, s.date, s.basic_salary, s.performance_bonus, 
        s.full_attendance_bonus, s.business_operation_bonus, s.sum_salary,
        s.leave_deduction, s.deduction, s.late_deduction, s.social_security_personal,
        s.provident_fund, s.tax, s.water_electricity_fee, s.actual_salary,
        s.reimbursement, s.private_account, s.remark, s.total_salary,
        e.name AS employee_name, d.department_name, p.position_name
    </sql>

    <!-- 查询所有工资记录 -->
    <select id="selectAll" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        LEFT JOIN 
            employee e ON s.employee_id = e.employee_id
        LEFT JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        ORDER BY 
            s.date DESC, e.name ASC
    </select>

    <!-- 根据员工ID查询工资记录 -->
    <select id="selectByEmployeeId" parameterType="java.lang.Integer" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        LEFT JOIN 
            employee e ON s.employee_id = e.employee_id
        LEFT JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        WHERE 
            s.employee_id = #{employeeId}
        ORDER BY 
            s.date DESC
    </select>

    <!-- 根据员工ID和日期查询工资记录 -->
    <select id="selectByEmployeeIdAndDate" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        LEFT JOIN 
            employee e ON s.employee_id = e.employee_id
        LEFT JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        WHERE 
            s.employee_id = #{employeeId} AND s.date = #{date}
    </select>

    <!-- 根据ID查询工资记录 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        LEFT JOIN 
            employee e ON s.employee_id = e.employee_id
        LEFT JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        WHERE 
            s.id = #{id}
    </select>

    <!-- 分页查询工资记录 -->
    <select id="selectPage" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        LEFT JOIN 
            employee e ON s.employee_id = e.employee_id
        LEFT JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        <where>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="yearMonth != null and yearMonth != ''">
                AND s.date = #{yearMonth}
            </if>
        </where>
        ORDER BY 
            s.date DESC, e.name ASC
    </select>

    <sql id="insertSalaryColumns">
        employee_id, date, basic_salary, performance_bonus, 
        full_attendance_bonus, business_operation_bonus, sum_salary,
        leave_deduction, deduction, late_deduction, social_security_personal,
        provident_fund, tax, water_electricity_fee, actual_salary,
        reimbursement, private_account, remark, total_salary
    </sql>

    <sql id="insertSalaryValues">
        #{employeeId}, #{date}, #{basicSalary}, #{performanceBonus}, 
        #{fullAttendanceBonus}, #{businessOperationBonus}, #{sumSalary},
        #{leaveDeduction}, #{deduction}, #{lateDeduction}, #{socialSecurityPersonal},
        #{providentFund}, #{tax}, #{waterElectricityFee}, #{actualSalary},
        #{reimbursement}, #{privateAccount}, #{remark}, #{totalSalary}
    </sql>

    <!-- 新增工资记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Salary" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO salary (
            <include refid="insertSalaryColumns"/>
        ) VALUES (
            <include refid="insertSalaryValues"/>
        )
    </insert>

    <!-- 更新工资记录 -->
    <update id="update" parameterType="org.example.company_management.entity.Salary">
        UPDATE salary
        SET 
            employee_id = #{employeeId},
            date = #{date},
            basic_salary = #{basicSalary},
            performance_bonus = #{performanceBonus},
            full_attendance_bonus = #{fullAttendanceBonus},
            business_operation_bonus = #{businessOperationBonus},
            sum_salary = #{sumSalary},
            leave_deduction = #{leaveDeduction},
            deduction = #{deduction},
            late_deduction = #{lateDeduction},
            social_security_personal = #{socialSecurityPersonal},
            provident_fund = #{providentFund},
            tax = #{tax},
            water_electricity_fee = #{waterElectricityFee},
            actual_salary = #{actualSalary},
            reimbursement = #{reimbursement},
            private_account = #{privateAccount},
            remark = #{remark},
            total_salary = #{totalSalary}
        WHERE 
            id = #{id}
    </update>

    <!-- 删除工资记录 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM salary WHERE id = #{id}
    </delete>

    <!-- 批量删除工资记录 -->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM salary WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询多个部门工资统计 -->
    <select id="selectMultiDepartmentSalary" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        JOIN 
            employee e ON s.employee_id = e.employee_id
        JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        <where>
            d.department_id IN
            <foreach collection="departmentIds" item="departmentId" open="(" separator="," close=")">
                #{departmentId}
            </foreach>
            <!-- 检查日期参数 -->
            <if test="date != null and date != ''">
                <choose>
                    <!-- 日期范围查询：检查是否以'range_'开头 -->
                    <when test="date.toString().startsWith('range_')">
                        <!-- 分割日期字符串 -->
                        <bind name="dateStr" value="date.toString().substring(6)"/>
                        <bind name="dateRange" value="dateStr.split('_')"/>
                        AND s.date BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                    </when>
                    <!-- 单一日期查询 -->
                    <otherwise>
                        AND s.date = #{date}
                    </otherwise>
                </choose>
            </if>
            <!-- 新增：根据员工姓名模糊筛选 -->
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
        </where>
        ORDER BY 
            s.date DESC, e.employee_id ASC
    </select>
    
    <!-- 查询部门员工工资详情 -->
    <select id="selectEmployeeSalariesByDepartment" resultMap="SalaryResultMap">
        SELECT 
            <include refid="selectSalaryColumns"/>
        FROM 
            salary s
        JOIN 
            employee e ON s.employee_id = e.employee_id
        JOIN 
            department d ON e.department_id = d.department_id
        LEFT JOIN 
            position p ON e.position_id = p.position_id
        WHERE 
            d.department_id = #{departmentId}
            <if test="date != null and date != ''">
                AND s.date = #{date}
            </if>
        ORDER BY 
            e.name ASC
    </select>

    <!-- 批量新增工资记录 -->
    <insert id="batchInsertSalaries" parameterType="java.util.List">
        INSERT INTO salary (
            <include refid="insertSalaryColumns"/>
        )
        VALUES
        <foreach collection="salaryList" item="salary" separator=",">
            (
            <include refid="insertSalaryValues"/>
            )
        </foreach>
    </insert>

    <resultMap id="BaseResultMap" type="org.example.company_management.entity.Salary">
        <id column="id" property="id"/>
        <result column="employee_id" property="employeeId"/>
        <result column="date" property="date"/>
        <result column="basic_salary" property="basicSalary"/>
        <result column="performance_bonus" property="performanceBonus"/>
        <result column="full_attendance_bonus" property="fullAttendanceBonus"/>
        <result column="business_operation_bonus" property="businessOperationBonus"/>
        <result column="sum_salary" property="sumSalary"/>
        <result column="leave_deduction" property="leaveDeduction"/>
        <result column="deduction" property="deduction"/>
        <result column="late_deduction" property="lateDeduction"/>
        <result column="social_security_personal" property="socialSecurityPersonal"/>
        <result column="provident_fund" property="providentFund"/>
        <result column="tax" property="tax"/>
        <result column="water_electricity_fee" property="waterElectricityFee"/>
        <result column="actual_salary" property="actualSalary"/>
        <result column="reimbursement" property="reimbursement"/>
        <result column="private_account" property="privateAccount"/>
        <result column="total_salary" property="totalSalary"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, employee_id, date, basic_salary, performance_bonus, full_attendance_bonus, 
        business_operation_bonus, sum_salary, leave_deduction, deduction, late_deduction, 
        social_security_personal, provident_fund, tax, water_electricity_fee, actual_salary, 
        reimbursement, private_account, total_salary, remark
    </sql>

    <select id="findByEmployeeIdAndDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM salary
        WHERE employee_id = #{employeeId} AND date = #{date}
        LIMIT 1;
    </select>

</mapper> 
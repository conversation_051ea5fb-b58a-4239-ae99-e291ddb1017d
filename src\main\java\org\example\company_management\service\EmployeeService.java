package org.example.company_management.service;

import org.example.company_management.entity.Employee;
import org.example.company_management.utils.PageResult;

import java.util.List;

/**
 * 员工服务接口
 */
public interface EmployeeService {
    /**
     * 查询所有员工
     * @return 员工列表
     */
    List<Employee> list();
    
    /**
     * 分页查询员工
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 员工姓名（可选）
     * @param departmentId 部门ID（可选）
     * @return 分页结果
     */
    PageResult<Employee> pageList(int pageNum, int pageSize, String name, Integer departmentId);
    
    /**
     * 根据ID查询员工
     * @param id 员工ID
     * @return 员工信息
     */
    Employee getById(Integer id);
    
    /**
     * 根据部门ID查询员工
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<Employee> getByDepartmentId(Integer departmentId);
    
    /**
     * 新增员工
     * @param employee 员工信息
     */
    void add(Employee employee);
    
    /**
     * 修改员工
     * @param employee 员工信息
     */
    void update(Employee employee);
    
    /**
     * 删除员工
     * @param id 员工ID
     */
    void delete(Integer id);
    
    /**
     * 更新员工状态
     * @param id 员工ID
     * @param status 状态
     */
    void updateStatus(Integer id, String status);
    
    /**
     * 批量更新员工部门
     * @param departmentId 原部门ID
     * @param newDepartmentId 新部门ID，如果为null则设置为未分配状态
     * @return 更新的员工数量
     */
    int updateEmployeeDepartment(Integer departmentId, Integer newDepartmentId);
    
    /**
     * 根据姓名查询员工
     * @param name 员工姓名
     * @return 员工列表
     */
    List<Employee> findByName(String name);
    
    /**
     * 分页查询部门下的员工
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param departmentId 部门ID
     * @param name 员工姓名（可选）
     * @return 分页结果
     */
    PageResult<Employee> pageListByDepartmentId(int pageNum, int pageSize, Integer departmentId, String name);
} 
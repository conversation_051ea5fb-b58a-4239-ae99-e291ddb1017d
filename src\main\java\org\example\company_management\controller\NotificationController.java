package org.example.company_management.controller;

import org.example.company_management.entity.EmployeeNotification;
import org.example.company_management.service.NotificationService;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.example.company_management.entity.Employee;
import org.example.company_management.mapper.EmployeeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/notifications")
public class NotificationController {

    private static final Logger log = LoggerFactory.getLogger(NotificationController.class);
    private final NotificationService notificationService;
    private final EmployeeMapper employeeMapper;

    @Autowired
    public NotificationController(NotificationService notificationService, EmployeeMapper employeeMapper) {
        this.notificationService = notificationService;
        this.employeeMapper = employeeMapper;
    }

    /**
     * 获取当前登录的员工ID
     * @return 当前登录的员工ID，如果未登录则返回null
     */
    private Integer getCurrentEmployeeId() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("employeeId")) {
            Object idObject = employeeInfo.get("employeeId");
            if (idObject instanceof Integer) {
                return (Integer) idObject;
            } else if (idObject instanceof Number) {
                return ((Number) idObject).intValue();
            }
        }
        return null;
    }

    @GetMapping("/bell")
    public Result<List<EmployeeNotification>> getBellNotifications() {
        Integer employeeId = getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取用户信息，请重新登录");
        }

        List<EmployeeNotification> notifications = notificationService.getBellNotificationsForEmployee(employeeId);
        return Result.success(notifications);
    }

    @PostMapping("/trigger-analysis")
    public Result<?> triggerNotificationAnalysisForCurrentUser() {
        Integer employeeId = getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取用户信息，请重新登录后再尝试触发分析。");
        }

        try {
            Employee currentEmployee = employeeMapper.findByIdWithPositionName(employeeId);
            if (currentEmployee != null && "Active".equalsIgnoreCase(currentEmployee.getStatus())) {
                notificationService.generateNotificationsForEmployeeAsync(currentEmployee);
                log.info("通过 /trigger-analysis 接口为员工 {} (ID: {}) 异步触发通知检查与生成。", currentEmployee.getName(), employeeId);
                return Result.success("通知分析任务已成功触发");
            } else if (currentEmployee == null) {
                log.warn("通过 /trigger-analysis 接口未找到ID为 {} 的员工信息，无法触发通知生成。", employeeId);
                return Result.error("未找到员工信息，无法触发通知生成");
            } else {
                log.info("通过 /trigger-analysis 接口发现员工 {} (ID: {}) 状态为 {}，不触发通知生成。", currentEmployee.getName(), employeeId, currentEmployee.getStatus());
                return Result.success("员工状态非Active，不触发通知生成");
            }
        } catch (Exception e) {
            log.error("通过 /trigger-analysis 接口为员工ID {} 异步触发通知生成时发生错误: {}", employeeId, e.getMessage(), e);
            // 即使失败，也返回一个通用错误，避免泄露过多信息
            return Result.error("触发通知分析任务时发生内部错误");
        }
    }

    @PostMapping("/{notificationId}/dismiss")
    public Result<?> dismissNotification(@PathVariable Long notificationId) {
        Integer employeeId = getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取用户信息，请重新登录");
        }
        return notificationService.dismissNotification(notificationId, employeeId);
    }

    @PostMapping("/{notificationId}/read-in-bell")
    public Result<?> markAsReadInBell(@PathVariable Long notificationId) {
        Integer employeeId = getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取用户信息，请重新登录");
        }
        return notificationService.markAsReadInBell(notificationId, employeeId);
    }

    @PostMapping("/dismiss-multiple")
    public Result<?> dismissMultipleNotifications(@RequestBody Map<String, Object> payload) {
        Integer employeeId = getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取用户信息，请重新登录");
        }

        Object idsObj = payload.get("ids");
        if (idsObj == null) {
            return Result.error("请提供需要处理的通知ID");
        }

        List<Long> ids = new ArrayList<>();
        try {
            if (idsObj instanceof List<?>) {
                List<?> rawIds = (List<?>) idsObj;
                for (Object id : rawIds) {
                    if (id instanceof Number) {
                        ids.add(((Number) id).longValue());
                    } else if (id instanceof String) {
                        ids.add(Long.parseLong((String) id));
                    } else {
                        log.warn("无法转换的ID类型: {}", id.getClass().getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析通知ID列表时发生错误: {}", e.getMessage());
            return Result.error("通知ID格式错误");
        }

        if (ids.isEmpty()) {
            return Result.error("没有有效的通知ID");
        }

        return notificationService.dismissMultipleNotifications(ids, employeeId);
    }

    @GetMapping("/popup")
    public Result<List<EmployeeNotification>> getPopupNotifications() {
        Integer employeeId = getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取用户信息，请重新登录");
        }
        List<EmployeeNotification> notifications = notificationService.getPopupNotificationsForEmployee(employeeId);
        //  Currently, service returns empty list. If it were to return data:
        // if (notifications.isEmpty()) {
        //     return Result.success(Collections.emptyList(), "暂无弹窗通知");
        // }
        return Result.success(notifications);
    }
} 
package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Department;
import java.util.List;

/**
 * 部门Mapper接口
 */
@Mapper
public interface DepartmentMapper {
    /**
     * 查询所有部门
     * @return 部门列表
     */
    List<Department> selectAll();
    
    /**
     * 分页查询部门
     * @param offset 起始位置
     * @param pageSize 每页大小
     * @return 部门列表
     */
    List<Department> selectByPage(@Param("offset") int offset, @Param("pageSize") int pageSize);
    
    /**
     * 获取部门总数
     * @return 部门总数
     */
    int countTotal();
    
    /**
     * 根据ID查询部门
     * @param departmentId 部门ID
     * @return 部门信息
     */
    Department selectById(Integer departmentId);
    
    /**
     * 新增部门
     * @param department 部门信息
     * @return 影响行数
     */
    int insert(Department department);
    
    /**
     * 修改部门
     * @param department 部门信息
     * @return 影响行数
     */
    int update(Department department);
    
    /**
     * 删除部门
     * @param departmentId 部门ID
     * @return 影响行数
     */
    int deleteById(Integer departmentId);
    
    /**
     * 分页查询部门(带条件)
     * @param offset 起始位置
     * @param pageSize 每页大小
     * @param departmentName 部门名称(可选，用于模糊匹配)
     * @param leaderId 部门负责人ID(可选)
     * @param leaderName 部门负责人姓名(可选，用于模糊匹配)
     * @param parentDepartmentId 父部门ID(可选)
     * @return 部门列表
     */
    List<Department> selectByPageAndCondition(
        @Param("offset") int offset, 
        @Param("pageSize") int pageSize, 
        @Param("departmentName") String departmentName, 
        @Param("leaderId") Integer leaderId,
        @Param("leaderName") String leaderName,
        @Param("parentDepartmentId") Integer parentDepartmentId
    );
    
    /**
     * 获取符合条件的部门总数
     * @param departmentName 部门名称(可选，用于模糊匹配)
     * @param leaderId 部门负责人ID(可选)
     * @param leaderName 部门负责人姓名(可选，用于模糊匹配)
     * @param parentDepartmentId 父部门ID(可选)
     * @return 部门总数
     */
    int countTotalByCondition(
        @Param("departmentName") String departmentName, 
        @Param("leaderId") Integer leaderId,
        @Param("leaderName") String leaderName,
        @Param("parentDepartmentId") Integer parentDepartmentId
    );
    
    /**
     * 根据关键词搜索部门(模糊匹配部门名称)
     * @param keyword 搜索关键词
     * @return 部门列表
     */
    List<Department> selectByKeyword(@Param("keyword") String keyword);
    
    /**
     * 根据父部门ID查询子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> selectByParentId(@Param("parentId") Integer parentId);
    
    /**
     * 根据部门负责人ID查询部门列表
     * @param leaderId 部门负责人ID
     * @return 该负责人负责的所有部门列表
     */
    List<Department> selectByLeaderId(@Param("leaderId") Integer leaderId);
} 
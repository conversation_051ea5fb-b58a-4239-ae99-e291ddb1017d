package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationRule {
    private Integer ruleId;
    private String ruleName;
    private String description;
    private String targetPositionName;
    private String conditionType;
    private String messageTemplateCn;
    private Integer defaultShowAgainAfterDays;
    private Integer priority;
    private Boolean isActive;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { UserFilled, Lock } from '@element-plus/icons-vue';
import { ElMessage, ElNotification } from 'element-plus';
import { employeeLogin } from '@/api/auth';
import { useAuthStore } from '@/stores/token';
import { triggerNotificationAnalysis } from '@/api/notification';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// 登录表单
const loginForm = reactive({
    phone: '',
    password: '',
});

// 表单验证状态
const formTouched = reactive({
    phone: false,
    password: false,
    form: false, // 整个表单是否被提交过
});

// 登录状态
const loading = ref(false);
const formValid = ref(false);

// 表单验证规则
const validatePhone = (phone) => {
    return true; // No specific format validation
};

const validatePassword = (password) => {
    // 密码至少8位，包含大小写字母和数字
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);

    return password.length >= 8 && hasUpperCase && hasLowerCase && hasNumber;
};

// 验证表单
const validateForm = () => {
    // 标记整个表单为已提交状态
    formTouched.form = true;
    formTouched.phone = true;
    formTouched.password = true;

    const isPhoneValid = validatePhone(loginForm.phone);
    const isPasswordValid = validatePassword(loginForm.password);

    formValid.value = isPhoneValid && isPasswordValid;
    return formValid.value;
};

// 监听输入变化进行实时验证
const validatePhoneInput = () => {
    formTouched.phone = true;
    if (!loginForm.phone) return '请输入手机号';
    return ''; // If not empty, considered valid
};

const validatePasswordInput = () => {
    formTouched.password = true;
    if (!loginForm.password) return '请输入密码';
    if (!validatePassword(loginForm.password)) {
        return '密码必须至少8位，包含大小写字母和数字';
    }
    return '';
};

// 获取字段错误信息，但只在字段被触碰后显示
const getPhoneError = () => (formTouched.phone ? validatePhoneInput() : '');
const getPasswordError = () =>
    formTouched.password ? validatePasswordInput() : '';

// 提交登录表单
const handleLogin = async () => {
    if (!validateForm()) {
        ElMessage.error('请正确填写所有必填项');
        return;
    }

    loading.value = true;
    authStore.setLoading(true);
    authStore.setError(null);

    try {
        // 调用登录API
        const response = await employeeLogin(loginForm);

        if (response.code === 200) {
            // 设置token
            authStore.setToken(response.data);

            // 触发通知分析 API 调用
            triggerNotificationAnalysis().then(() => {
                //console.log('Login.vue: Notification analysis triggered successfully.');
            }).catch(error => {
                //console.error('Login.vue: Failed to trigger notification analysis:', error);
                // 通常这里不需要对用户显示错误，因为这是后台任务的启动
            });

            // 显示登录成功消息
            ElMessage({
                message: '登录成功，欢迎回来！',
                type: 'success',
                duration: 3000,
                showClose: true,
            });

            // 显示更醒目的通知
            ElNotification({
                title: '登录成功',
                message: `欢迎回到中航信息系统！`,
                type: 'success',
                duration: 4000,
                position: 'top-right',
            });

            // 延迟导航，让用户有时间看到成功消息
            setTimeout(() => {
                // 跳转到主页或重定向页面
                const redirectPath = route.query.redirect || '/dashboard';
                router.replace({
                    path: redirectPath,
                    query: { fromLogin: 'true' },
                });
            }, 1000);
        } else {
            ElMessage.error(response.message || '登录失败，请检查账号密码');
        }
    } catch (error) {
        console.error('登录错误:', error);
        ElMessage.error(error.message || '登录失败，请检查网络连接或账号密码');
    } finally {
        loading.value = false;
        authStore.setLoading(false);
    }
};

// 检查是否已经登录
onMounted(() => {
    if (authStore.isAuthenticated) {
        router.push('/dashboard');
    }
});
</script>

<template>
    <div class="login-container">
        <!-- 背景动画元素 -->
        <div class="bg-animation">
            <div class="cube"></div>
            <div class="cube"></div>
            <div class="cube"></div>
            <div class="cube"></div>
            <div class="cube"></div>
        </div>

        <div class="login-box">
            <div class="logo">
                <svg
                    viewBox="0 0 24 24"
                    width="48"
                    height="48"
                    fill="currentColor"
                >
                    <path d="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z"></path>
                </svg>
            </div>
            <h1 class="title">中航信息平台</h1>

            <form
                @submit.prevent="handleLogin"
                class="login-form"
            >
                <div class="input-group">
                    <el-input
                        v-model="loginForm.phone"
                        placeholder="手机号"
                        :prefix-icon="UserFilled"
                        size="large"
                        @input="validatePhoneInput"
                        @blur="formTouched.phone = true"
                        clearable
                    ></el-input>
                    <div
                        class="error-message"
                        v-if="getPhoneError()"
                    >
                        {{ getPhoneError() }}
                    </div>
                </div>

                <div class="input-group">
                    <el-input
                        v-model="loginForm.password"
                        type="password"
                        placeholder="密码"
                        :prefix-icon="Lock"
                        show-password
                        size="large"
                        @input="validatePasswordInput"
                        @blur="formTouched.password = true"
                        clearable
                    ></el-input>
                    <div
                        class="error-message"
                        v-if="getPasswordError()"
                    >
                        {{ getPasswordError() }}
                    </div>
                </div>

                <el-button
                    type="primary"
                    native-type="submit"
                    class="login-button"
                    size="large"
                    :loading="loading"
                >登录</el-button>
            </form>

            <div class="footer-text">
                Copyright © 2025 中航信息系统
            </div>
        </div>
    </div>
</template>

<style scoped>
.login-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #1a2a42;
    background-image: linear-gradient(135deg, #1a2a42 0%, #0c1424 100%);
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* 背景动画 */
.bg-animation {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.cube {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.05);
    animation: cube 15s linear infinite;
    border-radius: 8px;
}

.cube:nth-child(1) {
    top: 10%;
    left: 45%;
    animation-delay: 0s;
}

.cube:nth-child(2) {
    top: 70%;
    left: 25%;
    animation-delay: -2s;
    width: 100px;
    height: 100px;
}

.cube:nth-child(3) {
    top: 40%;
    left: 80%;
    animation-delay: -4s;
    width: 60px;
    height: 60px;
}

.cube:nth-child(4) {
    top: 80%;
    left: 70%;
    animation-delay: -6s;
    width: 120px;
    height: 120px;
}

.cube:nth-child(5) {
    top: 30%;
    left: 10%;
    animation-delay: -8s;
    width: 90px;
    height: 90px;
}

@keyframes cube {
    0% {
        transform: scale(0) rotate(0deg) translate(-50%, -50%);
        opacity: 1;
    }
    100% {
        transform: scale(1.5) rotate(360deg) translate(-50%, -50%);
        opacity: 0;
    }
}

.login-box {
    width: 90%;
    max-width: 480px;
    padding: 50px 30px;
    background-color: rgba(255, 255, 255, 0.98);
    border-radius: 12px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.login-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
}

.logo {
    color: #1e3a8a;
    margin-bottom: 20px;
}

.logo svg {
    width: 60px;
    height: 60px;
}

.title {
    font-size: 28px;
    color: #333;
    margin-bottom: 40px;
    font-weight: 600;
}

.input-group {
    margin-bottom: 25px;
    text-align: left;
}

.error-message {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
    line-height: 1.2;
    padding-left: 12px;
}

:deep(.el-input__inner) {
    height: 50px;
    font-size: 16px;
    border-radius: 8px;
}

:deep(.el-input__wrapper) {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

:deep(.el-input__wrapper:hover),
:deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__icon) {
    font-size: 18px;
    line-height: 50px;
}

.login-button {
    width: 100%;
    margin-top: 15px;
    background-color: #1e3a8a;
    border-color: #1e3a8a;
    height: 50px;
    font-size: 16px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
}

.login-button:hover {
    background-color: #152c69;
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
    transform: translateY(-2px);
}

.footer-text {
    margin-top: 40px;
    font-size: 14px;
    color: #999;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    .login-box {
        padding: 40px 25px;
        max-width: 450px;
    }

    .title {
        font-size: 24px;
        margin-bottom: 30px;
    }

    .logo svg {
        width: 50px;
        height: 50px;
    }

    .footer-text {
        margin-top: 30px;
    }
}

@media screen and (max-width: 480px) {
    .login-box {
        width: 95%;
        padding: 30px 15px;
    }

    .logo svg {
        width: 45px;
        height: 45px;
    }

    .title {
        font-size: 22px;
        margin-bottom: 25px;
    }

    .input-group {
        margin-bottom: 20px;
    }

    :deep(.el-input__inner) {
        height: 45px;
    }

    .login-button {
        height: 45px;
    }

    .footer-text {
        margin-top: 25px;
        font-size: 12px;
    }
}
</style> 
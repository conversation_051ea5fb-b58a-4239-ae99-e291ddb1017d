<script setup>
import { ref, onMounted, computed } from 'vue';
import { useAuthStore } from '../stores/token';
import {
    DataAnalysis,
    Money,
    Calendar,
    Document,
    Bell,
    Connection,
    User,
    Wallet
} from '@element-plus/icons-vue';

const authStore = useAuthStore();
const user = ref(null);
const loading = ref(true);

// 获取员工信息
onMounted(async () => {
    // 用户信息现在应该由 Dashboard.vue 获取并存入 authStore
    // Home.vue 直接从 authStore 读取
    // 如果需要，可以在这里检查 authStore.user 是否已填充，并相应设置 loading
    if (authStore.user && authStore.user.name) { // 检查 authStore.user 是否有效
        user.value = authStore.user; // 可选：如果模板中不直接用 authStore.user
        loading.value = false;
    } else {
        // Dashboard.vue 应该已经加载了用户信息
        // 但作为后备或表示状态，可以暂时保留 loading
        // 或者监听 authStore.user 的变化来更新 user.value 和 loading
        // 为了简化，这里假设 Dashboard.vue 会处理好初始加载
        console.warn("Home.vue: authStore.user not populated on mount as expected. Relying on reactivity or Dashboard's load.");
        // 如果 authStore.user 是响应式的，模板直接使用 authStore.user?.name 即可
        // loading.value 可以基于 authStore.user?.name 是否存在来设置
        loading.value = !authStore.user?.name; // 简单示例
    }
});

// 格式化日期为当前日期
const currentDate = computed(() => {
    return new Date().toLocaleDateString('zh-CN', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });
});
</script>

<template>
    <div class="home-container">
        <!-- 欢迎卡片 -->
        <el-card
            shadow="hover"
            class="welcome-card"
        >
            <div class="welcome-header">
                <h2>欢迎来到中航物流信息系统</h2>
                <p class="date">{{ currentDate }}</p>
            </div>

            <div class="user-greeting">
                <h3>您好，{{ authStore.user?.name || '用户' }}！</h3>
                <p>欢迎回到信息系统，祝您使用愉快，工作顺利。</p>
            </div>
        </el-card>

        <!-- 快捷操作 -->
        <el-card
            shadow="hover"
            class="quick-actions-card"
        >
            <template #header>
                <div class="card-header">
                    <h3>快速访问</h3>
                </div>
            </template>
            <div class="quick-actions-container">
                <div
                    class="action-item"
                    @click="$router.push('/dashboard/performance')"
                >
                    <el-icon :size="36">
                        <DataAnalysis />
                    </el-icon>
                    <span>查看业绩</span>
                </div>
                <div
                    class="action-item"
                    @click="$router.push('/dashboard/salary')"
                >
                    <el-icon :size="36">
                        <Money />
                    </el-icon>
                    <span>查看工资</span>
                </div>
                <div
                    class="action-item"
                    @click="$router.push('/dashboard/client')"
                >
                    <el-icon :size="36">
                        <User />
                    </el-icon>
                    <span>客户管理</span>
                </div>
                <div
                    class="action-item"
                    @click="$router.push('/dashboard/petty-cash')"
                >
                    <el-icon :size="36">
                        <Wallet />
                    </el-icon>
                    <span>备用金管理</span>
                </div>
            </div>
        </el-card>

        <!-- 系统公告 -->
        <el-card
            shadow="hover"
            class="notice-card"
        >
            <template #header>
                <div class="card-header">
                    <h3>系统公告</h3>
                    <el-icon :size="20">
                        <Bell />
                    </el-icon>
                </div>
            </template>
            <div class="notice-content">
                
            </div>
        </el-card>
    </div>
</template>

<style scoped>
.home-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.welcome-card {
    margin-bottom: 0;
    background: #ffffff;
    border-radius: 8px;
    padding: 10px 20px;
}

.welcome-header {
    display: flex;
    flex-direction: column;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}

.welcome-header h2 {
    margin: 0 0 5px 0;
    font-size: 24px;
    color: #1e3a8a;
    font-weight: 600;
}

.date {
    color: #888;
    font-size: 14px;
    margin: 0;
}

.user-greeting {
    margin-bottom: 15px;
}

.user-greeting h3 {
    font-size: 20px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    margin-top: 0;
}

.user-greeting p {
    margin: 0;
    color: #666;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
}

/* 快捷操作样式 */
.quick-actions-card {
    border-radius: 8px;
}

.quick-actions-container {
    display: flex;
    justify-content: space-around;
    padding: 20px 0;
    width: 100%;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 120px;
    background-color: #f5f7fa;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-item:hover {
    background-color: #ecf5ff;
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.action-item .el-icon {
    margin-bottom: 12px;
    color: #409eff;
    transition: all 0.3s ease;
}

.action-item:hover .el-icon {
    transform: scale(1.1);
}

.action-item span {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

/* 系统公告样式 */
.notice-card {
    border-radius: 8px;
}

.notice-content {
    padding: 10px 0;
}

.notice-item {
    padding: 12px 0;
}

.notice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notice-item h4 {
    margin: 0;
    font-size: 16px;
    color: #303133;
    font-weight: 500;
}

.notice-item p {
    margin: 0;
    color: #606266;
    line-height: 1.6;
    font-size: 14px;
}

.notice-time {
    color: #909399;
    font-size: 13px;
}

.el-divider {
    margin: 12px 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .quick-actions-container {
        justify-content: center;
    }

    .action-item {
        width: 100px;
        height: 100px;
    }

    .welcome-header h2 {
        font-size: 24px;
    }
}
</style> 
package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;

/**
 * 业绩实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Performance {
    /**
     * 业绩记录ID
     */
    private Integer id;
    
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 员工姓名（非数据库字段）
     */
    private String employeeName;
    
    /**
     * 部门（非数据库字段）
     */
    private String department;
    
    /**
     * 职位（非数据库字段）
     */
    private String position;
    
    /**
     * 日期（年月）
     */
    private String date;
    
    /**
     * 预估业绩
     */
    private BigDecimal estimatedPerformance;
    
    /**
     * 实际业绩
     */
    private BigDecimal actualPerformance;
    
    /**
     * 员工总工资（非数据库字段，关联自salary表）
     */
    private BigDecimal totalSalary;

    /**
     * 部门备用金总额（非数据库字段，关联自petty_cash表）
     */
    private BigDecimal totalPettyCash;

    /**
     * 部门平均费用（非数据库字段）
     */
    private BigDecimal averageDepartmentExpense;

    /**
     * 员工其他费用（非数据库字段）
     */
    private BigDecimal totalEmployeeOtherExpenses;

    /**
     * 预估月利润（非数据库字段）
     */
    private BigDecimal estimatedMonthlyProfitLoss;

    /**
     * 实际月利润（非数据库字段）
     */
    private BigDecimal actualMonthlyProfitLoss;

    private transient Integer employeeDepartmentId; // To hold department ID for calculations
} 